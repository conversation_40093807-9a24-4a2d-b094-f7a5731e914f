package com.yhl.scp.gateway.enums;

import com.yhl.platform.common.enums.CommonEnum;

/**
 * <code>ResponseStatusEnum</code>
 * <p>
 * ResponseStatusEnum
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-21 15:41:47
 */
public enum ResponseStatusEnum implements CommonEnum {

    SUCCESS("SUCCESS", "成功"),

    FAILURE("FAILURE", "失败"),

    ;

    private String code;
    private String desc;

    ResponseStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    void setDesc(String desc) {
        this.desc = desc;
    }

}