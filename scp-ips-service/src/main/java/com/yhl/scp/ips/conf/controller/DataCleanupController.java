package com.yhl.scp.ips.conf.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.conf.dto.DataCleanupDTO;
import com.yhl.scp.ips.conf.service.DataCleanupService;
import com.yhl.scp.ips.conf.vo.DataCleanupVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "数据清理配置表控制器")
@RestController
@RequestMapping("dataCleanup")
public class DataCleanupController extends BaseController {

    @Resource
    private DataCleanupService dataCleanupService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<DataCleanupVO>> page() {
        List<DataCleanupVO> dataCleanupList = dataCleanupService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<DataCleanupVO> pageInfo = new PageInfo<>(dataCleanupList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody DataCleanupDTO dataCleanupDTO) {
        return dataCleanupService.doCreate(dataCleanupDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody DataCleanupDTO dataCleanupDTO) {
        return dataCleanupService.doUpdate(dataCleanupDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        dataCleanupService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<DataCleanupVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, dataCleanupService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "手动清理")
    @PostMapping(value = "manual")
    public BaseResponse<Void> manualCleanup(@RequestBody List<String> ids) {
        return dataCleanupService.manualDataCleanup(ids);
    }
}
