package com.yhl.scp.ips.conf.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.ips.conf.domain.entity.DataCleanupDO;
import com.yhl.scp.ips.conf.infrastructure.dao.DataCleanupDao;
import com.yhl.scp.ips.conf.infrastructure.po.DataCleanupPO;
import com.yhl.scp.ips.enums.DataBaseTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 数据清理配置领域服务
 *
 * <AUTHOR>
 */
@Service
public class DataCleanupDomainService {

    @Resource
    private DataCleanupDao dataCleanupDao;

    /**
     * 数据校验
     *
     * @param dataCleanupDO 领域对象
     */
    public void validation(DataCleanupDO dataCleanupDO) {
        checkNotNull(dataCleanupDO);
        checkUniqueCode(dataCleanupDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param dataCleanupDO 领域对象
     */
    private void checkNotNull(DataCleanupDO dataCleanupDO) {
        if (StringUtils.isBlank(dataCleanupDO.getDbType())) {
            throw new BusinessException("数据库类型，不能为空");
        }
        if (DataBaseTypeEnum.MYSQL.getCode().equals(dataCleanupDO.getDbType())) {
            if (StringUtils.isBlank(dataCleanupDO.getDatabaseName())) {
                throw new BusinessException("数据库名称，不能为空");
            }
            if (StringUtils.isBlank(dataCleanupDO.getTableName())) {
                throw new BusinessException("表名称，不能为空");
            }
        } else {
            if (StringUtils.isBlank(dataCleanupDO.getDatabaseName())) {
                throw new BusinessException("集合名称，不能为空");
            }
        }
        if (StringUtils.isBlank(dataCleanupDO.getBeforeDaysReferColumn())) {
            throw new BusinessException("清理天数参考字段，不能为空");
        }
    }

    /**
     * 唯一性校验
     *
     * @param dataCleanupDO 领域对象
     */
    private void checkUniqueCode(DataCleanupDO dataCleanupDO) {
        Map<String, Object> params = new HashMap<>(4);
        params.put("dbType", dataCleanupDO.getDbType());
        params.put("databaseName", dataCleanupDO.getDatabaseName());
        if (DataBaseTypeEnum.MYSQL.getCode().equals(dataCleanupDO.getDbType())) {
            params.put("tableName", dataCleanupDO.getTableName());
        }
        if (StringUtils.isBlank(dataCleanupDO.getId())) {
            List<DataCleanupPO> list = dataCleanupDao.selectByParams(params);
            if (CollectionUtils.isNotEmpty(list)) {
                if (DataBaseTypeEnum.MYSQL.getCode().equals(dataCleanupDO.getDbType())) {
                    throw new BusinessException("新增失败，数据清理配置表已存在：" + dataCleanupDO.getTableName());
                } else {
                    throw new BusinessException("新增失败，数据清理配置集合已存在：" + dataCleanupDO.getDatabaseName());
                }
            }
        } else {
            DataCleanupPO old = dataCleanupDao.selectByPrimaryKey(dataCleanupDO.getId());
            if (!Objects.equals(dataCleanupDO.getDbType(), old.getDbType()) ||
                    !Objects.equals(dataCleanupDO.getDatabaseName(), old.getDatabaseName()) ||
                    !Objects.equals(dataCleanupDO.getTableName(), old.getTableName())) {
                List<DataCleanupPO> list = dataCleanupDao.selectByParams(params);
                if (CollectionUtils.isNotEmpty(list)) {
                    if (DataBaseTypeEnum.MYSQL.getCode().equals(dataCleanupDO.getDbType())) {
                        throw new BusinessException("修改失败，数据清理配置表已存在：" + dataCleanupDO.getTableName());
                    } else {
                        throw new BusinessException("修改失败，数据清理配置集合已存在：" + dataCleanupDO.getDatabaseName());
                    }
                }
            }
        }
    }

}
