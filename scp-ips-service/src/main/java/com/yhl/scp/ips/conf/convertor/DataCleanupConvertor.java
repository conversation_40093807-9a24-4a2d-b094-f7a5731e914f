package com.yhl.scp.ips.conf.convertor;

import com.yhl.scp.ips.conf.domain.entity.DataCleanupDO;
import com.yhl.scp.ips.conf.dto.DataCleanupDTO;
import com.yhl.scp.ips.conf.infrastructure.po.DataCleanupPO;
import com.yhl.scp.ips.conf.vo.DataCleanupVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DataCleanupConvertor {

    DataCleanupConvertor INSTANCE = Mappers.getMapper(DataCleanupConvertor.class);

    DataCleanupDO dto2Do(DataCleanupDTO obj);

    DataCleanupDTO do2Dto(DataCleanupDO obj);

    List<DataCleanupDO> dto2Dos(List<DataCleanupDTO> list);

    List<DataCleanupDTO> do2Dtos(List<DataCleanupDO> list);

    DataCleanupVO do2Vo(DataCleanupDO obj);

    DataCleanupVO po2Vo(DataCleanupPO obj);

    List<DataCleanupVO> po2Vos(List<DataCleanupPO> list);

    DataCleanupPO do2Po(DataCleanupDO obj);

    DataCleanupDO po2Do(DataCleanupPO obj);

    DataCleanupPO dto2Po(DataCleanupDTO obj);

    List<DataCleanupPO> dto2Pos(List<DataCleanupDTO> obj);

}
