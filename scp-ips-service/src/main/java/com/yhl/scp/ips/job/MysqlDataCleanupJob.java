package com.yhl.scp.ips.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.job.handler.MysqlDataCleanupHandler;
import org.apache.commons.compress.utils.Lists;

import javax.annotation.Resource;

/**
 * mysql数据清理任务
 *
 * <AUTHOR>
 */
public class MysqlDataCleanupJob {

    @Resource
    private MysqlDataCleanupHandler mysqlDataCleanupHandler;

    @XxlJob("mysqlDataCleanupHandler")
    public ReturnT<String> executeMysqlDataCleanup() {
        XxlJobHelper.log("mysql data cleanup handler start");
        try {
            BaseResponse<Void> response = mysqlDataCleanupHandler.cleanMysqlData(Lists.newArrayList());
            if (!response.getSuccess()) {
                XxlJobHelper.log("mysql data cleanup handler error", response.getMsg());
                return ReturnT.FAIL;
            }
        } catch (Exception ex) {
            XxlJobHelper.log("mysql data cleanup handler error", ex);
            return ReturnT.FAIL;
        }
        XxlJobHelper.log("mysql data cleanup handler end");
        return ReturnT.SUCCESS;
    }
}
