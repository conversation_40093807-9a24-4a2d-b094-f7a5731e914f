package com.yhl.scp.ips.conf.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.ips.conf.infrastructure.po.DataCleanupPO;
import com.yhl.scp.ips.conf.vo.DataCleanupVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DataCleanupDao extends BaseDao<DataCleanupPO, DataCleanupVO> {

    public List<DataCleanupPO> selectEnableDataByIds(@Param("dbType") String dbType);
}
