package com.yhl.scp.ips.conf.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

public class DataCleanupPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 107933261999803156L;

    /**
     * 数据库类型: mysql, mongodb
     */
    private String dbType;
    /**
     * 数据库名/集合名
     */
    private String databaseName;
    /**
     * 数据表名/集合名
     */
    private String tableName;
    /**
     * n天之前的数据清除，默认30天
     */
    private Integer beforeDays;
    /**
     * 创建时间字段名
     */
    private String beforeDaysReferColumn;
    /**
     * where子句信息
     */
    private String whereClause;
    /**
     * 每次删除批次大小
     */
    private Integer batchSize;
    /**
     * 上次清理时间
     */
    private Date lastCleanupTime;
    /**
     * 下次清理时间
     */
    private Date nextCleanupTime;
    /**
     * 描述信息
     */
    private String description;

    public String getDbType() {
        return dbType;
    }

    public void setDbType(String dbType) {
        this.dbType = dbType;
    }

    public String getDatabaseName() {
        return databaseName;
    }

    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Integer getBeforeDays() {
        return beforeDays;
    }

    public void setBeforeDays(Integer beforeDays) {
        this.beforeDays = beforeDays;
    }

    public String getBeforeDaysReferColumn() {
        return beforeDaysReferColumn;
    }

    public void setBeforeDaysReferColumn(String beforeDaysReferColumn) {
        this.beforeDaysReferColumn = beforeDaysReferColumn;
    }

    public String getWhereClause() {
        return whereClause;
    }

    public void setWhereClause(String whereClause) {
        this.whereClause = whereClause;
    }

    public Integer getBatchSize() {
        return batchSize;
    }

    public void setBatchSize(Integer batchSize) {
        this.batchSize = batchSize;
    }

    public Date getLastCleanupTime() {
        return lastCleanupTime;
    }

    public void setLastCleanupTime(Date lastCleanupTime) {
        this.lastCleanupTime = lastCleanupTime;
    }

    public Date getNextCleanupTime() {
        return nextCleanupTime;
    }

    public void setNextCleanupTime(Date nextCleanupTime) {
        this.nextCleanupTime = nextCleanupTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
