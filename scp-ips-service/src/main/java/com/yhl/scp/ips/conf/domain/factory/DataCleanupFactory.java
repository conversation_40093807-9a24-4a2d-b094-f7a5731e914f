package com.yhl.scp.ips.conf.domain.factory;

import com.yhl.scp.ips.conf.domain.entity.DataCleanupDO;
import com.yhl.scp.ips.conf.dto.DataCleanupDTO;
import com.yhl.scp.ips.conf.infrastructure.dao.DataCleanupDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class DataCleanupFactory {

    @Resource
    private DataCleanupDao dataCleanupDao;

    DataCleanupDO create(DataCleanupDTO dto) {
        // TODO
        return null;
    }

}
