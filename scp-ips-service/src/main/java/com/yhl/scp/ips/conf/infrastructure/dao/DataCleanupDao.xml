<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.ips.conf.infrastructure.dao.DataCleanupDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.ips.conf.infrastructure.po.DataCleanupPO">
        <!--@Table conf_data_cleanup-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="db_type" jdbcType="VARCHAR" property="dbType"/>
        <result column="database_name" jdbcType="VARCHAR" property="databaseName"/>
        <result column="table_name" jdbcType="VARCHAR" property="tableName"/>
        <result column="before_days" jdbcType="INTEGER" property="beforeDays"/>
        <result column="before_days_refer_column" jdbcType="VARCHAR" property="beforeDaysReferColumn"/>
        <result column="where_clause" jdbcType="VARCHAR" property="whereClause"/>
        <result column="batch_size" jdbcType="INTEGER" property="batchSize"/>
        <result column="last_cleanup_time" jdbcType="TIMESTAMP" property="lastCleanupTime"/>
        <result column="next_cleanup_time" jdbcType="TIMESTAMP" property="nextCleanupTime"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.ips.conf.vo.DataCleanupVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id, db_type, database_name, `table_name`, before_days, before_days_refer_column, where_clause, batch_size, last_cleanup_time, next_cleanup_time, description, remark, enabled, creator, create_time, modifier, modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.dbType != null and params.dbType != ''">
                and db_type = #{params.dbType,jdbcType=VARCHAR}
            </if>
            <if test="params.databaseName != null and params.databaseName != ''">
                and database_name = #{params.databaseName,jdbcType=VARCHAR}
            </if>
            <if test="params.tableName != null and params.tableName != ''">
                and `table_name` = #{params.tableName,jdbcType=VARCHAR}
            </if>
            <if test="params.beforeDays != null">
                and before_days = #{params.beforeDays,jdbcType=INTEGER}
            </if>
            <if test="params.beforeDaysReferColumn != null and params.beforeDaysReferColumn != ''">
                and before_days_refer_column = #{params.beforeDaysReferColumn,jdbcType=VARCHAR}
            </if>
            <if test="params.whereClause != null and params.whereClause != ''">
                and where_clause = #{params.whereClause,jdbcType=VARCHAR}
            </if>
            <if test="params.batchSize != null">
                and batch_size = #{params.batchSize,jdbcType=INTEGER}
            </if>
            <if test="params.lastCleanupTime != null">
                and last_cleanup_time = #{params.lastCleanupTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.nextCleanupTime != null">
                and next_cleanup_time = #{params.nextCleanupTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.description != null and params.description != ''">
                and description = #{params.description,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from conf_data_cleanup
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from conf_data_cleanup
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from conf_data_cleanup
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from conf_data_cleanup
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectEnableDataByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from conf_data_cleanup
        where 1 = 1
        and db_type = #{dbType,jdbcType=VARCHAR}
        and enabled = 'YES'
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.ips.conf.infrastructure.po.DataCleanupPO">
        insert into conf_data_cleanup(id,
                                      db_type,
                                      database_name,
                                      `table_name`,
                                      before_days,
                                      before_days_refer_column,
                                      where_clause,
                                      batch_size,
                                      last_cleanup_time,
                                      next_cleanup_time,
                                      description,
                                      remark,
                                      enabled,
                                      creator,
                                      create_time,
                                      modifier,
                                      modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{dbType,jdbcType=VARCHAR},
                #{databaseName,jdbcType=VARCHAR},
                #{tableName,jdbcType=VARCHAR},
                #{beforeDays,jdbcType=INTEGER},
                #{beforeDaysReferColumn,jdbcType=VARCHAR},
                #{whereClause,jdbcType=VARCHAR},
                #{batchSize,jdbcType=INTEGER},
                #{lastCleanupTime,jdbcType=TIMESTAMP},
                #{nextCleanupTime,jdbcType=TIMESTAMP},
                #{description,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.ips.conf.infrastructure.po.DataCleanupPO">
        insert into conf_data_cleanup(id,
                                      db_type,
                                      database_name,
                                      `table_name`,
                                      before_days,
                                      before_days_refer_column,
                                      where_clause,
                                      batch_size,
                                      last_cleanup_time,
                                      next_cleanup_time,
                                      description,
                                      remark,
                                      enabled,
                                      creator,
                                      create_time,
                                      modifier,
                                      modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{dbType,jdbcType=VARCHAR},
                #{databaseName,jdbcType=VARCHAR},
                #{tableName,jdbcType=VARCHAR},
                #{beforeDays,jdbcType=INTEGER},
                #{beforeDaysReferColumn,jdbcType=VARCHAR},
                #{whereClause,jdbcType=VARCHAR},
                #{batchSize,jdbcType=INTEGER},
                #{lastCleanupTime,jdbcType=TIMESTAMP},
                #{nextCleanupTime,jdbcType=TIMESTAMP},
                #{description,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into conf_data_cleanup(
        id,
        db_type,
        database_name,
        `table_name`,
        before_days,
        before_days_refer_column,
        where_clause,
        batch_size,
        last_cleanup_time,
        next_cleanup_time,
        description,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id,jdbcType=VARCHAR},
            #{entity.dbType,jdbcType=VARCHAR},
            #{entity.databaseName,jdbcType=VARCHAR},
            #{entity.tableName,jdbcType=VARCHAR},
            #{entity.beforeDays,jdbcType=INTEGER},
            #{entity.beforeDaysReferColumn,jdbcType=VARCHAR},
            #{entity.whereClause,jdbcType=VARCHAR},
            #{entity.batchSize,jdbcType=INTEGER},
            #{entity.lastCleanupTime,jdbcType=TIMESTAMP},
            #{entity.nextCleanupTime,jdbcType=TIMESTAMP},
            #{entity.description,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into conf_data_cleanup(
        id,
        db_type,
        database_name,
        `table_name`,
        before_days,
        before_days_refer_column,
        where_clause,
        batch_size,
        last_cleanup_time,
        next_cleanup_time,
        description,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.dbType,jdbcType=VARCHAR},
            #{entity.databaseName,jdbcType=VARCHAR},
            #{entity.tableName,jdbcType=VARCHAR},
            #{entity.beforeDays,jdbcType=INTEGER},
            #{entity.beforeDaysReferColumn,jdbcType=VARCHAR},
            #{entity.whereClause,jdbcType=VARCHAR},
            #{entity.batchSize,jdbcType=INTEGER},
            #{entity.lastCleanupTime,jdbcType=TIMESTAMP},
            #{entity.nextCleanupTime,jdbcType=TIMESTAMP},
            #{entity.description,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.ips.conf.infrastructure.po.DataCleanupPO">
        update conf_data_cleanup
        set db_type                  = #{dbType,jdbcType=VARCHAR},
            database_name            = #{databaseName,jdbcType=VARCHAR},
            `table_name`             = #{tableName,jdbcType=VARCHAR},
            before_days              = #{beforeDays,jdbcType=INTEGER},
            before_days_refer_column = #{beforeDaysReferColumn,jdbcType=VARCHAR},
            where_clause             = #{whereClause,jdbcType=VARCHAR},
            batch_size               = #{batchSize,jdbcType=INTEGER},
            last_cleanup_time        = #{lastCleanupTime,jdbcType=TIMESTAMP},
            next_cleanup_time        = #{nextCleanupTime,jdbcType=TIMESTAMP},
            description              = #{description,jdbcType=VARCHAR},
            remark                   = #{remark,jdbcType=VARCHAR},
            enabled                  = #{enabled,jdbcType=VARCHAR},
            modifier                 = #{modifier,jdbcType=VARCHAR},
            modify_time              = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.ips.conf.infrastructure.po.DataCleanupPO">
        update conf_data_cleanup
        <set>
            <if test="item.dbType != null and item.dbType != ''">
                db_type = #{item.dbType,jdbcType=VARCHAR},
            </if>
            <if test="item.databaseName != null and item.databaseName != ''">
                database_name = #{item.databaseName,jdbcType=VARCHAR},
            </if>
            <if test="item.tableName != null and item.tableName != ''">
                `table_name` = #{item.tableName,jdbcType=VARCHAR},
            </if>
            <if test="item.beforeDays != null">
                before_days = #{item.beforeDays,jdbcType=INTEGER},
            </if>
            <if test="item.beforeDaysReferColumn != null and item.beforeDaysReferColumn != ''">
                before_days_refer_column = #{item.beforeDaysReferColumn,jdbcType=VARCHAR},
            </if>
            <if test="item.whereClause != null and item.whereClause != ''">
                where_clause = #{item.whereClause,jdbcType=VARCHAR},
            </if>
            <if test="item.batchSize != null">
                batch_size = #{item.batchSize,jdbcType=INTEGER},
            </if>
            <if test="item.lastCleanupTime != null">
                last_cleanup_time = #{item.lastCleanupTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.nextCleanupTime != null">
                next_cleanup_time = #{item.nextCleanupTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.description != null and item.description != ''">
                description = #{item.description,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update conf_data_cleanup
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="db_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.dbType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="database_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.databaseName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="table_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.tableName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="before_days = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.beforeDays,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="before_days_refer_column = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.beforeDaysReferColumn,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="where_clause = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.whereClause,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="batch_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.batchSize,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="last_cleanup_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lastCleanupTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="next_cleanup_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.nextCleanupTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="description = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.description,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update conf_data_cleanup
            <set>
                <if test="item.dbType != null and item.dbType != ''">
                    db_type = #{item.dbType,jdbcType=VARCHAR},
                </if>
                <if test="item.databaseName != null and item.databaseName != ''">
                    database_name = #{item.databaseName,jdbcType=VARCHAR},
                </if>
                <if test="item.tableName != null and item.tableName != ''">
                    `table_name` = #{item.tableName,jdbcType=VARCHAR},
                </if>
                <if test="item.beforeDays != null">
                    before_days = #{item.beforeDays,jdbcType=INTEGER},
                </if>
                <if test="item.beforeDaysReferColumn != null and item.beforeDaysReferColumn != ''">
                    before_days_refer_column = #{item.beforeDaysReferColumn,jdbcType=VARCHAR},
                </if>
                <if test="item.whereClause != null and item.whereClause != ''">
                    where_clause = #{item.whereClause,jdbcType=VARCHAR},
                </if>
                <if test="item.batchSize != null">
                    batch_size = #{item.batchSize,jdbcType=INTEGER},
                </if>
                <if test="item.lastCleanupTime != null">
                    last_cleanup_time = #{item.lastCleanupTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.nextCleanupTime != null">
                    next_cleanup_time = #{item.nextCleanupTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.description != null and item.description != ''">
                    description = #{item.description,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from conf_data_cleanup
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from conf_data_cleanup where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
