package com.yhl.scp.ips.conf.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.audit.feign.AuditFeign;
import com.yhl.scp.ips.conf.convertor.DataCleanupConvertor;
import com.yhl.scp.ips.conf.domain.entity.DataCleanupDO;
import com.yhl.scp.ips.conf.domain.service.DataCleanupDomainService;
import com.yhl.scp.ips.conf.dto.DataCleanupDTO;
import com.yhl.scp.ips.conf.infrastructure.dao.DataCleanupDao;
import com.yhl.scp.ips.conf.infrastructure.po.DataCleanupPO;
import com.yhl.scp.ips.conf.service.DataCleanupService;
import com.yhl.scp.ips.conf.vo.DataCleanupVO;
import com.yhl.scp.ips.job.handler.MysqlDataCleanupHandler;
import com.yhl.scp.ips.utils.BasePOUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
public class DataCleanupServiceImpl extends AbstractService implements DataCleanupService {

    @Resource
    private DataCleanupDao dataCleanupDao;

    @Resource
    private DataCleanupDomainService dataCleanupDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private MysqlDataCleanupHandler mysqlDataCleanupHandler;

    @Resource
    private AuditFeign auditFeign;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(DataCleanupDTO dataCleanupDTO) {
        // 0.数据转换
        DataCleanupDO dataCleanupDO = DataCleanupConvertor.INSTANCE.dto2Do(dataCleanupDTO);
        DataCleanupPO dataCleanupPO = DataCleanupConvertor.INSTANCE.dto2Po(dataCleanupDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        dataCleanupDomainService.validation(dataCleanupDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(dataCleanupPO);
        dataCleanupDao.insert(dataCleanupPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(DataCleanupDTO dataCleanupDTO) {
        // 0.数据转换
        DataCleanupDO dataCleanupDO = DataCleanupConvertor.INSTANCE.dto2Do(dataCleanupDTO);
        DataCleanupPO dataCleanupPO = DataCleanupConvertor.INSTANCE.dto2Po(dataCleanupDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        dataCleanupDomainService.validation(dataCleanupDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(dataCleanupPO);
        dataCleanupDao.update(dataCleanupPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<DataCleanupDTO> list) {
        List<DataCleanupPO> newList = DataCleanupConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        dataCleanupDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<DataCleanupDTO> list) {
        List<DataCleanupPO> newList = DataCleanupConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        dataCleanupDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return dataCleanupDao.deleteBatch(idList);
        }
        return dataCleanupDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public DataCleanupVO selectByPrimaryKey(String id) {
        DataCleanupPO po = dataCleanupDao.selectByPrimaryKey(id);
        return DataCleanupConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "DATA_CLEANUP")
    public List<DataCleanupVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "DATA_CLEANUP")
    public List<DataCleanupVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<DataCleanupVO> dataList = dataCleanupDao.selectByCondition(sortParam, queryCriteriaParam);
        DataCleanupServiceImpl target = springBeanUtils.getBean(DataCleanupServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<DataCleanupVO> selectByParams(Map<String, Object> params) {
        List<DataCleanupPO> list = dataCleanupDao.selectByParams(params);
        return DataCleanupConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<DataCleanupVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public BaseResponse<Void> manualDataCleanup(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return BaseResponse.error("请至少选择一条数据");
        }
        CompletableFuture<Void> mysqlFuture = CompletableFuture.runAsync(() -> mysqlDataCleanupHandler.cleanMysqlData(ids));
        CompletableFuture<Void> mongoFuture = CompletableFuture.runAsync(() -> auditFeign.cleanupMongoData(ids));
        CompletableFuture<Void> future = CompletableFuture.allOf(mysqlFuture, mongoFuture);
        future.join();
        return BaseResponse.success("手动清除成功");
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<DataCleanupVO> invocation(List<DataCleanupVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
