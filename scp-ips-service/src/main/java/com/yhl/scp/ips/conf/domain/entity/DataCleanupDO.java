package com.yhl.scp.ips.conf.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DataCleanupDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 508674224507792812L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 数据库类型: mysql, mongodb
     */
    private String dbType;
    /**
     * 数据库名/集合名
     */
    private String databaseName;
    /**
     * 数据表名/集合名
     */
    private String tableName;
    /**
     * n天之前的数据清除，默认30天
     */
    private Integer beforeDays;
    /**
     * 创建时间字段名
     */
    private String beforeDaysReferColumn;
    /**
     * where子句信息
     */
    private String whereClause;
    /**
     * 每次删除批次大小
     */
    private Integer batchSize;
    /**
     * 上次清理时间
     */
    private Date lastCleanupTime;
    /**
     * 下次清理时间
     */
    private Date nextCleanupTime;
    /**
     * 描述信息
     */
    private String description;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String modifier;
    /**
     * 修改时间
     */
    private Date modifyTime;

}
