package com.yhl.scp.ips.job.handler;

import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.conf.infrastructure.dao.DataCleanupDao;
import com.yhl.scp.ips.conf.infrastructure.po.DataCleanupPO;
import com.yhl.scp.ips.enums.DataBaseTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 数据清理处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MysqlDataCleanupHandler {

    @Resource
    private DataCleanupDao dataCleanupDao;

    @Resource
    private JdbcTemplate jdbcTemplate;

    public BaseResponse<Void> cleanMysqlData(List<String> executorIds) {
        List<DataCleanupPO> cleanupList = dataCleanupDao.selectEnableDataByIds(DataBaseTypeEnum.MYSQL.getCode());
        if (CollectionUtils.isEmpty(cleanupList)) {
            return BaseResponse.error("没有可执行数据清理任务");
        }
        if (CollectionUtils.isNotEmpty(executorIds)) {
            cleanupList = cleanupList.stream().filter(cleanupVO -> executorIds.contains(cleanupVO.getId())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(cleanupList)) {
            return BaseResponse.error("没有可执行数据清理任务");
        }
        for (DataCleanupPO cleanupPO : cleanupList) {
            boolean deleteResult = deleteByDataCleanup(cleanupPO);
            if (!deleteResult) {
                continue;
            }
            cleanupPO.setLastCleanupTime(new Date());
            dataCleanupDao.update(cleanupPO);
        }
        return BaseResponse.success("MySQL清理成功");
    }

    private boolean deleteByDataCleanup(DataCleanupPO cleanupPO) {
        int batchSize = Objects.nonNull(cleanupPO.getBatchSize()) ? cleanupPO.getBatchSize() : 2000;
        String tableName = cleanupPO.getDatabaseName() + "." + cleanupPO.getTableName();
        int beforeDays = Objects.nonNull(cleanupPO.getBeforeDays()) ? cleanupPO.getBeforeDays() : 30;
        String whereClause = cleanupPO.getWhereClause();
        String beforeDaysReferColumn = cleanupPO.getBeforeDaysReferColumn();
        try {
            // 构建基础查询条件
            long cutoffTimeMillis = System.currentTimeMillis() - (long) beforeDays * 24 * 60 * 60 * 1000L;
            Date cutoffDate = new Date(cutoffTimeMillis);

            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("DELETE FROM ").append(tableName);
            sqlBuilder.append(" WHERE ").append(beforeDaysReferColumn).append(" < ?");

            // 合并动态条件
            Object[] params;
            if (StringUtils.isNotBlank(whereClause)) {
                sqlBuilder.append(" AND (").append(whereClause).append(")");
                params = new Object[]{cutoffDate};
            } else {
                params = new Object[]{cutoffDate};
            }

            sqlBuilder.append(" ORDER BY ").append(beforeDaysReferColumn).append(" ASC");
            sqlBuilder.append(" LIMIT ").append(batchSize);

            long startTime = System.currentTimeMillis();
            long totalDeleted = 0;

            int deletedCount;
            do {
                deletedCount = jdbcTemplate.update(sqlBuilder.toString(), params);
                totalDeleted += deletedCount;
                log.info("批量删除记录: 表={}, 删除数量={}, 累计删除={}, 耗时={}ms", tableName, deletedCount, totalDeleted, System.currentTimeMillis() - startTime);
            } while (deletedCount > 0);

            log.info("完成清理任务: 表={}, 总删除={}, 耗时={}ms", tableName, totalDeleted, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("处理数据清理任务失败: 配置ID={}, 表名={}", cleanupPO.getId(), tableName, e);
            return false;
        }
        return true;
    }
}
