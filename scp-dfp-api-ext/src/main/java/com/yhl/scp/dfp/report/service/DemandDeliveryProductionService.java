package com.yhl.scp.dfp.report.service;

import com.yhl.scp.dfp.report.dto.DemandDeliveryProductionDetailDTO;
import com.yhl.scp.dfp.report.vo.DemandDeliveryProductionVO;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <code>DemandDeliveryProductionService</code>
 * <p>
 * 需求发货生产报表服务接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-15 10:49:11
 */
public interface DemandDeliveryProductionService {

    /**
     * 查询需求发货生产报表数据（原方法，保持兼容性）
     * @param dto 查询条件
     * @return 报表数据列表
     */
    List<DemandDeliveryProductionVO> queryDemandDeliveryProductionReport(DemandDeliveryProductionDetailDTO dto);

    /**
     * 全量查询需求发货生产报表数据（取消分页）
     * @param dto 查询条件
     * @return 全量报表数据
     */
    List<DemandDeliveryProductionVO> queryDemandDeliveryProductionReportWithoutPagination(DemandDeliveryProductionDetailDTO dto);

    /**
     * 获取并处理所有报表数据（不进行DTO层面的过滤，强制从数据源获取）
     */
    List<DemandDeliveryProductionVO> fetchAndProcessAllReportData(DemandDeliveryProductionDetailDTO dto);

    /**
     * 获取中转库库存数据
     * @param oemCodes
     * @param productCodes
     * @return
     */
    Map<String, BigDecimal> getTransitStockMap(List<String> oemCodes, List<String> productCodes);

    /**
     * 导出需求发货生产报表数据
     * @param dto 查询条件
     * @param response HTTP响应
     */
    void exportData(DemandDeliveryProductionDetailDTO dto, HttpServletResponse response);


    /**
     * 获取中转库在途数据
     * @param oemCodes
     * @param productCodes
     * @return
     */
    Map<String, BigDecimal> getTransportingQtyMap(List<String> oemCodes, List<String> productCodes);
}
