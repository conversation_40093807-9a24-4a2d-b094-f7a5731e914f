package com.yhl.scp.dfp.consistence.service;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.consistence.dto.ConsistenceDemandForecastDataDTO;
import com.yhl.scp.dfp.consistence.dto.ExecutionMonitorDTO;
import com.yhl.scp.dfp.consistence.dto.ForecastSummerReportDataDTO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataVO;
import com.yhl.scp.dfp.consistence.vo.ExecutionMonitorResultVO;
import com.yhl.scp.dfp.consistence.vo.ForecastSummerReportDataVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2;
import com.yhl.scp.dfp.demand.dto.DemandForecastReplayDTO;
import com.yhl.scp.dfp.demand.vo.DemandForecastReplayDetailVO;
import com.yhl.scp.dfp.demand.vo.DemandForecastReplayVO;
import com.yhl.scp.dfp.demand.vo.DemandVersionAndOemVO;
import com.yhl.scp.dfp.demand.vo.DemandVersionVO;
import com.yhl.scp.dfp.release.dto.ReleaseOemDTO;
import com.yhl.scp.dfp.release.dto.ReleaseProductItemDTO;
import com.yhl.scp.dfp.release.vo.ReleaseExportVO;
import com.yhl.scp.dfp.release.vo.ReleaseLineChartMonthVO;
import com.yhl.scp.dfp.release.vo.ReleaseOemVO;
import com.yhl.scp.dfp.release.vo.ReleaseVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <code>ConsistenceDemandForecastDataService</code>
 * <p>
 * 一致性业务预测数据应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 10:19:10
 */
public interface ConsistenceDemandForecastDataService extends BaseService<ConsistenceDemandForecastDataDTO,
        ConsistenceDemandForecastDataVO> {

    /**
     * 查询所有
     *
     * @return list {@link ConsistenceDemandForecastDataVO}
     */
    List<ConsistenceDemandForecastDataVO> selectAll();

    /**
     * 批量删除
     *
     * @param versionDTOList 删除对象列表
     */
    int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList);

    /**
     * 执行监控
     *
     * @param monitorDTO 监控参数
     * @return @{link ExecutionMonitorResultVO}
     */
    List<ExecutionMonitorResultVO> executionMonitor(ExecutionMonitorDTO monitorDTO);

    void monitorUpdateDemandForecast(String versionId, String month);

    /**
     * 根据计划周期查询最新一致性业务预测数据
     *
     * @param planPeriod 计划周期
     * @param startTime  需求开始时间
     * @param endTime    需求结束时间
     * @return {@link DeliveryPlanVO2}
     */
    List<DeliveryPlanVO2> selectVO2ByPlanPeriod(String planPeriod, String startTime, String endTime);

    /**
     * 根据版本获取统计后数据
     *
     * @param versionList     需求版本列表
     * @param showLevel       展示层级
     * @param replayDTO       复盘请求对象
     * @param vehicleCodeList 车型代码列表
     * @param dateListSort    日期列表
     * @return {@link DemandForecastReplayDetailVO}
     */
    @SuppressWarnings("unused")
    List<DemandForecastReplayDetailVO> selectDataByVersionList(List<DemandVersionVO> versionList, String showLevel,
                                                               DemandForecastReplayDTO replayDTO,
                                                               List<String> vehicleCodeList, List<String> dateListSort);

    /**
     * 执行监控版本下拉
     *
     * @return {@link DemandVersionVO}
     */
    List<DemandVersionAndOemVO> getVersionAndOem();

    /**
     * 根据版本id查询一致性业务预测数据
     *
     * @param versionIds 版本ID列表
     * @return {@link DeliveryPlanVO2}
     */
    List<DeliveryPlanVO2> selectVO2ByVersionId(List<String> versionIds);

    /**
     * 查询复盘数据
     *
     * @param basicParams 基础参数
     * @param showLevel   展示层级
     * @param planPeriod  计划视角
     * @param planPeriods 计划周期列表
     * @return {@link DemandForecastReplayVO}
     */
    List<DemandForecastReplayVO> selectForecastReplay(Map<String, Object> basicParams, String showLevel,
                                                      String planPeriod, List<String> planPeriods);

    List<ReleaseOemVO> selectOemInfoByVersionId(ReleaseOemDTO releaseOemDTO);

    List<ReleaseVO> selectByVersionAndOem(String versionId, String demandCategory, String oemCode, String stockPointCode);

    int updateForecastValueByIds(List<ReleaseProductItemDTO> detailDTOS);

    List<ReleaseLineChartMonthVO> selectLineChartByYearAndOem(int calcYear, String demandType, String oemCode);

    @SuppressWarnings("unused")
    List<Map<String, String>> selectDistinctVersionCode(String versionStatus);

    List<Map<String, String>> selectDistinctNewVersionCode();

    List<ReleaseExportVO> selectReleaseExportVO(Map<String, Object> params);

    List<ConsistenceDemandForecastDataVO> selectByPrimaryKeys(List<String> ids);

    BaseResponse<String> executionSend();

    List<ConsistenceDemandForecastDataVO> selectForecastQuantityByVersionId(String versionId, String yearMonthStr);

    /**
     * 查询预测汇总报表数据
     *
     * @param dto 查询条件
     * @return java.util.List<com.yhl.scp.dfp.consistence.vo.ForecastSummerReportDataVO>
     */
    List<ForecastSummerReportDataVO> queryForecastSummaryReport(ForecastSummerReportDataDTO dto);

    BaseResponse<List<LabelValue<String>>> selectPartLevelProductDropDown(ConsistenceDemandForecastDataDTO dto);

    BaseResponse<String> executionQuayErrorSend();

    void forecastSummaryReportExport(HttpServletResponse response, ForecastSummerReportDataDTO dto);

    List<ConsistenceDemandForecastDataVO> selectIgnorePlannerDataList(String versionId, String orderPlanner,
                                                                      String planPeriod);

}