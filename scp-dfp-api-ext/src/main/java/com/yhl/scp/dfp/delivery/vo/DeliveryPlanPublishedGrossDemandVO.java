package com.yhl.scp.dfp.delivery.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>DeliveryPlanPublishedGrossDemandVO</code>
 * <p>
 * 发货计划发布表-毛需求计算版VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-12 16:32:04
 */
@ApiModel(value = "发货计划发布表-毛需求计算版VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryPlanPublishedGrossDemandVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 429505634753294495L;

    /**
     * KID
     */
    @ApiModelProperty(value = "KID")
    @FieldInterpretation(value = "KID")
    private Integer kid;
    /**
     * 发货计划版本id
     */
    @ApiModelProperty(value = "发货计划版本id")
    @FieldInterpretation(value = "发货计划版本id")
    private String deliveryVersionId;
    /**
     * 主表id
     */
    @ApiModelProperty(value = "主表id")
    @FieldInterpretation(value = "主表id")
    private String deliveryPlanDataId;
    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    @FieldInterpretation(value = "需求类型")
    private String demandCategory;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemCode;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String productCode;
    /**
     * 发货日期
     */
    @ApiModelProperty(value = "发货日期")
    @FieldInterpretation(value = "发货日期")
    private Date demandTime;
    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    @FieldInterpretation(value = "需求数量")
    private Integer demandQuantity;
    /**
     * 生产协调量
     */
    @ApiModelProperty(value = "生产协调量")
    @FieldInterpretation(value = "生产协调量")
    private Integer coordinationQuantity;
    /**
     * 箱数
     */
    @ApiModelProperty(value = "箱数")
    @FieldInterpretation(value = "箱数")
    private Integer boxQuantity;
    /**
     * 发版人
     */
    @ApiModelProperty(value = "发版人")
    @FieldInterpretation(value = "发版人")
    private String publisher;
    /**
     * 发版时间
     */
    @ApiModelProperty(value = "发版时间")
    @FieldInterpretation(value = "发版时间")
    private Date publishTime;

    @Override
    public void clean() {

    }

}
