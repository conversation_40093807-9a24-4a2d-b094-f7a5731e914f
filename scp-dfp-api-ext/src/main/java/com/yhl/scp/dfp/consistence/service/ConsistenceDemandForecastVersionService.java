package com.yhl.scp.dfp.consistence.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.consistence.dto.ConsistenceDemandForecastSavePublishDTO;
import com.yhl.scp.dfp.consistence.dto.ConsistenceDemandForecastVersionDTO;
import com.yhl.scp.dfp.consistence.dto.ExecutionMonitorDTO;
import com.yhl.scp.dfp.consistence.dto.TreePageQueryDTO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastReferenceVersionVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionVO;
import com.yhl.scp.dfp.demand.vo.DemandForecastEstablishmentVO;

/**
 * <code>ConsistenceDemandForecastVersionService</code>
 * <p>
 * 一致性业务预测版本应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 10:19:30
 */
public interface ConsistenceDemandForecastVersionService extends BaseService<ConsistenceDemandForecastVersionDTO,
        ConsistenceDemandForecastVersionVO> {

    /**
     * 查询所有
     *
     * @return list {@link ConsistenceDemandForecastVersionVO}
     */
    List<ConsistenceDemandForecastVersionVO> selectAll();

    /**
     * 批量删除
     *
     * @param versionDTOList 删除对象列表
     */
    int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList);

    /**
     * 分级查询
     *
     * @return ConsistenceDemandForecastVersionVOList
     */
    List<ConsistenceDemandForecastVersionVO> treeQuery();

    /**
     * 获取一致性业务预测版本号
     *
     * @param planPeriod 计划周期
     * @return {@link ConsistenceDemandForecastVersionVO}
     */
    List<ConsistenceDemandForecastVersionVO> getVersionCodes(String planPeriod);

    /**
     * 根据版本号查询主机厂信息
     *
     * @param versionId 版本ID
     * @return java.util.List<java.lang.String>
     */
    List<String> getOemCodeOrigin(String versionId);

    /**
     * 一致性业务预测版本发布
     *
     * @param versionId 版本ID
     */
    void publishVersion(String versionId);

    /**
     * 获取每个计划周期最新版本数据
     */
    List<ConsistenceDemandForecastVersionVO> selectForecastVersionALlPlanPeriodNew();

    /**
     * 查询第二三层级已发布版本信息
     *
     * @return {@link ConsistenceDemandForecastVersionVO}
     */
    List<ConsistenceDemandForecastVersionVO> selectPublishVersionDetail();

    /**
     * 查询一致性业务预测版本-产能平衡使用
     *
     * @return {@link ConsistenceDemandForecastVersionVO}
     */
    List<ConsistenceDemandForecastVersionVO> selectVersionInfoByCapacityBalance(Pagination pagination);

    List<DemandForecastEstablishmentVO> selectMaxVersionCodeData(String demandCategory, String oemCode,
                                                                 List<String> productCodes);

    List<ConsistenceDemandForecastVersionVO> selectMaxVersionByParams(Map<String, Object> params);


    BaseResponse<List<LabelValue<String>>> dropDownVersion();

    String getLastVersionId(String versionId);

    List<ConsistenceDemandForecastVersionVO> selectByPlanPeriodList(List<String> planPeriodList, String publishStatus);

	String getMaxVersionCodes(String planPeriod);

	void doCreateVersion(ConsistenceDemandForecastVersionDTO consistenceDemandForecastVersionDTO);

    ExecutionMonitorDTO getExecutionMonitorDTO();

    ConsistenceDemandForecastVersionVO selectConsistenceDemandForecastVersionLatestPublished();

    /**
     * 查询最新的版本
     * @param of
     * @return
     */
	ConsistenceDemandForecastVersionVO selectOneMaxVersionByParams(Map<String, String> params);

	/**
	 * 查询每个月是否发布为是且是否为评审版本为是的一致性需求预测版本ids
	 * @param planPeriods
	 * @return
	 */
	List<String> selectHistoryVersionByPlanPeriods(List<String> planPeriods);

    void export(HttpServletResponse response);

    ConsistenceDemandForecastReferenceVersionVO getReferenceForecastVersion(String planPeriod);

    String doCreateAndPublish(ConsistenceDemandForecastSavePublishDTO consistenceDemandForecastSavePublishDTO);

    PageInfo<ConsistenceDemandForecastVersionVO> treePageQuery(TreePageQueryDTO treePageQueryDTO);
}