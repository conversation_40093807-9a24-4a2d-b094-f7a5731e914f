package com.yhl.scp.mrp.compare.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MaterialChangeCompareVO</code>
 * <p>
 * 材料计划变动提醒-材料对比VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-17 16:00:26
 */
@ApiModel(value = "材料计划变动提醒-材料对比VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaterialChangeCompareVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -78776812721054992L;

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    @FieldInterpretation(value = "产品编码")
    private String productCode;
    /**
     * 材料编码
     */
    @ApiModelProperty(value = "材料编码")
    @FieldInterpretation(value = "材料编码")
    private String materialCode;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    @FieldInterpretation(value = "车型编码")
    private String vehicleModelCode;
    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @FieldInterpretation(value = "供应商编码")
    private String supplierCode;
    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @FieldInterpretation(value = "供应商名称")
    private String supplierName;
    /**
     * 采购需求第1周现数量
     */
    @ApiModelProperty(value = "采购需求第1周现数量")
    @FieldInterpretation(value = "采购需求第1周现数量")
    private BigDecimal purchaseDemandOneWeekCurrentQuantity;
    /**
     * 采购需求第1周变化量
     */
    @ApiModelProperty(value = "采购需求第1周变化量")
    @FieldInterpretation(value = "采购需求第1周变化量")
    private BigDecimal purchaseDemandOneWeekChangeQuantity;
    /**
     * 采购需求第2周现数量
     */
    @ApiModelProperty(value = "采购需求第2周现数量")
    @FieldInterpretation(value = "采购需求第2周现数量")
    private BigDecimal purchaseDemandTwoWeekCurrentQuantity;
    /**
     * 采购需求第2周变化量
     */
    @ApiModelProperty(value = "采购需求第2周变化量")
    @FieldInterpretation(value = "采购需求第2周变化量")
    private BigDecimal purchaseDemandTwoWeekChangeQuantity;
    /**
     * 采购需求第3周现数量
     */
    @ApiModelProperty(value = "采购需求第3周现数量")
    @FieldInterpretation(value = "采购需求第3周现数量")
    private BigDecimal purchaseDemandThreeWeekCurrentQuantity;
    /**
     * 采购需求第3周变化量
     */
    @ApiModelProperty(value = "采购需求第3周变化量")
    @FieldInterpretation(value = "采购需求第3周变化量")
    private BigDecimal purchaseDemandThreeWeekChangeQuantity;
    /**
     * 采购需求第4周现数量
     */
    @ApiModelProperty(value = "采购需求第4周现数量")
    @FieldInterpretation(value = "采购需求第4周现数量")
    private BigDecimal purchaseDemandFourWeekCurrentQuantity;
    /**
     * 采购需求第4周变化量
     */
    @ApiModelProperty(value = "采购需求第4周变化量")
    @FieldInterpretation(value = "采购需求第4周变化量")
    private BigDecimal purchaseDemandFourWeekChangeQuantity;
    /**
     * 采购需求第1月现数量
     */
    @ApiModelProperty(value = "采购需求第1月现数量")
    @FieldInterpretation(value = "采购需求第1月现数量")
    private BigDecimal purchaseDemandOneMonthCurrentQuantity;
    /**
     * 采购需求第1月变化量
     */
    @ApiModelProperty(value = "采购需求第1月变化量")
    @FieldInterpretation(value = "采购需求第1月变化量")
    private BigDecimal purchaseDemandOneMonthChangeQuantity;
    /**
     * 采购需求第2月现数量
     */
    @ApiModelProperty(value = "采购需求第2月现数量")
    @FieldInterpretation(value = "采购需求第2月现数量")
    private BigDecimal purchaseDemandTwoMonthCurrentQuantity;
    /**
     * 采购需求第2月变化量
     */
    @ApiModelProperty(value = "采购需求第2月变化量")
    @FieldInterpretation(value = "采购需求第2月变化量")
    private BigDecimal purchaseDemandTwoMonthChangeQuantity;
    /**
     * 采购需求第3月现数量
     */
    @ApiModelProperty(value = "采购需求第3月现数量")
    @FieldInterpretation(value = "采购需求第3月现数量")
    private BigDecimal purchaseDemandThreeMonthCurrentQuantity;
    /**
     * 采购需求第3月变化量
     */
    @ApiModelProperty(value = "采购需求第3月变化量")
    @FieldInterpretation(value = "采购需求第3月变化量")
    private BigDecimal purchaseDemandThreeMonthChangeQuantity;
    /**
     * 采购需求第4月现数量
     */
    @ApiModelProperty(value = "采购需求第4月现数量")
    @FieldInterpretation(value = "采购需求第4月现数量")
    private BigDecimal purchaseDemandFourMonthCurrentQuantity;
    /**
     * 采购需求第4月变化量
     */
    @ApiModelProperty(value = "采购需求第4月变化量")
    @FieldInterpretation(value = "采购需求第4月变化量")
    private BigDecimal purchaseDemandFourMonthChangeQuantity;
    /**
     * 采购需求第5月现数量
     */
    @ApiModelProperty(value = "采购需求第5月现数量")
    @FieldInterpretation(value = "采购需求第5月现数量")
    private BigDecimal purchaseDemandFiveMonthCurrentQuantity;
    /**
     * 采购需求第5月变化量
     */
    @ApiModelProperty(value = "采购需求第5月变化量")
    @FieldInterpretation(value = "采购需求第5月变化量")
    private BigDecimal purchaseDemandFiveMonthChangeQuantity;
    /**
     * 采购需求第6月现数量
     */
    @ApiModelProperty(value = "采购需求第6月现数量")
    @FieldInterpretation(value = "采购需求第6月现数量")
    private BigDecimal purchaseDemandSixMonthCurrentQuantity;
    /**
     * 采购需求第6月变化量
     */
    @ApiModelProperty(value = "采购需求第6月变化量")
    @FieldInterpretation(value = "采购需求第6月变化量")
    private BigDecimal purchaseDemandSixMonthChangeQuantity;
    /**
     * 采购需求第7月现数量
     */
    @ApiModelProperty(value = "采购需求第7月现数量")
    @FieldInterpretation(value = "采购需求第7月现数量")
    private BigDecimal purchaseDemandSenveMonthCurrentQuantity;
    /**
     * 采购需求第7月变化量
     */
    @ApiModelProperty(value = "采购需求第7月变化量")
    @FieldInterpretation(value = "采购需求第7月变化量")
    private BigDecimal purchaseDemandSenveMonthChangeQuantity;
    /**
     * 采购需求第8月现数量
     */
    @ApiModelProperty(value = "采购需求第8月现数量")
    @FieldInterpretation(value = "采购需求第8月现数量")
    private BigDecimal purchaseDemandEightMonthCurrentQuantity;
    /**
     * 采购需求第8月变化量
     */
    @ApiModelProperty(value = "采购需求第8月变化量")
    @FieldInterpretation(value = "采购需求第8月变化量")
    private BigDecimal purchaseDemandEightMonthChangeQuantity;
    /**
     * 波动率
     */
    @ApiModelProperty(value = "波动率")
    @FieldInterpretation(value = "波动率")
    private BigDecimal volatility;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @FieldInterpretation(value = "版本")
    private Integer versionValue;

    @Override
    public void clean() {

    }

}
