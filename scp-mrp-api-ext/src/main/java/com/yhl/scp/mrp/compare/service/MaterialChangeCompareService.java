package com.yhl.scp.mrp.compare.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mrp.compare.dto.MaterialChangeCompareDTO;
import com.yhl.scp.mrp.compare.vo.MaterialChangeCompareVO;

import java.util.List;

/**
 * <code>MaterialChangeCompareService</code>
 * <p>
 * 材料计划变动提醒-材料对比应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-17 16:00:32
 */
public interface MaterialChangeCompareService extends BaseService<MaterialChangeCompareDTO, MaterialChangeCompareVO> {

    /**
     * 查询所有
     *
     * @return list {@link MaterialChangeCompareVO}
     */
    List<MaterialChangeCompareVO> selectAll();

    void doCompareCalc();

    void doDeleteAll();
}
