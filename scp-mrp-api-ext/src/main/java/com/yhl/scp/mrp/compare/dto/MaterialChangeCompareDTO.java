package com.yhl.scp.mrp.compare.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <code>MaterialChangeCompareDTO</code>
 * <p>
 * 材料计划变动提醒-材料对比DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-17 16:00:22
 */
@ApiModel(value = "材料计划变动提醒-材料对比DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialChangeCompareDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -29246841523063112L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    private String productCode;
    /**
     * 材料编码
     */
    @ApiModelProperty(value = "材料编码")
    private String materialCode;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    private String vehicleModelCode;
    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;
    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;
    /**
     * 采购需求第1周现数量
     */
    @ApiModelProperty(value = "采购需求第1周现数量")
    private BigDecimal purchaseDemandOneWeekCurrentQuantity;
    /**
     * 采购需求第1周变化量
     */
    @ApiModelProperty(value = "采购需求第1周变化量")
    private BigDecimal purchaseDemandOneWeekChangeQuantity;
    /**
     * 采购需求第2周现数量
     */
    @ApiModelProperty(value = "采购需求第2周现数量")
    private BigDecimal purchaseDemandTwoWeekCurrentQuantity;
    /**
     * 采购需求第2周变化量
     */
    @ApiModelProperty(value = "采购需求第2周变化量")
    private BigDecimal purchaseDemandTwoWeekChangeQuantity;
    /**
     * 采购需求第3周现数量
     */
    @ApiModelProperty(value = "采购需求第3周现数量")
    private BigDecimal purchaseDemandThreeWeekCurrentQuantity;
    /**
     * 采购需求第3周变化量
     */
    @ApiModelProperty(value = "采购需求第3周变化量")
    private BigDecimal purchaseDemandThreeWeekChangeQuantity;
    /**
     * 采购需求第4周现数量
     */
    @ApiModelProperty(value = "采购需求第4周现数量")
    private BigDecimal purchaseDemandFourWeekCurrentQuantity;
    /**
     * 采购需求第4周变化量
     */
    @ApiModelProperty(value = "采购需求第4周变化量")
    private BigDecimal purchaseDemandFourWeekChangeQuantity;
    /**
     * 采购需求第1月现数量
     */
    @ApiModelProperty(value = "采购需求第1月现数量")
    private BigDecimal purchaseDemandOneMonthCurrentQuantity;
    /**
     * 采购需求第1月变化量
     */
    @ApiModelProperty(value = "采购需求第1月变化量")
    private BigDecimal purchaseDemandOneMonthChangeQuantity;
    /**
     * 采购需求第2月现数量
     */
    @ApiModelProperty(value = "采购需求第2月现数量")
    private BigDecimal purchaseDemandTwoMonthCurrentQuantity;
    /**
     * 采购需求第2月变化量
     */
    @ApiModelProperty(value = "采购需求第2月变化量")
    private BigDecimal purchaseDemandTwoMonthChangeQuantity;
    /**
     * 采购需求第3月现数量
     */
    @ApiModelProperty(value = "采购需求第3月现数量")
    private BigDecimal purchaseDemandThreeMonthCurrentQuantity;
    /**
     * 采购需求第3月变化量
     */
    @ApiModelProperty(value = "采购需求第3月变化量")
    private BigDecimal purchaseDemandThreeMonthChangeQuantity;
    /**
     * 采购需求第4月现数量
     */
    @ApiModelProperty(value = "采购需求第4月现数量")
    private BigDecimal purchaseDemandFourMonthCurrentQuantity;
    /**
     * 采购需求第4月变化量
     */
    @ApiModelProperty(value = "采购需求第4月变化量")
    private BigDecimal purchaseDemandFourMonthChangeQuantity;
    /**
     * 采购需求第5月现数量
     */
    @ApiModelProperty(value = "采购需求第5月现数量")
    private BigDecimal purchaseDemandFiveMonthCurrentQuantity;
    /**
     * 采购需求第5月变化量
     */
    @ApiModelProperty(value = "采购需求第5月变化量")
    private BigDecimal purchaseDemandFiveMonthChangeQuantity;
    /**
     * 采购需求第6月现数量
     */
    @ApiModelProperty(value = "采购需求第6月现数量")
    private BigDecimal purchaseDemandSixMonthCurrentQuantity;
    /**
     * 采购需求第6月变化量
     */
    @ApiModelProperty(value = "采购需求第6月变化量")
    private BigDecimal purchaseDemandSixMonthChangeQuantity;
    /**
     * 采购需求第7月现数量
     */
    @ApiModelProperty(value = "采购需求第7月现数量")
    private BigDecimal purchaseDemandSenveMonthCurrentQuantity;
    /**
     * 采购需求第7月变化量
     */
    @ApiModelProperty(value = "采购需求第7月变化量")
    private BigDecimal purchaseDemandSenveMonthChangeQuantity;
    /**
     * 采购需求第8月现数量
     */
    @ApiModelProperty(value = "采购需求第8月现数量")
    private BigDecimal purchaseDemandEightMonthCurrentQuantity;
    /**
     * 采购需求第8月变化量
     */
    @ApiModelProperty(value = "采购需求第8月变化量")
    private BigDecimal purchaseDemandEightMonthChangeQuantity;
    /**
     * 波动率
     */
    @ApiModelProperty(value = "波动率")
    private BigDecimal volatility;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Integer versionValue;



    /**
     *  波动率集合
     */
    private List<BigDecimal> volatilityList = new ArrayList<>();

}
