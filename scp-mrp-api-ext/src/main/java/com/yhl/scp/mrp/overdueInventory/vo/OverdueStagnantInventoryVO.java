package com.yhl.scp.mrp.overdueInventory.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>OverdueStagnantInventoryVO</code>
 * <p>
 * 超期呆滞库存VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-10 08:59:44
 */
@ApiModel(value = "超期呆滞库存VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OverdueStagnantInventoryVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 123453482619363693L;

    /**
     * 组织
     */
    @ApiModelProperty(value = "组织")
    @FieldInterpretation(value = "组织")
    private String stockPointCode;
    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称")
    @FieldInterpretation(value = "组织名称")
    private String stockPointName;
    /**
     * 物料
     */
    @ApiModelProperty(value = "物料")
    @FieldInterpretation(value = "物料")
    private String productCode;
    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    @FieldInterpretation(value = "物料名称")
    private String productName;
    /**
     * 物料类型
     */
    @ApiModelProperty(value = "物料类型")
    @FieldInterpretation(value = "物料类型")
    private String productType;
    /**
     * 物料分类大类
     */
    @ApiModelProperty(value = "物料分类大类")
    @FieldInterpretation(value = "物料分类大类")
    private String materialsMainClassification;
    /**
     * 物料分类小类
     */
    @ApiModelProperty(value = "物料分类小类")
    @FieldInterpretation(value = "物料分类小类")
    private String materialsSecondClassification;
    /**
     * 工艺类型
     */
    @ApiModelProperty(value = "工艺类型")
    @FieldInterpretation(value = "工艺类型")
    private String itemType;
    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    @FieldInterpretation(value = "颜色")
    private String productColor;
    /**
     * 子库存
     */
    @ApiModelProperty(value = "子库存")
    @FieldInterpretation(value = "子库存")
    private String subinventory;
    /**
     * 货位
     */
    @ApiModelProperty(value = "货位")
    @FieldInterpretation(value = "货位")
    private String freightSpace;
    /**
     * 批次
     */
    @ApiModelProperty(value = "批次")
    @FieldInterpretation(value = "批次")
    private String batch;
    /**
     * 现有量
     */
    @ApiModelProperty(value = "现有量")
    @FieldInterpretation(value = "现有量")
    private String currentQuantity;
    /**
     * 入库时间
     */
    @ApiModelProperty(value = "入库时间")
    @FieldInterpretation(value = "入库时间")
    private String assignedTime;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    @FieldInterpretation(value = "最后更新时间")
    private String lastUpdateDate;
    /**
     * 库龄天数
     */
    @ApiModelProperty(value = "库龄天数")
    @FieldInterpretation(value = "库龄天数")
    private String stockAgeDay;
    /**
     * 保质期
     */
    @ApiModelProperty(value = "保质期")
    @FieldInterpretation(value = "保质期")
    private String expireDate;
    /**
     * 距离失效时间
     */
    @ApiModelProperty(value = "距离失效时间")
    @FieldInterpretation(value = "距离失效时间")
    private String distanceEnableDate;
    /**
     * 库龄超期界定天数
     */
    @ApiModelProperty(value = "库龄超期界定天数")
    @FieldInterpretation(value = "库龄超期界定天数")
    private BigDecimal overDeadlineDay;
    /**
     * 超期天数
     */
    @ApiModelProperty(value = "超期天数")
    @FieldInterpretation(value = "超期天数")
    private String overDay;
    /**
     * 筛选范围内需求
     */
    @ApiModelProperty(value = "筛选范围内需求")
    @FieldInterpretation(value = "筛选范围内需求")
    private BigDecimal scopeDemandQuantity;
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @FieldInterpretation(value = "日期")
    private String dDate;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @FieldInterpretation(value = "修改时间")
    private Date modifyTime;

    /**
     * 是否积压
     */
    @ApiModelProperty(value = "是否积压")
    @FieldInterpretation(value = "是否积压")
    private String isBacklog;

    @Override
    public void clean() {

    }

}
