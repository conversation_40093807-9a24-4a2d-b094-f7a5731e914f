package com.yhl.scp.mrp.compare.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mrp.compare.dto.ProductChangeCompareDTO;
import com.yhl.scp.mrp.compare.vo.ProductChangeCompareVO;

import java.util.Date;
import java.util.List;

/**
 * <code>ProductChangeCompare2Service</code>
 * <p>
 * 材料计划变动提醒-产品对比应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-18 09:57:20
 */
public interface ProductChangeCompareService extends BaseService<ProductChangeCompareDTO, ProductChangeCompareVO> {

    /**
     * 查询所有
     *
     * @return list {@link ProductChangeCompareVO}
     */
    List<ProductChangeCompareVO> selectAll();

    void doCompareCalc();

    void doDeleteAll();

    List<List<Date>> groupCollection(List<Date> dateList, int baseSize, int groupCount);

    void doMrpCalc();

}
