package com.yhl.scp.mds.curingTime.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesCuringTime;
import com.yhl.scp.dcp.feign.NewDcpFeign;
import com.yhl.scp.ips.constant.MqConstants;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.curingTime.convertor.MdsCuringTimeConvertor;
import com.yhl.scp.mds.curingTime.domain.entity.MdsCuringTimeDO;
import com.yhl.scp.mds.curingTime.domain.service.MdsCuringTimeDomainService;
import com.yhl.scp.mds.curingTime.dto.MdsCuringTimeDTO;
import com.yhl.scp.mds.curingTime.infrastructure.dao.MdsCuringTimeDao;
import com.yhl.scp.mds.curingTime.infrastructure.po.MdsCuringTimePO;
import com.yhl.scp.mds.curingTime.service.MdsCuringTimeService;
import com.yhl.scp.mds.curingTime.vo.MdsCuringTimeVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.bouncycastle.asn1.x500.style.RFC4519Style.o;

/**
 * <code>MdsCuringTimeServiceImpl</code>
 * <p>
 * mes固化时间接口同步中间表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-24 15:23:15
 */
@Slf4j
@Service
public class MdsCuringTimeServiceImpl extends AbstractService implements MdsCuringTimeService {

    @Resource
    private MdsCuringTimeDao mdsCuringTimeDao;

    @Resource
    private MdsCuringTimeDomainService mdsCuringTimeDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;
    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private RabbitTemplate rabbitTemplate;
    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(MdsCuringTimeDTO mdsCuringTimeDTO) {
        // 0.数据转换
        MdsCuringTimeDO mdsCuringTimeDO = MdsCuringTimeConvertor.INSTANCE.dto2Do(mdsCuringTimeDTO);
        MdsCuringTimePO mdsCuringTimePO = MdsCuringTimeConvertor.INSTANCE.dto2Po(mdsCuringTimeDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        mdsCuringTimeDomainService.validation(mdsCuringTimeDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(mdsCuringTimePO);
        mdsCuringTimeDao.insert(mdsCuringTimePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(MdsCuringTimeDTO mdsCuringTimeDTO) {
        // 0.数据转换
        MdsCuringTimeDO mdsCuringTimeDO = MdsCuringTimeConvertor.INSTANCE.dto2Do(mdsCuringTimeDTO);
        MdsCuringTimePO mdsCuringTimePO = MdsCuringTimeConvertor.INSTANCE.dto2Po(mdsCuringTimeDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        mdsCuringTimeDomainService.validation(mdsCuringTimeDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(mdsCuringTimePO);
        mdsCuringTimeDao.update(mdsCuringTimePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MdsCuringTimeDTO> list) {
        List<MdsCuringTimePO> newList = MdsCuringTimeConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        mdsCuringTimeDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MdsCuringTimeDTO> list) {
        List<MdsCuringTimePO> newList = MdsCuringTimeConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        mdsCuringTimeDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return mdsCuringTimeDao.deleteBatch(idList);
        }
        return mdsCuringTimeDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MdsCuringTimeVO selectByPrimaryKey(String id) {
        MdsCuringTimePO po = mdsCuringTimeDao.selectByPrimaryKey(id);
        return MdsCuringTimeConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MDS_CURING_TIME")
    public List<MdsCuringTimeVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "MDS_CURING_TIME")
    public List<MdsCuringTimeVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MdsCuringTimeVO> dataList = mdsCuringTimeDao.selectByCondition(sortParam, queryCriteriaParam);
        MdsCuringTimeServiceImpl target = SpringBeanUtils.getBean(MdsCuringTimeServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MdsCuringTimeVO> selectByParams(Map<String, Object> params) {
        List<MdsCuringTimePO> list = mdsCuringTimeDao.selectByParams(params);
        return MdsCuringTimeConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MdsCuringTimeVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public BaseResponse<Void> doSync(String scenario) {
        log.info("开始执行固化时间同步任务, 数据源: {}", scenario);
        
        DynamicDataSourceContextHolder.setDataSource(scenario);
        log.info("已切换到指定数据源: {}, 当前数据源: {}", scenario, DynamicDataSourceContextHolder.getDataSource());
        
        try {
            String currentDataSource = DynamicDataSourceContextHolder.getDataSource();
            if (!Objects.equals(scenario, currentDataSource)) {
                log.error("数据源切换失败，期望: {}, 实际: {}", scenario, currentDataSource);
                return BaseResponse.error("数据源切换失败");
            }
            
            // 查询中间表数据
            log.info("准备从中间表查询数据，当前数据源上下文: {}", currentDataSource);
            
            List<MdsCuringTimeVO> mdsCuringTimeVOS;
            try {
                mdsCuringTimeVOS = mdsCuringTimeDao.selectFromMid();
            } catch (Exception e) {
                log.error("查询中间表数据失败，数据源: {}, 错误信息: {}", scenario, e.getMessage(), e);
                throw new RuntimeException("查询中间表数据失败: " + e.getMessage(), e);
            }

        if (CollectionUtils.isEmpty(mdsCuringTimeVOS)) {
            log.error("接口中间表固化时间数据为空，数据源: {}，请检查表 {}.itf_curing_time 是否存在数据", scenario, scenario);
            return BaseResponse.error("接口中间表固化时间数据为空");
        }
        log.info("从中间表查询到 {} 条固化时间数据", mdsCuringTimeVOS.size());
        
        List<String> ids = mdsCuringTimeVOS.stream().map(BaseVO::getId).distinct().collect(Collectors.toList());
        List<String> kids = mdsCuringTimeVOS.stream().map(MdsCuringTimeVO::getKid).distinct().collect(Collectors.toList());
        log.info("提取到 {} 个ID和 {} 个KID", ids.size(), kids.size());
        
        List<MdsCuringTimePO> oldMdsCuringTimePOs = mdsCuringTimeDao.selectByParams(ImmutableMap.of("kids", kids));
        log.info("从现有数据中查询到 {} 条匹配记录", oldMdsCuringTimePOs.size());
        
        List<MdsCuringTimeDTO> insertMdsCuringTimeDTOs = Lists.newArrayList();
        List<MdsCuringTimeDTO> updateMdsCuringTimeDTOs = Lists.newArrayList();
        Map<String, MdsCuringTimePO> oldMdsCuringTimePOMap = oldMdsCuringTimePOs.stream().collect(Collectors.toMap(MdsCuringTimePO::getKid, Function.identity(), (v1, v2) -> v1));
                
        log.info("开始处理数据转换，准备区分新增和更新数据");
        for(MdsCuringTimeVO mesCuringTime : mdsCuringTimeVOS){
            String kid = mesCuringTime.getKid();
            MdsCuringTimeDTO mdsCuringTimeDTO = new MdsCuringTimeDTO();
            if(oldMdsCuringTimePOMap.containsKey(kid)) {
                log.info("已有数据，正在处理数据，kid:{}",kid);
                MdsCuringTimePO mdsCuringTimePO = oldMdsCuringTimePOMap.get(kid);
                mdsCuringTimeDTO=MdsCuringTimeConvertor.INSTANCE.po2Dto(mdsCuringTimePO);
                mdsCuringTimeDTO.setLastUpdateDate(mesCuringTime.getLastUpdateDate());
                mdsCuringTimeDTO.setGhTime(mesCuringTime.getGhTime());
                mdsCuringTimeDTO.setCreateUser(mesCuringTime.getCreateUser());
                mdsCuringTimeDTO.setGhFlag(mesCuringTime.getGhFlag());
                mdsCuringTimeDTO.setCompanyCode(mesCuringTime.getCompanyCode());
                mdsCuringTimeDTO.setStockPointCode(mesCuringTime.getStockPointCode());
                mdsCuringTimeDTO.setProductCode(mesCuringTime.getProductCode());
                mdsCuringTimeDTO.setEnabled(Objects.equals(mesCuringTime.getEnabled(),"Y")?
                        YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                updateMdsCuringTimeDTOs.add(mdsCuringTimeDTO);
            }else{
                log.info("没有找到对应的数据，正在处理新增数据，kid:{}",kid);

                mdsCuringTimeDTO.setKid(kid);
                mdsCuringTimeDTO.setLastUpdateDate(mesCuringTime.getLastUpdateDate());
                mdsCuringTimeDTO.setGhTime(mesCuringTime.getGhTime());
                mdsCuringTimeDTO.setCreateUser(mesCuringTime.getCreateUser());
                mdsCuringTimeDTO.setGhFlag(mesCuringTime.getGhFlag());
                mdsCuringTimeDTO.setCompanyCode(mesCuringTime.getCompanyCode());
                mdsCuringTimeDTO.setStockPointCode(mesCuringTime.getStockPointCode());
                mdsCuringTimeDTO.setProductCode(mesCuringTime.getProductCode());
                mdsCuringTimeDTO.setEnabled(Objects.equals(mesCuringTime.getEnabled(),"Y")?
                        YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                insertMdsCuringTimeDTOs.add(mdsCuringTimeDTO);
            }
        }
        log.info("数据分类完成，新增数据 {} 条，更新数据 {} 条", insertMdsCuringTimeDTOs.size(), updateMdsCuringTimeDTOs.size());
        
        if(CollectionUtils.isNotEmpty(insertMdsCuringTimeDTOs)){
            List<List<MdsCuringTimeDTO>> partition = com.google.common.collect.Lists.partition(insertMdsCuringTimeDTOs, 2500);
            log.info("开始批量插入新增数据，共 {} 批", partition.size());
            for (List<MdsCuringTimeDTO> mdsCuringTimeDTOs : partition) {
                this.doCreateBatch(mdsCuringTimeDTOs);
            }
            log.info("MES固化时间新增数据条数：{}", insertMdsCuringTimeDTOs.size());

        }
        if(CollectionUtils.isNotEmpty(updateMdsCuringTimeDTOs)){
            List<List<MdsCuringTimeDTO>> partition = com.google.common.collect.Lists.partition(updateMdsCuringTimeDTOs, 2500);
            log.info("开始批量更新数据，共 {} 批", partition.size());
            for (List<MdsCuringTimeDTO> mdsCuringTimeDTOs : partition) {
                this.doUpdateBatch(mdsCuringTimeDTOs);
            }
            log.info("MES固化时间更新数据条数：{}", updateMdsCuringTimeDTOs.size());

        }
            String profile = System.getProperty("spring.profiles.active") + ".";
            Map<String,Object> feedBackMap=new HashMap<>();
            feedBackMap.put("scenario",scenario);
            feedBackMap.put("ids",ids);
            log.info("准备发送反馈消息到MQ, exchange: {}, routingKey: {}", profile + MqConstants.BPIM_SYNC_EXCHANGE, profile + MqConstants.CURING_TIME_SYNC_FEEDBACK_KEY);
            rabbitTemplate.convertAndSend(profile + MqConstants.BPIM_SYNC_EXCHANGE, profile + MqConstants.CURING_TIME_SYNC_FEEDBACK_KEY, feedBackMap);
            log.info("固化时间同步任务执行完成");
        } finally {
            DynamicDataSourceContextHolder.clearDataSource();
            log.info("数据源已清除");
        }

        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> handleSyncCuringTime(Scenario scenario) {
        Map<String, Object> apiParamMap = new HashMap<>(2);
        apiParamMap.put("scenario", scenario.getDataBaseName());
        newDcpFeign.callExternalApi(scenario.getTenantId(), ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.CURING_TIME.getCode(), apiParamMap);

        return BaseResponse.success("同步成功");
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<MdsCuringTimeVO> invocation(List<MdsCuringTimeVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
