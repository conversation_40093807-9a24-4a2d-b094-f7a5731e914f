<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.baseResource.infrastructure.dao.PhysicalResourceLogDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mds.baseResource.infrastructure.po.PhysicalResourceLogPO">
        <!--@Table mds_res_physical_resource_log-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="prod_line_code" jdbcType="VARCHAR" property="prodLineCode"/>
        <result column="prod_line_desc" jdbcType="VARCHAR" property="prodLineDesc"/>
        <result column="prod_line_group_code" jdbcType="VARCHAR" property="prodLineGroupCode"/>
        <result column="prod_line_group_desc" jdbcType="VARCHAR" property="prodLineGroupDesc"/>
        <result column="sequence_code" jdbcType="VARCHAR" property="sequenceCode"/>
        <result column="sequence_desc" jdbcType="VARCHAR" property="sequenceDesc"/>
        <result column="prod_line_family_code" jdbcType="VARCHAR" property="prodLineFamilyCode"/>
        <result column="prod_line_family_desc" jdbcType="VARCHAR" property="prodLineFamilyDesc"/>
        <result column="enable_flag" jdbcType="VARCHAR" property="enableFlag"/>
        <result column="plant_code" jdbcType="VARCHAR" property="plantCode"/>
        <result column="plant_desc" jdbcType="VARCHAR" property="plantDesc"/>
        <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mds.baseResource.vo.PhysicalResourceLogVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
id,prod_line_code,prod_line_desc,prod_line_group_code,prod_line_group_desc,sequence_code,sequence_desc,prod_line_family_code,prod_line_family_desc,enable_flag,plant_code,plant_desc,last_update_date,enabled,creator,create_time,modifier,modify_time,remark,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.prodLineCode != null and params.prodLineCode != ''">
                and prod_line_code = #{params.prodLineCode,jdbcType=VARCHAR}
            </if>
            <if test="params.prodLineCodes != null and params.prodLineCodes != ''">
                and prod_line_code in
                <foreach collection="params.prodLineCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.prodLineDesc != null and params.prodLineDesc != ''">
                and prod_line_desc = #{params.prodLineDesc,jdbcType=VARCHAR}
            </if>
            <if test="params.prodLineGroupCode != null and params.prodLineGroupCode != ''">
                and prod_line_group_code = #{params.prodLineGroupCode,jdbcType=VARCHAR}
            </if>
            <if test="params.prodLineGroupCodes != null and params.prodLineGroupCodes != ''">
                and prod_line_group_code in
                <foreach collection="params.prodLineGroupCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.prodLineGroupDesc != null and params.prodLineGroupDesc != ''">
                and prod_line_group_desc = #{params.prodLineGroupDesc,jdbcType=VARCHAR}
            </if>
            <if test="params.sequenceCode != null and params.sequenceCode != ''">
                and sequence_code = #{params.sequenceCode,jdbcType=VARCHAR}
            </if>
            <if test="params.sequenceDesc != null and params.sequenceDesc != ''">
                and sequence_desc = #{params.sequenceDesc,jdbcType=VARCHAR}
            </if>
            <if test="params.prodLineFamilyCode != null and params.prodLineFamilyCode != ''">
                and prod_line_family_code = #{params.prodLineFamilyCode,jdbcType=VARCHAR}
            </if>
            <if test="params.prodLineFamilyCodes != null and params.prodLineFamilyCodes != ''">
                and prod_line_family_code in
                <foreach collection="params.prodLineFamilyCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>

            <if test="params.prodLineFamilyDesc != null and params.prodLineFamilyDesc != ''">
                and prod_line_family_desc = #{params.prodLineFamilyDesc,jdbcType=VARCHAR}
            </if>
            <if test="params.enableFlag != null and params.enableFlag != ''">
                and enable_flag = #{params.enableFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.plantCode != null and params.plantCode != ''">
                and plant_code = #{params.plantCode,jdbcType=VARCHAR}
            </if>
            <if test="params.plantDesc != null and params.plantDesc != ''">
                and plant_desc = #{params.plantDesc,jdbcType=VARCHAR}
            </if>
            <if test="params.lastUpdateDate != null">
                and last_update_date = #{params.lastUpdateDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_res_physical_resource_log
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_res_physical_resource_log
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from mds_res_physical_resource_log
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_res_physical_resource_log
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mds.baseResource.infrastructure.po.PhysicalResourceLogPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mds_res_physical_resource_log(
        id,
        prod_line_code,
        prod_line_desc,
        prod_line_group_code,
        prod_line_group_desc,
        sequence_code,
        sequence_desc,
        prod_line_family_code,
        prod_line_family_desc,
        enable_flag,
        plant_code,
        plant_desc,
        last_update_date,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        remark,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{prodLineCode,jdbcType=VARCHAR},
        #{prodLineDesc,jdbcType=VARCHAR},
        #{prodLineGroupCode,jdbcType=VARCHAR},
        #{prodLineGroupDesc,jdbcType=VARCHAR},
        #{sequenceCode,jdbcType=VARCHAR},
        #{sequenceDesc,jdbcType=VARCHAR},
        #{prodLineFamilyCode,jdbcType=VARCHAR},
        #{prodLineFamilyDesc,jdbcType=VARCHAR},
        #{enableFlag,jdbcType=VARCHAR},
        #{plantCode,jdbcType=VARCHAR},
        #{plantDesc,jdbcType=VARCHAR},
        #{lastUpdateDate,jdbcType=TIMESTAMP},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mds.baseResource.infrastructure.po.PhysicalResourceLogPO">
        insert into mds_res_physical_resource_log(
        id,
        prod_line_code,
        prod_line_desc,
        prod_line_group_code,
        prod_line_group_desc,
        sequence_code,
        sequence_desc,
        prod_line_family_code,
        prod_line_family_desc,
        enable_flag,
        plant_code,
        plant_desc,
        last_update_date,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        remark,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{prodLineCode,jdbcType=VARCHAR},
        #{prodLineDesc,jdbcType=VARCHAR},
        #{prodLineGroupCode,jdbcType=VARCHAR},
        #{prodLineGroupDesc,jdbcType=VARCHAR},
        #{sequenceCode,jdbcType=VARCHAR},
        #{sequenceDesc,jdbcType=VARCHAR},
        #{prodLineFamilyCode,jdbcType=VARCHAR},
        #{prodLineFamilyDesc,jdbcType=VARCHAR},
        #{enableFlag,jdbcType=VARCHAR},
        #{plantCode,jdbcType=VARCHAR},
        #{plantDesc,jdbcType=VARCHAR},
        #{lastUpdateDate,jdbcType=TIMESTAMP},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_res_physical_resource_log(
        id,
        prod_line_code,
        prod_line_desc,
        prod_line_group_code,
        prod_line_group_desc,
        sequence_code,
        sequence_desc,
        prod_line_family_code,
        prod_line_family_desc,
        enable_flag,
        plant_code,
        plant_desc,
        last_update_date,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        remark,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.prodLineCode,jdbcType=VARCHAR},
        #{entity.prodLineDesc,jdbcType=VARCHAR},
        #{entity.prodLineGroupCode,jdbcType=VARCHAR},
        #{entity.prodLineGroupDesc,jdbcType=VARCHAR},
        #{entity.sequenceCode,jdbcType=VARCHAR},
        #{entity.sequenceDesc,jdbcType=VARCHAR},
        #{entity.prodLineFamilyCode,jdbcType=VARCHAR},
        #{entity.prodLineFamilyDesc,jdbcType=VARCHAR},
        #{entity.enableFlag,jdbcType=VARCHAR},
        #{entity.plantCode,jdbcType=VARCHAR},
        #{entity.plantDesc,jdbcType=VARCHAR},
        #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_res_physical_resource_log(
        id,
        prod_line_code,
        prod_line_desc,
        prod_line_group_code,
        prod_line_group_desc,
        sequence_code,
        sequence_desc,
        prod_line_family_code,
        prod_line_family_desc,
        enable_flag,
        plant_code,
        plant_desc,
        last_update_date,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        remark,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.prodLineCode,jdbcType=VARCHAR},
        #{entity.prodLineDesc,jdbcType=VARCHAR},
        #{entity.prodLineGroupCode,jdbcType=VARCHAR},
        #{entity.prodLineGroupDesc,jdbcType=VARCHAR},
        #{entity.sequenceCode,jdbcType=VARCHAR},
        #{entity.sequenceDesc,jdbcType=VARCHAR},
        #{entity.prodLineFamilyCode,jdbcType=VARCHAR},
        #{entity.prodLineFamilyDesc,jdbcType=VARCHAR},
        #{entity.enableFlag,jdbcType=VARCHAR},
        #{entity.plantCode,jdbcType=VARCHAR},
        #{entity.plantDesc,jdbcType=VARCHAR},
        #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mds.baseResource.infrastructure.po.PhysicalResourceLogPO">
        update mds_res_physical_resource_log set
        prod_line_code = #{prodLineCode,jdbcType=VARCHAR},
        prod_line_desc = #{prodLineDesc,jdbcType=VARCHAR},
        prod_line_group_code = #{prodLineGroupCode,jdbcType=VARCHAR},
        prod_line_group_desc = #{prodLineGroupDesc,jdbcType=VARCHAR},
        sequence_code = #{sequenceCode,jdbcType=VARCHAR},
        sequence_desc = #{sequenceDesc,jdbcType=VARCHAR},
        prod_line_family_code = #{prodLineFamilyCode,jdbcType=VARCHAR},
        prod_line_family_desc = #{prodLineFamilyDesc,jdbcType=VARCHAR},
        enable_flag = #{enableFlag,jdbcType=VARCHAR},
        plant_code = #{plantCode,jdbcType=VARCHAR},
        plant_desc = #{plantDesc,jdbcType=VARCHAR},
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
        enabled            = #{enabled,jdbcType=VARCHAR},
        modifier           = #{modifier,jdbcType=VARCHAR},
        modify_time        = #{modifyTime,jdbcType=TIMESTAMP},
        remark             = #{remark,jdbcType=VARCHAR},
        version_value      = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mds.baseResource.infrastructure.po.PhysicalResourceLogPO">
        update mds_res_physical_resource_log
        <set>
            <if test="item.prodLineCode != null and item.prodLineCode != ''">
                prod_line_code = #{item.prodLineCode,jdbcType=VARCHAR},
            </if>
            <if test="item.prodLineDesc != null and item.prodLineDesc != ''">
                prod_line_desc = #{item.prodLineDesc,jdbcType=VARCHAR},
            </if>
            <if test="item.prodLineGroupCode != null and item.prodLineGroupCode != ''">
                prod_line_group_code = #{item.prodLineGroupCode,jdbcType=VARCHAR},
            </if>
            <if test="item.prodLineGroupDesc != null and item.prodLineGroupDesc != ''">
                prod_line_group_desc = #{item.prodLineGroupDesc,jdbcType=VARCHAR},
            </if>
            <if test="item.sequenceCode != null and item.sequenceCode != ''">
                sequence_code = #{item.sequenceCode,jdbcType=VARCHAR},
            </if>
            <if test="item.sequenceDesc != null and item.sequenceDesc != ''">
                sequence_desc = #{item.sequenceDesc,jdbcType=VARCHAR},
            </if>
            <if test="item.prodLineFamilyCode != null and item.prodLineFamilyCode != ''">
                prod_line_family_code = #{item.prodLineFamilyCode,jdbcType=VARCHAR},
            </if>
            <if test="item.prodLineFamilyDesc != null and item.prodLineFamilyDesc != ''">
                prod_line_family_desc = #{item.prodLineFamilyDesc,jdbcType=VARCHAR},
            </if>
            <if test="item.enableFlag != null and item.enableFlag != ''">
                enable_flag = #{item.enableFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.plantCode != null and item.plantCode != ''">
                plant_code = #{item.plantCode,jdbcType=VARCHAR},
            </if>
            <if test="item.plantDesc != null and item.plantDesc != ''">
                plant_desc = #{item.plantDesc,jdbcType=VARCHAR},
            </if>
            <if test="item.lastUpdateDate != null">
                last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_res_physical_resource_log
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="prod_line_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.prodLineCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="prod_line_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.prodLineDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="prod_line_group_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.prodLineGroupCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="prod_line_group_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.prodLineGroupDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sequence_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sequenceCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sequence_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sequenceDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="prod_line_family_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.prodLineFamilyCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="prod_line_family_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.prodLineFamilyDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enable_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enableFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.plantCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plant_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.plantDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="last_update_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lastUpdateDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mds_res_physical_resource_log 
        <set>
            <if test="item.prodLineCode != null and item.prodLineCode != ''">
                prod_line_code = #{item.prodLineCode,jdbcType=VARCHAR},
            </if>
            <if test="item.prodLineDesc != null and item.prodLineDesc != ''">
                prod_line_desc = #{item.prodLineDesc,jdbcType=VARCHAR},
            </if>
            <if test="item.prodLineGroupCode != null and item.prodLineGroupCode != ''">
                prod_line_group_code = #{item.prodLineGroupCode,jdbcType=VARCHAR},
            </if>
            <if test="item.prodLineGroupDesc != null and item.prodLineGroupDesc != ''">
                prod_line_group_desc = #{item.prodLineGroupDesc,jdbcType=VARCHAR},
            </if>
            <if test="item.sequenceCode != null and item.sequenceCode != ''">
                sequence_code = #{item.sequenceCode,jdbcType=VARCHAR},
            </if>
            <if test="item.sequenceDesc != null and item.sequenceDesc != ''">
                sequence_desc = #{item.sequenceDesc,jdbcType=VARCHAR},
            </if>
            <if test="item.prodLineFamilyCode != null and item.prodLineFamilyCode != ''">
                prod_line_family_code = #{item.prodLineFamilyCode,jdbcType=VARCHAR},
            </if>
            <if test="item.prodLineFamilyDesc != null and item.prodLineFamilyDesc != ''">
                prod_line_family_desc = #{item.prodLineFamilyDesc,jdbcType=VARCHAR},
            </if>
            <if test="item.enableFlag != null and item.enableFlag != ''">
                enable_flag = #{item.enableFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.plantCode != null and item.plantCode != ''">
                plant_code = #{item.plantCode,jdbcType=VARCHAR},
            </if>
            <if test="item.plantDesc != null and item.plantDesc != ''">
                plant_desc = #{item.plantDesc,jdbcType=VARCHAR},
            </if>
            <if test="item.lastUpdateDate != null">
                last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mds_res_physical_resource_log where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_res_physical_resource_log where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
