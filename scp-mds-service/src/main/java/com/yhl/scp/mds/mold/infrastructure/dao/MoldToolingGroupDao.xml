<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.mold.infrastructure.dao.MoldToolingGroupDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mds.mold.infrastructure.po.MoldToolingGroupPO">
        <!--@Table mds_mold_tooling_group-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="organization_id" jdbcType="VARCHAR" property="organizationId"/>
        <result column="standard_resource_code" jdbcType="VARCHAR" property="standardResourceCode"/>
        <result column="standard_resource_name" jdbcType="VARCHAR" property="standardResourceName"/>
        <result column="resource_type" jdbcType="VARCHAR" property="resourceType"/>
        <result column="resource_category" jdbcType="VARCHAR" property="resourceCategory"/>
        <result column="resource_classification" jdbcType="VARCHAR" property="resourceClassification"/>
        <result column="capacity_type" jdbcType="VARCHAR" property="capacityType"/>
        <result column="display_index" jdbcType="INTEGER" property="displayIndex"/>
        <result column="production_efficiency" jdbcType="VARCHAR" property="productionEfficiency"/>
        <result column="resource_quantity_coefficient" jdbcType="INTEGER" property="resourceQuantityCoefficient"/>
        <result column="bottleneck" jdbcType="VARCHAR" property="bottleneck"/>
        <result column="infinite_capacity" jdbcType="VARCHAR" property="infiniteCapacity"/>
        <result column="avg_changeover_duration" jdbcType="INTEGER" property="avgChangeoverDuration"/>
        <result column="avg_changeover_cost" jdbcType="VARCHAR" property="avgChangeoverCost"/>
        <result column="labor_cost" jdbcType="VARCHAR" property="laborCost"/>
        <result column="overtime_cost" jdbcType="VARCHAR" property="overtimeCost"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="max_stock_level" jdbcType="VARCHAR" property="maxStockLevel"/>
        <result column="target_stock_level" jdbcType="VARCHAR" property="targetStockLevel"/>
        <result column="min_stock_level" jdbcType="VARCHAR" property="minStockLevel"/>
        <result column="currency_unit_id" jdbcType="VARCHAR" property="currencyUnitId"/>
        <result column="kid" jdbcType="VARCHAR" property="kid"/>
        <result column="production_planner" jdbcType="VARCHAR" property="productionPlanner"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mds.mold.vo.MoldToolingGroupVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,organization_id,standard_resource_code,standard_resource_name,resource_type,resource_category,resource_classification
        ,capacity_type,display_index,production_efficiency,resource_quantity_coefficient,bottleneck,infinite_capacity
        ,avg_changeover_duration,avg_changeover_cost,labor_cost,overtime_cost,remark,enabled,creator,create_time,modifier
        ,modify_time,version_value,max_stock_level,target_stock_level,min_stock_level,currency_unit_id,kid,production_planner
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.organizationId != null and params.organizationId != ''">
                and organization_id = #{params.organizationId,jdbcType=VARCHAR}
            </if>
            <if test="params.standardResourceCode != null and params.standardResourceCode != ''">
                and standard_resource_code = #{params.standardResourceCode,jdbcType=VARCHAR}
            </if>
            <if test="params.standardResourceName != null and params.standardResourceName != ''">
                and standard_resource_name = #{params.standardResourceName,jdbcType=VARCHAR}
            </if>
            <if test="params.resourceType != null and params.resourceType != ''">
                and resource_type = #{params.resourceType,jdbcType=VARCHAR}
            </if>
            <if test="params.resourceCategory != null and params.resourceCategory != ''">
                and resource_category = #{params.resourceCategory,jdbcType=VARCHAR}
            </if>
            <if test="params.resourceClassification != null and params.resourceClassification != ''">
                and resource_classification = #{params.resourceClassification,jdbcType=VARCHAR}
            </if>
            <if test="params.capacityType != null and params.capacityType != ''">
                and capacity_type = #{params.capacityType,jdbcType=VARCHAR}
            </if>
            <if test="params.displayIndex != null">
                and display_index = #{params.displayIndex,jdbcType=INTEGER}
            </if>
            <if test="params.productionEfficiency != null">
                and production_efficiency = #{params.productionEfficiency,jdbcType=VARCHAR}
            </if>
            <if test="params.resourceQuantityCoefficient != null">
                and resource_quantity_coefficient = #{params.resourceQuantityCoefficient,jdbcType=INTEGER}
            </if>
            <if test="params.bottleneck != null and params.bottleneck != ''">
                and bottleneck = #{params.bottleneck,jdbcType=VARCHAR}
            </if>
            <if test="params.infiniteCapacity != null and params.infiniteCapacity != ''">
                and infinite_capacity = #{params.infiniteCapacity,jdbcType=VARCHAR}
            </if>
            <if test="params.avgChangeoverDuration != null">
                and avg_changeover_duration = #{params.avgChangeoverDuration,jdbcType=INTEGER}
            </if>
            <if test="params.avgChangeoverCost != null">
                and avg_changeover_cost = #{params.avgChangeoverCost,jdbcType=VARCHAR}
            </if>
            <if test="params.laborCost != null">
                and labor_cost = #{params.laborCost,jdbcType=VARCHAR}
            </if>
            <if test="params.overtimeCost != null">
                and overtime_cost = #{params.overtimeCost,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGETR}
            </if>
            <if test="params.maxStockLevel != null">
                and max_stock_level = #{params.maxStockLevel,jdbcType=VARCHAR}
            </if>
            <if test="params.targetStockLevel != null">
                and target_stock_level = #{params.targetStockLevel,jdbcType=VARCHAR}
            </if>
            <if test="params.minStockLevel != null">
                and min_stock_level = #{params.minStockLevel,jdbcType=VARCHAR}
            </if>
            <if test="params.currencyUnitId != null and params.currencyUnitId != ''">
                and currency_unit_id = #{params.currencyUnitId,jdbcType=VARCHAR}
            </if>
            <if test="params.kid != null and params.kid != ''">
                and kid = #{params.kid,jdbcType=VARCHAR}
            </if>
            <if test="params.kids != null and params.kids != ''">
                and kid in
                <foreach collection="params.kids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productionPlanner != null and params.productionPlanner != ''">
                and production_planner = #{params.productionPlanner,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_mold_tooling_group
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_mold_tooling_group
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mds_mold_tooling_group
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_mold_tooling_group
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mds.mold.infrastructure.po.MoldToolingGroupPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mds_mold_tooling_group(
        id,
        organization_id,
        standard_resource_code,
        standard_resource_name,
        resource_type,
        resource_category,
        resource_classification,
        capacity_type,
        display_index,
        production_efficiency,
        resource_quantity_coefficient,
        bottleneck,
        infinite_capacity,
        avg_changeover_duration,
        avg_changeover_cost,
        labor_cost,
        overtime_cost,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        max_stock_level,
        target_stock_level,
        min_stock_level,
        currency_unit_id,
        kid,
        production_planner)
        values (
        #{id,jdbcType=VARCHAR},
        #{organizationId,jdbcType=VARCHAR},
        #{standardResourceCode,jdbcType=VARCHAR},
        #{standardResourceName,jdbcType=VARCHAR},
        #{resourceType,jdbcType=VARCHAR},
        #{resourceCategory,jdbcType=VARCHAR},
        #{resourceClassification,jdbcType=VARCHAR},
        #{capacityType,jdbcType=VARCHAR},
        #{displayIndex,jdbcType=INTEGER},
        #{productionEfficiency,jdbcType=VARCHAR},
        #{resourceQuantityCoefficient,jdbcType=INTEGER},
        #{bottleneck,jdbcType=VARCHAR},
        #{infiniteCapacity,jdbcType=VARCHAR},
        #{avgChangeoverDuration,jdbcType=INTEGER},
        #{avgChangeoverCost,jdbcType=VARCHAR},
        #{laborCost,jdbcType=VARCHAR},
        #{overtimeCost,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{maxStockLevel,jdbcType=VARCHAR},
        #{targetStockLevel,jdbcType=VARCHAR},
        #{minStockLevel,jdbcType=VARCHAR},
        #{currencyUnitId,jdbcType=VARCHAR},
        #{kid,jdbcType=VARCHAR},
        #{productionPlanner,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mds.mold.infrastructure.po.MoldToolingGroupPO">
        insert into mds_mold_tooling_group(id,
                                           organization_id,
                                           standard_resource_code,
                                           standard_resource_name,
                                           resource_type,
                                           resource_category,
                                           resource_classification,
                                           capacity_type,
                                           display_index,
                                           production_efficiency,
                                           resource_quantity_coefficient,
                                           bottleneck,
                                           infinite_capacity,
                                           avg_changeover_duration,
                                           avg_changeover_cost,
                                           labor_cost,
                                           overtime_cost,
                                           remark,
                                           enabled,
                                           creator,
                                           create_time,
                                           modifier,
                                           modify_time,
                                           version_value,
                                           max_stock_level,
                                           target_stock_level,
                                           min_stock_level,
                                           currency_unit_id,
                                           kid,
                                           production_planner)
        values (#{id,jdbcType=VARCHAR},
                #{organizationId,jdbcType=VARCHAR},
                #{standardResourceCode,jdbcType=VARCHAR},
                #{standardResourceName,jdbcType=VARCHAR},
                #{resourceType,jdbcType=VARCHAR},
                #{resourceCategory,jdbcType=VARCHAR},
                #{resourceClassification,jdbcType=VARCHAR},
                #{capacityType,jdbcType=VARCHAR},
                #{displayIndex,jdbcType=INTEGER},
                #{productionEfficiency,jdbcType=VARCHAR},
                #{resourceQuantityCoefficient,jdbcType=INTEGER},
                #{bottleneck,jdbcType=VARCHAR},
                #{infiniteCapacity,jdbcType=VARCHAR},
                #{avgChangeoverDuration,jdbcType=INTEGER},
                #{avgChangeoverCost,jdbcType=VARCHAR},
                #{laborCost,jdbcType=VARCHAR},
                #{overtimeCost,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER},
                #{maxStockLevel,jdbcType=VARCHAR},
                #{targetStockLevel,jdbcType=VARCHAR},
                #{minStockLevel,jdbcType=VARCHAR},
                #{currencyUnitId,jdbcType=VARCHAR},
                #{kid,jdbcType=VARCHAR},
                #{productionPlanner,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_mold_tooling_group(
        id,
        organization_id,
        standard_resource_code,
        standard_resource_name,
        resource_type,
        resource_category,
        resource_classification,
        capacity_type,
        display_index,
        production_efficiency,
        resource_quantity_coefficient,
        bottleneck,
        infinite_capacity,
        avg_changeover_duration,
        avg_changeover_cost,
        labor_cost,
        overtime_cost,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        max_stock_level,
        target_stock_level,
        min_stock_level,
        currency_unit_id,
        kid,
        production_planner)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.organizationId,jdbcType=VARCHAR},
            #{entity.standardResourceCode,jdbcType=VARCHAR},
            #{entity.standardResourceName,jdbcType=VARCHAR},
            #{entity.resourceType,jdbcType=VARCHAR},
            #{entity.resourceCategory,jdbcType=VARCHAR},
            #{entity.resourceClassification,jdbcType=VARCHAR},
            #{entity.capacityType,jdbcType=VARCHAR},
            #{entity.displayIndex,jdbcType=INTEGER},
            #{entity.productionEfficiency,jdbcType=VARCHAR},
            #{entity.resourceQuantityCoefficient,jdbcType=INTEGER},
            #{entity.bottleneck,jdbcType=VARCHAR},
            #{entity.infiniteCapacity,jdbcType=VARCHAR},
            #{entity.avgChangeoverDuration,jdbcType=INTEGER},
            #{entity.avgChangeoverCost,jdbcType=VARCHAR},
            #{entity.laborCost,jdbcType=VARCHAR},
            #{entity.overtimeCost,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.maxStockLevel,jdbcType=VARCHAR},
            #{entity.targetStockLevel,jdbcType=VARCHAR},
            #{entity.minStockLevel,jdbcType=VARCHAR},
            #{entity.currencyUnitId,jdbcType=VARCHAR},
            #{entity.kid,jdbcType=VARCHAR},
            #{entity.productionPlanner,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_mold_tooling_group(
        id,
        organization_id,
        standard_resource_code,
        standard_resource_name,
        resource_type,
        resource_category,
        resource_classification,
        capacity_type,
        display_index,
        production_efficiency,
        resource_quantity_coefficient,
        bottleneck,
        infinite_capacity,
        avg_changeover_duration,
        avg_changeover_cost,
        labor_cost,
        overtime_cost,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        max_stock_level,
        target_stock_level,
        min_stock_level,
        currency_unit_id,
        kid,
        production_planner)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.organizationId,jdbcType=VARCHAR},
            #{entity.standardResourceCode,jdbcType=VARCHAR},
            #{entity.standardResourceName,jdbcType=VARCHAR},
            #{entity.resourceType,jdbcType=VARCHAR},
            #{entity.resourceCategory,jdbcType=VARCHAR},
            #{entity.resourceClassification,jdbcType=VARCHAR},
            #{entity.capacityType,jdbcType=VARCHAR},
            #{entity.displayIndex,jdbcType=INTEGER},
            #{entity.productionEfficiency,jdbcType=VARCHAR},
            #{entity.resourceQuantityCoefficient,jdbcType=INTEGER},
            #{entity.bottleneck,jdbcType=VARCHAR},
            #{entity.infiniteCapacity,jdbcType=VARCHAR},
            #{entity.avgChangeoverDuration,jdbcType=INTEGER},
            #{entity.avgChangeoverCost,jdbcType=VARCHAR},
            #{entity.laborCost,jdbcType=VARCHAR},
            #{entity.overtimeCost,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.maxStockLevel,jdbcType=VARCHAR},
            #{entity.targetStockLevel,jdbcType=VARCHAR},
            #{entity.minStockLevel,jdbcType=VARCHAR},
            #{entity.currencyUnitId,jdbcType=VARCHAR},
            #{entity.kid,jdbcType=VARCHAR},
            #{entity.productionPlanner,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mds.mold.infrastructure.po.MoldToolingGroupPO">
        update mds_mold_tooling_group
        set organization_id               = #{organizationId,jdbcType=VARCHAR},
            standard_resource_code        = #{standardResourceCode,jdbcType=VARCHAR},
            standard_resource_name        = #{standardResourceName,jdbcType=VARCHAR},
            resource_type                 = #{resourceType,jdbcType=VARCHAR},
            resource_category             = #{resourceCategory,jdbcType=VARCHAR},
            resource_classification       = #{resourceClassification,jdbcType=VARCHAR},
            capacity_type                 = #{capacityType,jdbcType=VARCHAR},
            display_index                 = #{displayIndex,jdbcType=INTEGER},
            production_efficiency         = #{productionEfficiency,jdbcType=VARCHAR},
            resource_quantity_coefficient = #{resourceQuantityCoefficient,jdbcType=INTEGER},
            bottleneck                    = #{bottleneck,jdbcType=VARCHAR},
            infinite_capacity             = #{infiniteCapacity,jdbcType=VARCHAR},
            avg_changeover_duration       = #{avgChangeoverDuration,jdbcType=INTEGER},
            avg_changeover_cost           = #{avgChangeoverCost,jdbcType=VARCHAR},
            labor_cost                    = #{laborCost,jdbcType=VARCHAR},
            overtime_cost                 = #{overtimeCost,jdbcType=VARCHAR},
            remark                        = #{remark,jdbcType=VARCHAR},
            enabled                       = #{enabled,jdbcType=VARCHAR},
            modifier                      = #{modifier,jdbcType=VARCHAR},
            modify_time                   = #{modifyTime,jdbcType=TIMESTAMP},
            version_value                 = #{versionValue,jdbcType=INTEGER},
            max_stock_level               = #{maxStockLevel,jdbcType=VARCHAR},
            target_stock_level            = #{targetStockLevel,jdbcType=VARCHAR},
            min_stock_level               = #{minStockLevel,jdbcType=VARCHAR},
            currency_unit_id              = #{currencyUnitId,jdbcType=VARCHAR},
            kid                           = #{kid,jdbcType=VARCHAR},
            production_planner            = #{productionPlanner,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mds.mold.infrastructure.po.MoldToolingGroupPO">
        update mds_mold_tooling_group
        <set>
            <if test="item.organizationId != null and item.organizationId != ''">
                organization_id = #{item.organizationId,jdbcType=VARCHAR},
            </if>
            <if test="item.standardResourceCode != null and item.standardResourceCode != ''">
                standard_resource_code = #{item.standardResourceCode,jdbcType=VARCHAR},
            </if>
            <if test="item.standardResourceName != null and item.standardResourceName != ''">
                standard_resource_name = #{item.standardResourceName,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceType != null and item.resourceType != ''">
                resource_type = #{item.resourceType,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceCategory != null and item.resourceCategory != ''">
                resource_category = #{item.resourceCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceClassification != null and item.resourceClassification != ''">
                resource_classification = #{item.resourceClassification,jdbcType=VARCHAR},
            </if>
            <if test="item.capacityType != null and item.capacityType != ''">
                capacity_type = #{item.capacityType,jdbcType=VARCHAR},
            </if>
            <if test="item.displayIndex != null">
                display_index = #{item.displayIndex,jdbcType=INTEGER},
            </if>
            <if test="item.productionEfficiency != null">
                production_efficiency = #{item.productionEfficiency,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceQuantityCoefficient != null">
                resource_quantity_coefficient = #{item.resourceQuantityCoefficient,jdbcType=INTEGER},
            </if>
            <if test="item.bottleneck != null and item.bottleneck != ''">
                bottleneck = #{item.bottleneck,jdbcType=VARCHAR},
            </if>
            <if test="item.infiniteCapacity != null and item.infiniteCapacity != ''">
                infinite_capacity = #{item.infiniteCapacity,jdbcType=VARCHAR},
            </if>
            <if test="item.avgChangeoverDuration != null">
                avg_changeover_duration = #{item.avgChangeoverDuration,jdbcType=INTEGER},
            </if>
            <if test="item.avgChangeoverCost != null">
                avg_changeover_cost = #{item.avgChangeoverCost,jdbcType=VARCHAR},
            </if>
            <if test="item.laborCost != null">
                labor_cost = #{item.laborCost,jdbcType=VARCHAR},
            </if>
            <if test="item.overtimeCost != null">
                overtime_cost = #{item.overtimeCost,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.maxStockLevel != null">
                max_stock_level = #{item.maxStockLevel,jdbcType=VARCHAR},
            </if>
            <if test="item.targetStockLevel != null">
                target_stock_level = #{item.targetStockLevel,jdbcType=VARCHAR},
            </if>
            <if test="item.minStockLevel != null">
                min_stock_level = #{item.minStockLevel,jdbcType=VARCHAR},
            </if>
            <if test="item.currencyUnitId != null and item.currencyUnitId != ''">
                currency_unit_id = #{item.currencyUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.kid != null and item.kid != ''">
                kid = #{item.kid,jdbcType=VARCHAR},
            </if>
            <if test="item.productionPlanner != null and item.productionPlanner != ''">
                production_planner = #{item.productionPlanner,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_mold_tooling_group
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="organization_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.organizationId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="standard_resource_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.standardResourceCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="standard_resource_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.standardResourceName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resource_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.resourceType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resource_category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.resourceCategory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resource_classification = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.resourceClassification,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="capacity_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.capacityType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="display_index = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.displayIndex,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="production_efficiency = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionEfficiency,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resource_quantity_coefficient = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.resourceQuantityCoefficient,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="bottleneck = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.bottleneck,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="infinite_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.infiniteCapacity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="avg_changeover_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.avgChangeoverDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="avg_changeover_cost = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.avgChangeoverCost,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="labor_cost = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.laborCost,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="overtime_cost = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.overtimeCost,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="max_stock_level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxStockLevel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="target_stock_level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.targetStockLevel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="min_stock_level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.minStockLevel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="currency_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.currencyUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="kid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.kid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="production_planner = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionPlanner,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mds_mold_tooling_group
            <set>
                <if test="item.organizationId != null and item.organizationId != ''">
                    organization_id = #{item.organizationId,jdbcType=VARCHAR},
                </if>
                <if test="item.standardResourceCode != null and item.standardResourceCode != ''">
                    standard_resource_code = #{item.standardResourceCode,jdbcType=VARCHAR},
                </if>
                <if test="item.standardResourceName != null and item.standardResourceName != ''">
                    standard_resource_name = #{item.standardResourceName,jdbcType=VARCHAR},
                </if>
                <if test="item.resourceType != null and item.resourceType != ''">
                    resource_type = #{item.resourceType,jdbcType=VARCHAR},
                </if>
                <if test="item.resourceCategory != null and item.resourceCategory != ''">
                    resource_category = #{item.resourceCategory,jdbcType=VARCHAR},
                </if>
                <if test="item.resourceClassification != null and item.resourceClassification != ''">
                    resource_classification = #{item.resourceClassification,jdbcType=VARCHAR},
                </if>
                <if test="item.capacityType != null and item.capacityType != ''">
                    capacity_type = #{item.capacityType,jdbcType=VARCHAR},
                </if>
                <if test="item.displayIndex != null">
                    display_index = #{item.displayIndex,jdbcType=INTEGER},
                </if>
                <if test="item.productionEfficiency != null">
                    production_efficiency = #{item.productionEfficiency,jdbcType=VARCHAR},
                </if>
                <if test="item.resourceQuantityCoefficient != null">
                    resource_quantity_coefficient = #{item.resourceQuantityCoefficient,jdbcType=INTEGER},
                </if>
                <if test="item.bottleneck != null and item.bottleneck != ''">
                    bottleneck = #{item.bottleneck,jdbcType=VARCHAR},
                </if>
                <if test="item.infiniteCapacity != null and item.infiniteCapacity != ''">
                    infinite_capacity = #{item.infiniteCapacity,jdbcType=VARCHAR},
                </if>
                <if test="item.avgChangeoverDuration != null">
                    avg_changeover_duration = #{item.avgChangeoverDuration,jdbcType=INTEGER},
                </if>
                <if test="item.avgChangeoverCost != null">
                    avg_changeover_cost = #{item.avgChangeoverCost,jdbcType=VARCHAR},
                </if>
                <if test="item.laborCost != null">
                    labor_cost = #{item.laborCost,jdbcType=VARCHAR},
                </if>
                <if test="item.overtimeCost != null">
                    overtime_cost = #{item.overtimeCost,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
                <if test="item.maxStockLevel != null">
                    max_stock_level = #{item.maxStockLevel,jdbcType=VARCHAR},
                </if>
                <if test="item.targetStockLevel != null">
                    target_stock_level = #{item.targetStockLevel,jdbcType=VARCHAR},
                </if>
                <if test="item.minStockLevel != null">
                    min_stock_level = #{item.minStockLevel,jdbcType=VARCHAR},
                </if>
                <if test="item.currencyUnitId != null and item.currencyUnitId != ''">
                    currency_unit_id = #{item.currencyUnitId,jdbcType=VARCHAR},
                </if>
                <if test="item.kid != null and item.kid != ''">
                    kid = #{item.kid,jdbcType=VARCHAR},
                </if>
                <if test="item.productionPlanner != null and item.productionPlanner != ''">
                    production_planner = #{item.productionPlanner,jdbcType=VARCHAR},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mds_mold_tooling_group
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_mold_tooling_group where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
