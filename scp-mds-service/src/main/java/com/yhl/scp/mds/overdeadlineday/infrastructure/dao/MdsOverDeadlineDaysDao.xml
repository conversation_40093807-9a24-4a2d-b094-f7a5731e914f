<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.overdeadlineday.infrastructure.dao.MdsOverDeadlineDaysDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mds.overdeadlineday.infrastructure.po.MdsOverDeadlineDaysPO">
        <!--@Table mds_over_deadline_days-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="company_code" jdbcType="VARCHAR" property="companyCode"/>
        <result column="materials_type" jdbcType="VARCHAR" property="materialsType"/>
        <result column="materials_main_classification" jdbcType="VARCHAR" property="materialsMainClassification"/>
        <result column="materials_second_classification" jdbcType="VARCHAR" property="materialsSecondClassification"/>
        <result column="product_type" jdbcType="VARCHAR" property="productType"/>
        <result column="color_code" jdbcType="VARCHAR" property="colorCode"/>
        <result column="over_deadline_day" jdbcType="VARCHAR" property="overDeadlineDay"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mds.overdeadlineday.vo.MdsOverDeadlineDaysVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,stock_point_code,company_code,materials_type,materials_main_classification,materials_second_classification,product_type
        ,color_code,over_deadline_day,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.companyCode != null and params.companyCode != ''">
                and company_code = #{params.companyCode,jdbcType=VARCHAR}
            </if>
            <if test="params.materialsType != null and params.materialsType != ''">
                and materials_type = #{params.materialsType,jdbcType=VARCHAR}
            </if>
            <if test="params.materialsMainClassification != null and params.materialsMainClassification != ''">
                and materials_main_classification = #{params.materialsMainClassification,jdbcType=VARCHAR}
            </if>
            <if test="params.materialsSecondClassification != null and params.materialsSecondClassification != ''">
                and materials_second_classification = #{params.materialsSecondClassification,jdbcType=VARCHAR}
            </if>
            <if test="params.productType != null and params.productType != ''">
                and product_type = #{params.productType,jdbcType=VARCHAR}
            </if>
            <if test="params.colorCode != null and params.colorCode != ''">
                and color_code = #{params.colorCode,jdbcType=VARCHAR}
            </if>
            <if test="params.overDeadlineDay != null">
                and over_deadline_day = #{params.overDeadlineDay,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_over_deadline_days
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_over_deadline_days
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mds_over_deadline_days
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_over_deadline_days
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mds.overdeadlineday.infrastructure.po.MdsOverDeadlineDaysPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mds_over_deadline_days(
        id,
        stock_point_code,
        company_code,
        materials_type,
        materials_main_classification,
        materials_second_classification,
        product_type,
        color_code,
        over_deadline_day,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{companyCode,jdbcType=VARCHAR},
        #{materialsType,jdbcType=VARCHAR},
        #{materialsMainClassification,jdbcType=VARCHAR},
        #{materialsSecondClassification,jdbcType=VARCHAR},
        #{productType,jdbcType=VARCHAR},
        #{colorCode,jdbcType=VARCHAR},
        #{overDeadlineDay,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mds.overdeadlineday.infrastructure.po.MdsOverDeadlineDaysPO">
        insert into mds_over_deadline_days(id,
                                           stock_point_code,
                                           company_code,
                                           materials_type,
                                           materials_main_classification,
                                           materials_second_classification,
                                           product_type,
                                           color_code,
                                           over_deadline_day,
                                           remark,
                                           enabled,
                                           creator,
                                           create_time,
                                           modifier,
                                           modify_time,
                                           version_value)
        values (#{id,jdbcType=VARCHAR},
                #{stockPointCode,jdbcType=VARCHAR},
                #{companyCode,jdbcType=VARCHAR},
                #{materialsType,jdbcType=VARCHAR},
                #{materialsMainClassification,jdbcType=VARCHAR},
                #{materialsSecondClassification,jdbcType=VARCHAR},
                #{productType,jdbcType=VARCHAR},
                #{colorCode,jdbcType=VARCHAR},
                #{overDeadlineDay,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_over_deadline_days(
        id,
        stock_point_code,
        company_code,
        materials_type,
        materials_main_classification,
        materials_second_classification,
        product_type,
        color_code,
        over_deadline_day,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.companyCode,jdbcType=VARCHAR},
            #{entity.materialsType,jdbcType=VARCHAR},
            #{entity.materialsMainClassification,jdbcType=VARCHAR},
            #{entity.materialsSecondClassification,jdbcType=VARCHAR},
            #{entity.productType,jdbcType=VARCHAR},
            #{entity.colorCode,jdbcType=VARCHAR},
            #{entity.overDeadlineDay,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_over_deadline_days(
        id,
        stock_point_code,
        company_code,
        materials_type,
        materials_main_classification,
        materials_second_classification,
        product_type,
        color_code,
        over_deadline_day,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.companyCode,jdbcType=VARCHAR},
            #{entity.materialsType,jdbcType=VARCHAR},
            #{entity.materialsMainClassification,jdbcType=VARCHAR},
            #{entity.materialsSecondClassification,jdbcType=VARCHAR},
            #{entity.productType,jdbcType=VARCHAR},
            #{entity.colorCode,jdbcType=VARCHAR},
            #{entity.overDeadlineDay,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mds.overdeadlineday.infrastructure.po.MdsOverDeadlineDaysPO">
        update mds_over_deadline_days
        set stock_point_code                = #{stockPointCode,jdbcType=VARCHAR},
            company_code                    = #{companyCode,jdbcType=VARCHAR},
            materials_type                  = #{materialsType,jdbcType=VARCHAR},
            materials_main_classification   = #{materialsMainClassification,jdbcType=VARCHAR},
            materials_second_classification = #{materialsSecondClassification,jdbcType=VARCHAR},
            product_type                    = #{productType,jdbcType=VARCHAR},
            color_code                      = #{colorCode,jdbcType=VARCHAR},
            over_deadline_day               = #{overDeadlineDay,jdbcType=VARCHAR},
            remark                          = #{remark,jdbcType=VARCHAR},
            enabled                         = #{enabled,jdbcType=VARCHAR},
            modifier                        = #{modifier,jdbcType=VARCHAR},
            modify_time                     = #{modifyTime,jdbcType=TIMESTAMP},
            version_value                   = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mds.overdeadlineday.infrastructure.po.MdsOverDeadlineDaysPO">
        update mds_over_deadline_days
        <set>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.companyCode != null and item.companyCode != ''">
                company_code = #{item.companyCode,jdbcType=VARCHAR},
            </if>
            <if test="item.materialsType != null and item.materialsType != ''">
                materials_type = #{item.materialsType,jdbcType=VARCHAR},
            </if>
            <if test="item.materialsMainClassification != null and item.materialsMainClassification != ''">
                materials_main_classification = #{item.materialsMainClassification,jdbcType=VARCHAR},
            </if>
            <if test="item.materialsSecondClassification != null and item.materialsSecondClassification != ''">
                materials_second_classification = #{item.materialsSecondClassification,jdbcType=VARCHAR},
            </if>
            <if test="item.productType != null and item.productType != ''">
                product_type = #{item.productType,jdbcType=VARCHAR},
            </if>
            <if test="item.colorCode != null and item.colorCode != ''">
                color_code = #{item.colorCode,jdbcType=VARCHAR},
            </if>
            <if test="item.overDeadlineDay != null">
                over_deadline_day = #{item.overDeadlineDay,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_over_deadline_days
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="company_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.companyCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="materials_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialsType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="materials_main_classification = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialsMainClassification,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="materials_second_classification = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialsSecondClassification,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="color_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.colorCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="over_deadline_day = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.overDeadlineDay,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mds_over_deadline_days
            <set>
                <if test="item.stockPointCode != null and item.stockPointCode != ''">
                    stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
                </if>
                <if test="item.companyCode != null and item.companyCode != ''">
                    company_code = #{item.companyCode,jdbcType=VARCHAR},
                </if>
                <if test="item.materialsType != null and item.materialsType != ''">
                    materials_type = #{item.materialsType,jdbcType=VARCHAR},
                </if>
                <if test="item.materialsMainClassification != null and item.materialsMainClassification != ''">
                    materials_main_classification = #{item.materialsMainClassification,jdbcType=VARCHAR},
                </if>
                <if test="item.materialsSecondClassification != null and item.materialsSecondClassification != ''">
                    materials_second_classification = #{item.materialsSecondClassification,jdbcType=VARCHAR},
                </if>
                <if test="item.productType != null and item.productType != ''">
                    product_type = #{item.productType,jdbcType=VARCHAR},
                </if>
                <if test="item.colorCode != null and item.colorCode != ''">
                    color_code = #{item.colorCode,jdbcType=VARCHAR},
                </if>
                <if test="item.overDeadlineDay != null">
                    over_deadline_day = #{item.overDeadlineDay,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mds_over_deadline_days
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_over_deadline_days where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
