package com.yhl.scp.mds.mq.receiver;

import com.yhl.scp.ips.constant.MqConstants;
import com.yhl.scp.mds.curingTime.service.MdsCuringTimeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class MqMessageReceiver {

    @Resource
    private MdsCuringTimeService mdsCuringTimeService;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "${spring.profiles.active}."+ MqConstants.CURING_TIME_SYNC_QUEUE, ignoreDeclarationExceptions = "true"),
            exchange = @Exchange(value = "${spring.profiles.active}."+MqConstants.BPIM_SYNC_EXCHANGE, ignoreDeclarationExceptions = "true", type = ExchangeTypes.TOPIC),
            key = "${spring.profiles.active}."+MqConstants.CURING_TIME_SYNC_KEY
    ))
    public void receiveCuringTimeSyncMessage(String scenario) {
        log.info("接收到固化时间同步消息，场景: {}", scenario);
        try {
            log.info("开始处理固化时间同步任务，数据源: {}", scenario);
            mdsCuringTimeService.doSync(scenario);
            log.info("固化时间同步任务处理完成，数据源: {}", scenario);
        } catch (Exception e) {
            log.error("处理固化时间同步消息失败，场景: {}，错误信息: {}", scenario, e.getMessage(), e);
        }
    }
}