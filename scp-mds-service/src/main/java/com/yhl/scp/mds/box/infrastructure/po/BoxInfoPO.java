package com.yhl.scp.mds.box.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>BoxInfoPO</code>
 * <p>
 * 箱体信息PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:46:36
 */
public class BoxInfoPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 648505015276382194L;

    /**
     * 箱子类型：铁箱/纸箱
     */
    private String boxType;
    /**
     * 箱子编码
     */
    private String boxCode;
    /**
     * 单箱片数
     */
    private Integer piecePerBox;
    /**
     * 单排箱数
     */
    private Integer boxPerRow;
    /**
     * 单垛数量
     */
    private Integer perStackQuantity;
    /**
     * 箱数
     */
    private Integer boxQuantity;
    /**
     * 箱长
     */
    private BigDecimal boxLength;
    /**
     * 箱宽
     */
    private BigDecimal boxWidth;
    /**
     * 箱高
     */
    private BigDecimal boxHeight;
    /**
     * 库存点ID
     */
    private String stockPointId;
    /**
     * kid
     */
    private String kid;
    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;
    /**
     * 计划区域
     */
    private String planArea;

    public String getBoxType() {
        return boxType;
    }

    public void setBoxType(String boxType) {
        this.boxType = boxType;
    }

    public String getBoxCode() {
        return boxCode;
    }

    public void setBoxCode(String boxCode) {
        this.boxCode = boxCode;
    }

    public Integer getPiecePerBox() {
        return piecePerBox;
    }

    public void setPiecePerBox(Integer piecePerBox) {
        this.piecePerBox = piecePerBox;
    }

    public Integer getBoxPerRow() {
        return boxPerRow;
    }

    public void setBoxPerRow(Integer boxPerRow) {
        this.boxPerRow = boxPerRow;
    }

    public Integer getPerStackQuantity() {
        return perStackQuantity;
    }

    public void setPerStackQuantity(Integer perStackQuantity) {
        this.perStackQuantity = perStackQuantity;
    }

    public Integer getBoxQuantity() {
        return boxQuantity;
    }

    public void setBoxQuantity(Integer boxQuantity) {
        this.boxQuantity = boxQuantity;
    }

    public BigDecimal getBoxLength() {
        return boxLength;
    }

    public void setBoxLength(BigDecimal boxLength) {
        this.boxLength = boxLength;
    }

    public BigDecimal getBoxWidth() {
        return boxWidth;
    }

    public void setBoxWidth(BigDecimal boxWidth) {
        this.boxWidth = boxWidth;
    }

    public BigDecimal getBoxHeight() {
        return boxHeight;
    }

    public void setBoxHeight(BigDecimal boxHeight) {
        this.boxHeight = boxHeight;
    }

    public String getStockPointId() {
        return stockPointId;
    }

    public void setStockPointId(String stockPointId) {
        this.stockPointId = stockPointId;
    }

    public String getKid() {
        return kid;
    }

    public void setKid(String kid) {
        this.kid = kid;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getPlanArea() {
        return planArea;
    }

    public void setPlanArea(String planArea) {
        this.planArea = planArea;
    }
}
