package com.yhl.scp.mds.curingTime.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mds.curingTime.infrastructure.po.MdsCuringTimePO;
import com.yhl.scp.mds.curingTime.vo.MdsCuringTimeVO;

import java.util.List;

/**
 * <code>MdsCuringTimeDao</code>
 * <p>
 * mes固化时间接口同步中间表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-24 15:23:15
 */
public interface MdsCuringTimeDao extends BaseDao<MdsCuringTimePO, MdsCuringTimeVO> {
    List<MdsCuringTimeVO> selectFromMid();
}
