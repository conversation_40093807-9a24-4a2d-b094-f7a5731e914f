<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.production.infrastructure.dao.NewProductionOrganizationDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mds.production.infrastructure.po.NewProductionOrganizationPO">
        <!--@Table mds_org_production_organization-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="organization_code" jdbcType="VARCHAR" property="organizationCode"/>
        <result column="organization_name" jdbcType="VARCHAR" property="organizationName"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="display_index" jdbcType="INTEGER" property="displayIndex"/>
        <result column="sales_segment_id" jdbcType="VARCHAR" property="salesSegmentId"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="capacity_type" jdbcType="VARCHAR" property="capacityType"/>
        <result column="capacity_balance_type" jdbcType="VARCHAR" property="capacityBalanceType"/>
        <result column="calculation_type" jdbcType="VARCHAR" property="calculationType"/>
        <result column="plan_level" jdbcType="VARCHAR" property="planLevel"/>
        <result column="infinite_capacity" jdbcType="VARCHAR" property="infiniteCapacity"/>
        <result column="capacity_smooth" jdbcType="VARCHAR" property="capacitySmooth"/>
        <result column="capacity_smooth_delta" jdbcType="VARCHAR" property="capacitySmoothDelta"/>
        <result column="capacity_smooth_period_num" jdbcType="INTEGER" property="capacitySmoothPeriodNum"/>
        <result column="planning_frozen_duration" jdbcType="INTEGER" property="planningFrozenDuration"/>
        <result column="measurement_unit_id" jdbcType="VARCHAR" property="measurementUnitId"/>
        <result column="currency_id" jdbcType="VARCHAR" property="currencyId"/>
        <result column="lot_size" jdbcType="VARCHAR" property="lotSize"/>
        <result column="max_quantity" jdbcType="VARCHAR" property="maxQuantity"/>
        <result column="min_quantity" jdbcType="VARCHAR" property="minQuantity"/>
        <result column="country" jdbcType="VARCHAR" property="country"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="postal_code" jdbcType="VARCHAR" property="postalCode"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="longitude" jdbcType="VARCHAR" property="longitude"/>
        <result column="latitude" jdbcType="VARCHAR" property="latitude"/>
        <result column="depth" jdbcType="INTEGER" property="depth"/>
        <result column="effective_time" jdbcType="TIMESTAMP" property="effectiveTime"/>
        <result column="expiry_time" jdbcType="TIMESTAMP" property="expiryTime"/>
        <result column="counting_unit_id" jdbcType="VARCHAR" property="countingUnitId"/>
        <result column="currency_unit_id" jdbcType="VARCHAR" property="currencyUnitId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mds.production.vo.NewProductionOrganizationVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,organization_code,organization_name,category,display_index,sales_segment_id,parent_id,capacity_type
        ,capacity_balance_type,calculation_type,plan_level,infinite_capacity,capacity_smooth,capacity_smooth_delta
        ,capacity_smooth_period_num,planning_frozen_duration,measurement_unit_id,currency_id,lot_size,max_quantity
        ,min_quantity,country,area,province,city,postal_code,address,longitude,latitude,depth,effective_time
        ,expiry_time,counting_unit_id,currency_unit_id,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.organizationCode != null and params.organizationCode != ''">
                and organization_code = #{params.organizationCode,jdbcType=VARCHAR}
            </if>
            <if test="params.organizationName != null and params.organizationName != ''">
                and organization_name = #{params.organizationName,jdbcType=VARCHAR}
            </if>
            <if test="params.category != null and params.category != ''">
                and category = #{params.category,jdbcType=VARCHAR}
            </if>
            <if test="params.displayIndex != null">
                and display_index = #{params.displayIndex,jdbcType=INTEGER}
            </if>
            <if test="params.salesSegmentId != null and params.salesSegmentId != ''">
                and sales_segment_id = #{params.salesSegmentId,jdbcType=VARCHAR}
            </if>
            <if test="params.parentId != null and params.parentId != ''">
                and parent_id = #{params.parentId,jdbcType=VARCHAR}
            </if>
            <if test="params.capacityType != null and params.capacityType != ''">
                and capacity_type = #{params.capacityType,jdbcType=VARCHAR}
            </if>
            <if test="params.capacityBalanceType != null and params.capacityBalanceType != ''">
                and capacity_balance_type = #{params.capacityBalanceType,jdbcType=VARCHAR}
            </if>
            <if test="params.calculationType != null and params.calculationType != ''">
                and calculation_type = #{params.calculationType,jdbcType=VARCHAR}
            </if>
            <if test="params.planLevel != null and params.planLevel != ''">
                and plan_level = #{params.planLevel,jdbcType=VARCHAR}
            </if>
            <if test="params.infiniteCapacity != null and params.infiniteCapacity != ''">
                and infinite_capacity = #{params.infiniteCapacity,jdbcType=VARCHAR}
            </if>
            <if test="params.capacitySmooth != null and params.capacitySmooth != ''">
                and capacity_smooth = #{params.capacitySmooth,jdbcType=VARCHAR}
            </if>
            <if test="params.capacitySmoothDelta != null">
                and capacity_smooth_delta = #{params.capacitySmoothDelta,jdbcType=VARCHAR}
            </if>
            <if test="params.capacitySmoothPeriodNum != null">
                and capacity_smooth_period_num = #{params.capacitySmoothPeriodNum,jdbcType=INTEGER}
            </if>
            <if test="params.planningFrozenDuration != null">
                and planning_frozen_duration = #{params.planningFrozenDuration,jdbcType=INTEGER}
            </if>
            <if test="params.measurementUnitId != null and params.measurementUnitId != ''">
                and measurement_unit_id = #{params.measurementUnitId,jdbcType=VARCHAR}
            </if>
            <if test="params.currencyId != null and params.currencyId != ''">
                and currency_id = #{params.currencyId,jdbcType=VARCHAR}
            </if>
            <if test="params.lotSize != null">
                and lot_size = #{params.lotSize,jdbcType=VARCHAR}
            </if>
            <if test="params.maxQuantity != null">
                and max_quantity = #{params.maxQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.minQuantity != null">
                and min_quantity = #{params.minQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.country != null and params.country != ''">
                and country = #{params.country,jdbcType=VARCHAR}
            </if>
            <if test="params.area != null and params.area != ''">
                and area = #{params.area,jdbcType=VARCHAR}
            </if>
            <if test="params.province != null and params.province != ''">
                and province = #{params.province,jdbcType=VARCHAR}
            </if>
            <if test="params.city != null and params.city != ''">
                and city = #{params.city,jdbcType=VARCHAR}
            </if>
            <if test="params.postalCode != null and params.postalCode != ''">
                and postal_code = #{params.postalCode,jdbcType=VARCHAR}
            </if>
            <if test="params.address != null and params.address != ''">
                and address = #{params.address,jdbcType=VARCHAR}
            </if>
            <if test="params.longitude != null and params.longitude != ''">
                and longitude = #{params.longitude,jdbcType=VARCHAR}
            </if>
            <if test="params.latitude != null and params.latitude != ''">
                and latitude = #{params.latitude,jdbcType=VARCHAR}
            </if>
            <if test="params.depth != null">
                and depth = #{params.depth,jdbcType=INTEGER}
            </if>
            <if test="params.effectiveTime != null">
                and effective_time = #{params.effectiveTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.expiryTime != null">
                and expiry_time = #{params.expiryTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.countingUnitId != null and params.countingUnitId != ''">
                and counting_unit_id = #{params.countingUnitId,jdbcType=VARCHAR}
            </if>
            <if test="params.currencyUnitId != null and params.currencyUnitId != ''">
                and currency_unit_id = #{params.currencyUnitId,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_org_production_organization
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_org_production_organization
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mds_org_production_organization
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_org_production_organization
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mds.production.infrastructure.po.NewProductionOrganizationPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mds_org_production_organization(
        id,
        organization_code,
        organization_name,
        category,
        display_index,
        sales_segment_id,
        parent_id,
        capacity_type,
        capacity_balance_type,
        calculation_type,
        plan_level,
        infinite_capacity,
        capacity_smooth,
        capacity_smooth_delta,
        capacity_smooth_period_num,
        planning_frozen_duration,
        measurement_unit_id,
        currency_id,
        lot_size,
        max_quantity,
        min_quantity,
        country,
        area,
        province,
        city,
        postal_code,
        address,
        longitude,
        latitude,
        depth,
        effective_time,
        expiry_time,
        counting_unit_id,
        currency_unit_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{organizationCode,jdbcType=VARCHAR},
        #{organizationName,jdbcType=VARCHAR},
        #{category,jdbcType=VARCHAR},
        #{displayIndex,jdbcType=INTEGER},
        #{salesSegmentId,jdbcType=VARCHAR},
        #{parentId,jdbcType=VARCHAR},
        #{capacityType,jdbcType=VARCHAR},
        #{capacityBalanceType,jdbcType=VARCHAR},
        #{calculationType,jdbcType=VARCHAR},
        #{planLevel,jdbcType=VARCHAR},
        #{infiniteCapacity,jdbcType=VARCHAR},
        #{capacitySmooth,jdbcType=VARCHAR},
        #{capacitySmoothDelta,jdbcType=VARCHAR},
        #{capacitySmoothPeriodNum,jdbcType=INTEGER},
        #{planningFrozenDuration,jdbcType=INTEGER},
        #{measurementUnitId,jdbcType=VARCHAR},
        #{currencyId,jdbcType=VARCHAR},
        #{lotSize,jdbcType=VARCHAR},
        #{maxQuantity,jdbcType=VARCHAR},
        #{minQuantity,jdbcType=VARCHAR},
        #{country,jdbcType=VARCHAR},
        #{area,jdbcType=VARCHAR},
        #{province,jdbcType=VARCHAR},
        #{city,jdbcType=VARCHAR},
        #{postalCode,jdbcType=VARCHAR},
        #{address,jdbcType=VARCHAR},
        #{longitude,jdbcType=VARCHAR},
        #{latitude,jdbcType=VARCHAR},
        #{depth,jdbcType=INTEGER},
        #{effectiveTime,jdbcType=TIMESTAMP},
        #{expiryTime,jdbcType=TIMESTAMP},
        #{countingUnitId,jdbcType=VARCHAR},
        #{currencyUnitId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mds.production.infrastructure.po.NewProductionOrganizationPO">
        insert into mds_org_production_organization(id,
                                                    organization_code,
                                                    organization_name,
                                                    category,
                                                    display_index,
                                                    sales_segment_id,
                                                    parent_id,
                                                    capacity_type,
                                                    capacity_balance_type,
                                                    calculation_type,
                                                    plan_level,
                                                    infinite_capacity,
                                                    capacity_smooth,
                                                    capacity_smooth_delta,
                                                    capacity_smooth_period_num,
                                                    planning_frozen_duration,
                                                    measurement_unit_id,
                                                    currency_id,
                                                    lot_size,
                                                    max_quantity,
                                                    min_quantity,
                                                    country,
                                                    area,
                                                    province,
                                                    city,
                                                    postal_code,
                                                    address,
                                                    longitude,
                                                    latitude,
                                                    depth,
                                                    effective_time,
                                                    expiry_time,
                                                    counting_unit_id,
                                                    currency_unit_id,
                                                    remark,
                                                    enabled,
                                                    creator,
                                                    create_time,
                                                    modifier,
                                                    modify_time,
                                                    version_value)
        values (#{id,jdbcType=VARCHAR},
                #{organizationCode,jdbcType=VARCHAR},
                #{organizationName,jdbcType=VARCHAR},
                #{category,jdbcType=VARCHAR},
                #{displayIndex,jdbcType=INTEGER},
                #{salesSegmentId,jdbcType=VARCHAR},
                #{parentId,jdbcType=VARCHAR},
                #{capacityType,jdbcType=VARCHAR},
                #{capacityBalanceType,jdbcType=VARCHAR},
                #{calculationType,jdbcType=VARCHAR},
                #{planLevel,jdbcType=VARCHAR},
                #{infiniteCapacity,jdbcType=VARCHAR},
                #{capacitySmooth,jdbcType=VARCHAR},
                #{capacitySmoothDelta,jdbcType=VARCHAR},
                #{capacitySmoothPeriodNum,jdbcType=INTEGER},
                #{planningFrozenDuration,jdbcType=INTEGER},
                #{measurementUnitId,jdbcType=VARCHAR},
                #{currencyId,jdbcType=VARCHAR},
                #{lotSize,jdbcType=VARCHAR},
                #{maxQuantity,jdbcType=VARCHAR},
                #{minQuantity,jdbcType=VARCHAR},
                #{country,jdbcType=VARCHAR},
                #{area,jdbcType=VARCHAR},
                #{province,jdbcType=VARCHAR},
                #{city,jdbcType=VARCHAR},
                #{postalCode,jdbcType=VARCHAR},
                #{address,jdbcType=VARCHAR},
                #{longitude,jdbcType=VARCHAR},
                #{latitude,jdbcType=VARCHAR},
                #{depth,jdbcType=INTEGER},
                #{effectiveTime,jdbcType=TIMESTAMP},
                #{expiryTime,jdbcType=TIMESTAMP},
                #{countingUnitId,jdbcType=VARCHAR},
                #{currencyUnitId,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_org_production_organization(
        id,
        organization_code,
        organization_name,
        category,
        display_index,
        sales_segment_id,
        parent_id,
        capacity_type,
        capacity_balance_type,
        calculation_type,
        plan_level,
        infinite_capacity,
        capacity_smooth,
        capacity_smooth_delta,
        capacity_smooth_period_num,
        planning_frozen_duration,
        measurement_unit_id,
        currency_id,
        lot_size,
        max_quantity,
        min_quantity,
        country,
        area,
        province,
        city,
        postal_code,
        address,
        longitude,
        latitude,
        depth,
        effective_time,
        expiry_time,
        counting_unit_id,
        currency_unit_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.organizationCode,jdbcType=VARCHAR},
            #{entity.organizationName,jdbcType=VARCHAR},
            #{entity.category,jdbcType=VARCHAR},
            #{entity.displayIndex,jdbcType=INTEGER},
            #{entity.salesSegmentId,jdbcType=VARCHAR},
            #{entity.parentId,jdbcType=VARCHAR},
            #{entity.capacityType,jdbcType=VARCHAR},
            #{entity.capacityBalanceType,jdbcType=VARCHAR},
            #{entity.calculationType,jdbcType=VARCHAR},
            #{entity.planLevel,jdbcType=VARCHAR},
            #{entity.infiniteCapacity,jdbcType=VARCHAR},
            #{entity.capacitySmooth,jdbcType=VARCHAR},
            #{entity.capacitySmoothDelta,jdbcType=VARCHAR},
            #{entity.capacitySmoothPeriodNum,jdbcType=INTEGER},
            #{entity.planningFrozenDuration,jdbcType=INTEGER},
            #{entity.measurementUnitId,jdbcType=VARCHAR},
            #{entity.currencyId,jdbcType=VARCHAR},
            #{entity.lotSize,jdbcType=VARCHAR},
            #{entity.maxQuantity,jdbcType=VARCHAR},
            #{entity.minQuantity,jdbcType=VARCHAR},
            #{entity.country,jdbcType=VARCHAR},
            #{entity.area,jdbcType=VARCHAR},
            #{entity.province,jdbcType=VARCHAR},
            #{entity.city,jdbcType=VARCHAR},
            #{entity.postalCode,jdbcType=VARCHAR},
            #{entity.address,jdbcType=VARCHAR},
            #{entity.longitude,jdbcType=VARCHAR},
            #{entity.latitude,jdbcType=VARCHAR},
            #{entity.depth,jdbcType=INTEGER},
            #{entity.effectiveTime,jdbcType=TIMESTAMP},
            #{entity.expiryTime,jdbcType=TIMESTAMP},
            #{entity.countingUnitId,jdbcType=VARCHAR},
            #{entity.currencyUnitId,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_org_production_organization(
        id,
        organization_code,
        organization_name,
        category,
        display_index,
        sales_segment_id,
        parent_id,
        capacity_type,
        capacity_balance_type,
        calculation_type,
        plan_level,
        infinite_capacity,
        capacity_smooth,
        capacity_smooth_delta,
        capacity_smooth_period_num,
        planning_frozen_duration,
        measurement_unit_id,
        currency_id,
        lot_size,
        max_quantity,
        min_quantity,
        country,
        area,
        province,
        city,
        postal_code,
        address,
        longitude,
        latitude,
        depth,
        effective_time,
        expiry_time,
        counting_unit_id,
        currency_unit_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.organizationCode,jdbcType=VARCHAR},
            #{entity.organizationName,jdbcType=VARCHAR},
            #{entity.category,jdbcType=VARCHAR},
            #{entity.displayIndex,jdbcType=INTEGER},
            #{entity.salesSegmentId,jdbcType=VARCHAR},
            #{entity.parentId,jdbcType=VARCHAR},
            #{entity.capacityType,jdbcType=VARCHAR},
            #{entity.capacityBalanceType,jdbcType=VARCHAR},
            #{entity.calculationType,jdbcType=VARCHAR},
            #{entity.planLevel,jdbcType=VARCHAR},
            #{entity.infiniteCapacity,jdbcType=VARCHAR},
            #{entity.capacitySmooth,jdbcType=VARCHAR},
            #{entity.capacitySmoothDelta,jdbcType=VARCHAR},
            #{entity.capacitySmoothPeriodNum,jdbcType=INTEGER},
            #{entity.planningFrozenDuration,jdbcType=INTEGER},
            #{entity.measurementUnitId,jdbcType=VARCHAR},
            #{entity.currencyId,jdbcType=VARCHAR},
            #{entity.lotSize,jdbcType=VARCHAR},
            #{entity.maxQuantity,jdbcType=VARCHAR},
            #{entity.minQuantity,jdbcType=VARCHAR},
            #{entity.country,jdbcType=VARCHAR},
            #{entity.area,jdbcType=VARCHAR},
            #{entity.province,jdbcType=VARCHAR},
            #{entity.city,jdbcType=VARCHAR},
            #{entity.postalCode,jdbcType=VARCHAR},
            #{entity.address,jdbcType=VARCHAR},
            #{entity.longitude,jdbcType=VARCHAR},
            #{entity.latitude,jdbcType=VARCHAR},
            #{entity.depth,jdbcType=INTEGER},
            #{entity.effectiveTime,jdbcType=TIMESTAMP},
            #{entity.expiryTime,jdbcType=TIMESTAMP},
            #{entity.countingUnitId,jdbcType=VARCHAR},
            #{entity.currencyUnitId,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mds.production.infrastructure.po.NewProductionOrganizationPO">
        update mds_org_production_organization
        set organization_code          = #{organizationCode,jdbcType=VARCHAR},
            organization_name          = #{organizationName,jdbcType=VARCHAR},
            category                   = #{category,jdbcType=VARCHAR},
            display_index              = #{displayIndex,jdbcType=INTEGER},
            sales_segment_id           = #{salesSegmentId,jdbcType=VARCHAR},
            parent_id                  = #{parentId,jdbcType=VARCHAR},
            capacity_type              = #{capacityType,jdbcType=VARCHAR},
            capacity_balance_type      = #{capacityBalanceType,jdbcType=VARCHAR},
            calculation_type           = #{calculationType,jdbcType=VARCHAR},
            plan_level                 = #{planLevel,jdbcType=VARCHAR},
            infinite_capacity          = #{infiniteCapacity,jdbcType=VARCHAR},
            capacity_smooth            = #{capacitySmooth,jdbcType=VARCHAR},
            capacity_smooth_delta      = #{capacitySmoothDelta,jdbcType=VARCHAR},
            capacity_smooth_period_num = #{capacitySmoothPeriodNum,jdbcType=INTEGER},
            planning_frozen_duration   = #{planningFrozenDuration,jdbcType=INTEGER},
            measurement_unit_id        = #{measurementUnitId,jdbcType=VARCHAR},
            currency_id                = #{currencyId,jdbcType=VARCHAR},
            lot_size                   = #{lotSize,jdbcType=VARCHAR},
            max_quantity               = #{maxQuantity,jdbcType=VARCHAR},
            min_quantity               = #{minQuantity,jdbcType=VARCHAR},
            country                    = #{country,jdbcType=VARCHAR},
            area                       = #{area,jdbcType=VARCHAR},
            province                   = #{province,jdbcType=VARCHAR},
            city                       = #{city,jdbcType=VARCHAR},
            postal_code                = #{postalCode,jdbcType=VARCHAR},
            address                    = #{address,jdbcType=VARCHAR},
            longitude                  = #{longitude,jdbcType=VARCHAR},
            latitude                   = #{latitude,jdbcType=VARCHAR},
            depth                      = #{depth,jdbcType=INTEGER},
            effective_time             = #{effectiveTime,jdbcType=TIMESTAMP},
            expiry_time                = #{expiryTime,jdbcType=TIMESTAMP},
            counting_unit_id           = #{countingUnitId,jdbcType=VARCHAR},
            currency_unit_id           = #{currencyUnitId,jdbcType=VARCHAR},
            remark                     = #{remark,jdbcType=VARCHAR},
            enabled                    = #{enabled,jdbcType=VARCHAR},
            modifier                   = #{modifier,jdbcType=VARCHAR},
            modify_time                = #{modifyTime,jdbcType=TIMESTAMP},
            version_value              = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mds.production.infrastructure.po.NewProductionOrganizationPO">
        update mds_org_production_organization
        <set>
            <if test="item.organizationCode != null and item.organizationCode != ''">
                organization_code = #{item.organizationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.organizationName != null and item.organizationName != ''">
                organization_name = #{item.organizationName,jdbcType=VARCHAR},
            </if>
            <if test="item.category != null and item.category != ''">
                category = #{item.category,jdbcType=VARCHAR},
            </if>
            <if test="item.displayIndex != null">
                display_index = #{item.displayIndex,jdbcType=INTEGER},
            </if>
            <if test="item.salesSegmentId != null and item.salesSegmentId != ''">
                sales_segment_id = #{item.salesSegmentId,jdbcType=VARCHAR},
            </if>
            <if test="item.parentId != null and item.parentId != ''">
                parent_id = #{item.parentId,jdbcType=VARCHAR},
            </if>
            <if test="item.capacityType != null and item.capacityType != ''">
                capacity_type = #{item.capacityType,jdbcType=VARCHAR},
            </if>
            <if test="item.capacityBalanceType != null and item.capacityBalanceType != ''">
                capacity_balance_type = #{item.capacityBalanceType,jdbcType=VARCHAR},
            </if>
            <if test="item.calculationType != null and item.calculationType != ''">
                calculation_type = #{item.calculationType,jdbcType=VARCHAR},
            </if>
            <if test="item.planLevel != null and item.planLevel != ''">
                plan_level = #{item.planLevel,jdbcType=VARCHAR},
            </if>
            <if test="item.infiniteCapacity != null and item.infiniteCapacity != ''">
                infinite_capacity = #{item.infiniteCapacity,jdbcType=VARCHAR},
            </if>
            <if test="item.capacitySmooth != null and item.capacitySmooth != ''">
                capacity_smooth = #{item.capacitySmooth,jdbcType=VARCHAR},
            </if>
            <if test="item.capacitySmoothDelta != null">
                capacity_smooth_delta = #{item.capacitySmoothDelta,jdbcType=VARCHAR},
            </if>
            <if test="item.capacitySmoothPeriodNum != null">
                capacity_smooth_period_num = #{item.capacitySmoothPeriodNum,jdbcType=INTEGER},
            </if>
            <if test="item.planningFrozenDuration != null">
                planning_frozen_duration = #{item.planningFrozenDuration,jdbcType=INTEGER},
            </if>
            <if test="item.measurementUnitId != null and item.measurementUnitId != ''">
                measurement_unit_id = #{item.measurementUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.currencyId != null and item.currencyId != ''">
                currency_id = #{item.currencyId,jdbcType=VARCHAR},
            </if>
            <if test="item.lotSize != null">
                lot_size = #{item.lotSize,jdbcType=VARCHAR},
            </if>
            <if test="item.maxQuantity != null">
                max_quantity = #{item.maxQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.minQuantity != null">
                min_quantity = #{item.minQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.country != null and item.country != ''">
                country = #{item.country,jdbcType=VARCHAR},
            </if>
            <if test="item.area != null and item.area != ''">
                area = #{item.area,jdbcType=VARCHAR},
            </if>
            <if test="item.province != null and item.province != ''">
                province = #{item.province,jdbcType=VARCHAR},
            </if>
            <if test="item.city != null and item.city != ''">
                city = #{item.city,jdbcType=VARCHAR},
            </if>
            <if test="item.postalCode != null and item.postalCode != ''">
                postal_code = #{item.postalCode,jdbcType=VARCHAR},
            </if>
            <if test="item.address != null and item.address != ''">
                address = #{item.address,jdbcType=VARCHAR},
            </if>
            <if test="item.longitude != null and item.longitude != ''">
                longitude = #{item.longitude,jdbcType=VARCHAR},
            </if>
            <if test="item.latitude != null and item.latitude != ''">
                latitude = #{item.latitude,jdbcType=VARCHAR},
            </if>
            <if test="item.depth != null">
                depth = #{item.depth,jdbcType=INTEGER},
            </if>
            <if test="item.effectiveTime != null">
                effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.expiryTime != null">
                expiry_time = #{item.expiryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.countingUnitId != null and item.countingUnitId != ''">
                counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.currencyUnitId != null and item.currencyUnitId != ''">
                currency_unit_id = #{item.currencyUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_org_production_organization
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="organization_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.organizationCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="organization_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.organizationName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.category,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="display_index = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.displayIndex,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="sales_segment_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.salesSegmentId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="parent_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.parentId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="capacity_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.capacityType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="capacity_balance_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.capacityBalanceType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="calculation_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.calculationType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plan_level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planLevel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="infinite_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.infiniteCapacity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="capacity_smooth = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.capacitySmooth,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="capacity_smooth_delta = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.capacitySmoothDelta,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="capacity_smooth_period_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.capacitySmoothPeriodNum,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="planning_frozen_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planningFrozenDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="measurement_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.measurementUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="currency_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.currencyId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="lot_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lotSize,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="max_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="min_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.minQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="country = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.country,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.area,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="province = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.province,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="city = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.city,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="postal_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.postalCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="address = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.address,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="longitude = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.longitude,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="latitude = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.latitude,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="depth = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.depth,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="effective_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.effectiveTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="expiry_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.expiryTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="counting_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.countingUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="currency_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.currencyUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mds_org_production_organization
            <set>
                <if test="item.organizationCode != null and item.organizationCode != ''">
                    organization_code = #{item.organizationCode,jdbcType=VARCHAR},
                </if>
                <if test="item.organizationName != null and item.organizationName != ''">
                    organization_name = #{item.organizationName,jdbcType=VARCHAR},
                </if>
                <if test="item.category != null and item.category != ''">
                    category = #{item.category,jdbcType=VARCHAR},
                </if>
                <if test="item.displayIndex != null">
                    display_index = #{item.displayIndex,jdbcType=INTEGER},
                </if>
                <if test="item.salesSegmentId != null and item.salesSegmentId != ''">
                    sales_segment_id = #{item.salesSegmentId,jdbcType=VARCHAR},
                </if>
                <if test="item.parentId != null and item.parentId != ''">
                    parent_id = #{item.parentId,jdbcType=VARCHAR},
                </if>
                <if test="item.capacityType != null and item.capacityType != ''">
                    capacity_type = #{item.capacityType,jdbcType=VARCHAR},
                </if>
                <if test="item.capacityBalanceType != null and item.capacityBalanceType != ''">
                    capacity_balance_type = #{item.capacityBalanceType,jdbcType=VARCHAR},
                </if>
                <if test="item.calculationType != null and item.calculationType != ''">
                    calculation_type = #{item.calculationType,jdbcType=VARCHAR},
                </if>
                <if test="item.planLevel != null and item.planLevel != ''">
                    plan_level = #{item.planLevel,jdbcType=VARCHAR},
                </if>
                <if test="item.infiniteCapacity != null and item.infiniteCapacity != ''">
                    infinite_capacity = #{item.infiniteCapacity,jdbcType=VARCHAR},
                </if>
                <if test="item.capacitySmooth != null and item.capacitySmooth != ''">
                    capacity_smooth = #{item.capacitySmooth,jdbcType=VARCHAR},
                </if>
                <if test="item.capacitySmoothDelta != null">
                    capacity_smooth_delta = #{item.capacitySmoothDelta,jdbcType=VARCHAR},
                </if>
                <if test="item.capacitySmoothPeriodNum != null">
                    capacity_smooth_period_num = #{item.capacitySmoothPeriodNum,jdbcType=INTEGER},
                </if>
                <if test="item.planningFrozenDuration != null">
                    planning_frozen_duration = #{item.planningFrozenDuration,jdbcType=INTEGER},
                </if>
                <if test="item.measurementUnitId != null and item.measurementUnitId != ''">
                    measurement_unit_id = #{item.measurementUnitId,jdbcType=VARCHAR},
                </if>
                <if test="item.currencyId != null and item.currencyId != ''">
                    currency_id = #{item.currencyId,jdbcType=VARCHAR},
                </if>
                <if test="item.lotSize != null">
                    lot_size = #{item.lotSize,jdbcType=VARCHAR},
                </if>
                <if test="item.maxQuantity != null">
                    max_quantity = #{item.maxQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.minQuantity != null">
                    min_quantity = #{item.minQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.country != null and item.country != ''">
                    country = #{item.country,jdbcType=VARCHAR},
                </if>
                <if test="item.area != null and item.area != ''">
                    area = #{item.area,jdbcType=VARCHAR},
                </if>
                <if test="item.province != null and item.province != ''">
                    province = #{item.province,jdbcType=VARCHAR},
                </if>
                <if test="item.city != null and item.city != ''">
                    city = #{item.city,jdbcType=VARCHAR},
                </if>
                <if test="item.postalCode != null and item.postalCode != ''">
                    postal_code = #{item.postalCode,jdbcType=VARCHAR},
                </if>
                <if test="item.address != null and item.address != ''">
                    address = #{item.address,jdbcType=VARCHAR},
                </if>
                <if test="item.longitude != null and item.longitude != ''">
                    longitude = #{item.longitude,jdbcType=VARCHAR},
                </if>
                <if test="item.latitude != null and item.latitude != ''">
                    latitude = #{item.latitude,jdbcType=VARCHAR},
                </if>
                <if test="item.depth != null">
                    depth = #{item.depth,jdbcType=INTEGER},
                </if>
                <if test="item.effectiveTime != null">
                    effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.expiryTime != null">
                    expiry_time = #{item.expiryTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.countingUnitId != null and item.countingUnitId != ''">
                    counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
                </if>
                <if test="item.currencyUnitId != null and item.currencyUnitId != ''">
                    currency_unit_id = #{item.currencyUnitId,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mds_org_production_organization
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_org_production_organization where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
