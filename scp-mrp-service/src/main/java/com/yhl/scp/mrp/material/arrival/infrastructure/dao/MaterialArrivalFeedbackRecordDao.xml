<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.material.arrival.infrastructure.dao.MaterialArrivalFeedbackRecordDao">
    <resultMap id="BaseResultMap"
               type="com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialArrivalFeedbackRecordPO">
        <!--@Table mrp_material_arrival_feedback_record-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime"/>
        <result column="plan_need_id" jdbcType="VARCHAR" property="planNeedId"/>
        <result column="version" jdbcType="VARCHAR" property="version"/>
        <result column="product_stock_point_id" jdbcType="VARCHAR" property="productStockPointId"/>
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="demand_number" jdbcType="VARCHAR" property="demandNumber"/>
        <result column="demand_time" jdbcType="TIMESTAMP" property="demandTime"/>
        <result column="expected_arrival_time" jdbcType="TIMESTAMP" property="expectedArrivalTime"/>
        <result column="expected_arrival_number" jdbcType="VARCHAR" property="expectedArrivalNumber"/>
        <result column="confirmation_status" jdbcType="VARCHAR" property="confirmationStatus"/>
        <result column="confirmation_time" jdbcType="TIMESTAMP" property="confirmationTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.material.arrival.vo.MaterialArrivalFeedbackRecordVO">
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,publish_time,plan_need_id,version,product_stock_point_id,supplier_code,supplier_name,demand_number,demand_time,expected_arrival_time,expected_arrival_number,confirmation_status,confirmation_time,remark,enabled,creator,create_time,modifier,modify_time,version_value

    </sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List"/>,stock_point_code,stock_point_name,product_code,product_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.publishTime != null">
                and publish_time = #{params.publishTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.planNeedId != null and params.planNeedId != ''">
                and plan_need_id = #{params.planNeedId,jdbcType=VARCHAR}
            </if>
            <if test="params.version != null and params.version != ''">
                and version = #{params.version,jdbcType=VARCHAR}
            </if>
            <if test="params.productStockPointId != null and params.productStockPointId != ''">
                and product_stock_point_id = #{params.productStockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierCode != null and params.supplierCode != ''">
                and supplier_code = #{params.supplierCode,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierName != null and params.supplierName != ''">
                and supplier_name = #{params.supplierName,jdbcType=VARCHAR}
            </if>
            <if test="params.demandNumber != null">
                and demand_number = #{params.demandNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.demandTime != null">
                and demand_time = #{params.demandTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.expectedArrivalTime != null">
                and expected_arrival_time = #{params.expectedArrivalTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.expectedArrivalNumber != null">
                and expected_arrival_number = #{params.expectedArrivalNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.confirmationStatus != null and params.confirmationStatus != ''">
                and confirmation_status = #{params.confirmationStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.confirmationTime != null">
                and confirmation_time = #{params.confirmationTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_arrival_feedback_record
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_arrival_feedback_record
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_arrival_feedback_record
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_arrival_feedback_record
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert"
            parameterType="com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialArrivalFeedbackRecordPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_material_arrival_feedback_record(
        id,
        publish_time,
        plan_need_id,
        version,
        product_stock_point_id,
        supplier_code,
        supplier_name,
        demand_number,
        demand_time,
        expected_arrival_time,
        expected_arrival_number,
        confirmation_status,
        confirmation_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{publishTime,jdbcType=TIMESTAMP},
        #{planNeedId,jdbcType=VARCHAR},
        #{version,jdbcType=VARCHAR},
        #{productStockPointId,jdbcType=VARCHAR},
        #{supplierCode,jdbcType=VARCHAR},
        #{supplierName,jdbcType=VARCHAR},
        #{demandNumber,jdbcType=VARCHAR},
        #{demandTime,jdbcType=TIMESTAMP},
        #{expectedArrivalTime,jdbcType=TIMESTAMP},
        #{expectedArrivalNumber,jdbcType=VARCHAR},
        #{confirmationStatus,jdbcType=VARCHAR},
        #{confirmationTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialArrivalFeedbackRecordPO">
        insert into mrp_material_arrival_feedback_record(
        id,
        publish_time,
        plan_need_id,
        version,
        product_stock_point_id,
        supplier_code,
        supplier_name,
        demand_number,
        demand_time,
        expected_arrival_time,
        expected_arrival_number,
        confirmation_status,
        confirmation_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{publishTime,jdbcType=TIMESTAMP},
        #{planNeedId,jdbcType=VARCHAR},
        #{version,jdbcType=VARCHAR},
        #{productStockPointId,jdbcType=VARCHAR},
        #{supplierCode,jdbcType=VARCHAR},
        #{supplierName,jdbcType=VARCHAR},
        #{demandNumber,jdbcType=VARCHAR},
        #{demandTime,jdbcType=TIMESTAMP},
        #{expectedArrivalTime,jdbcType=TIMESTAMP},
        #{expectedArrivalNumber,jdbcType=VARCHAR},
        #{confirmationStatus,jdbcType=VARCHAR},
        #{confirmationTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_arrival_feedback_record(
        id,
        publish_time,
        plan_need_id,
        version,
        product_stock_point_id,
        supplier_code,
        supplier_name,
        demand_number,
        demand_time,
        expected_arrival_time,
        expected_arrival_number,
        confirmation_status,
        confirmation_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.publishTime,jdbcType=TIMESTAMP},
            #{entity.planNeedId,jdbcType=VARCHAR},
            #{entity.version,jdbcType=VARCHAR},
            #{entity.productStockPointId,jdbcType=VARCHAR},
            #{entity.supplierCode,jdbcType=VARCHAR},
            #{entity.supplierName,jdbcType=VARCHAR},
            #{entity.demandNumber,jdbcType=VARCHAR},
            #{entity.demandTime,jdbcType=TIMESTAMP},
            #{entity.expectedArrivalTime,jdbcType=TIMESTAMP},
            #{entity.expectedArrivalNumber,jdbcType=VARCHAR},
            #{entity.confirmationStatus,jdbcType=VARCHAR},
            #{entity.confirmationTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_material_arrival_feedback_record(
        id,
        publish_time,
        plan_need_id,
        version,
        product_stock_point_id,
        supplier_code,
        supplier_name,
        demand_number,
        demand_time,
        expected_arrival_time,
        expected_arrival_number,
        confirmation_status,
        confirmation_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.publishTime,jdbcType=TIMESTAMP},
            #{entity.planNeedId,jdbcType=VARCHAR},
            #{entity.version,jdbcType=VARCHAR},
            #{entity.productStockPointId,jdbcType=VARCHAR},
            #{entity.supplierCode,jdbcType=VARCHAR},
            #{entity.supplierName,jdbcType=VARCHAR},
            #{entity.demandNumber,jdbcType=VARCHAR},
            #{entity.demandTime,jdbcType=TIMESTAMP},
            #{entity.expectedArrivalTime,jdbcType=TIMESTAMP},
            #{entity.expectedArrivalNumber,jdbcType=VARCHAR},
            #{entity.confirmationStatus,jdbcType=VARCHAR},
            #{entity.confirmationTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update"
            parameterType="com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialArrivalFeedbackRecordPO">
        update mrp_material_arrival_feedback_record set
        publish_time = #{publishTime,jdbcType=TIMESTAMP},
        plan_need_id = #{planNeedId,jdbcType=VARCHAR},
        version = #{version,jdbcType=VARCHAR},
        product_stock_point_id = #{productStockPointId,jdbcType=VARCHAR},
        supplier_code = #{supplierCode,jdbcType=VARCHAR},
        supplier_name = #{supplierName,jdbcType=VARCHAR},
        demand_number = #{demandNumber,jdbcType=VARCHAR},
        demand_time = #{demandTime,jdbcType=TIMESTAMP},
        expected_arrival_time = #{expectedArrivalTime,jdbcType=TIMESTAMP},
        expected_arrival_number = #{expectedArrivalNumber,jdbcType=VARCHAR},
        confirmation_status = #{confirmationStatus,jdbcType=VARCHAR},
        confirmation_time = #{confirmationTime,jdbcType=TIMESTAMP},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value      = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialArrivalFeedbackRecordPO">
        update mrp_material_arrival_feedback_record
        <set>
            <if test="item.publishTime != null">
                publish_time = #{item.publishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.planNeedId != null and item.planNeedId != ''">
                plan_need_id = #{item.planNeedId,jdbcType=VARCHAR},
            </if>
            <if test="item.version != null and item.version != ''">
                version = #{item.version,jdbcType=VARCHAR},
            </if>
            <if test="item.productStockPointId != null and item.productStockPointId != ''">
                product_stock_point_id = #{item.productStockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierCode != null and item.supplierCode != ''">
                supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierName != null and item.supplierName != ''">
                supplier_name = #{item.supplierName,jdbcType=VARCHAR},
            </if>
            <if test="item.demandNumber != null">
                demand_number = #{item.demandNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.demandTime != null">
                demand_time = #{item.demandTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.expectedArrivalTime != null">
                expected_arrival_time = #{item.expectedArrivalTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.expectedArrivalNumber != null">
                expected_arrival_number = #{item.expectedArrivalNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.confirmationStatus != null and item.confirmationStatus != ''">
                confirmation_status = #{item.confirmationStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.confirmationTime != null">
                confirmation_time = #{item.confirmationTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_arrival_feedback_record
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="publish_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.publishTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="plan_need_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planNeedId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.version,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productStockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="expected_arrival_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.expectedArrivalTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="expected_arrival_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.expectedArrivalNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="confirmation_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.confirmationStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="confirmation_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.confirmationTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_material_arrival_feedback_record
            <set>
                <if test="item.publishTime != null">
                    publish_time = #{item.publishTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.planNeedId != null and item.planNeedId != ''">
                    plan_need_id = #{item.planNeedId,jdbcType=VARCHAR},
                </if>
                <if test="item.version != null and item.version != ''">
                    version = #{item.version,jdbcType=VARCHAR},
                </if>
                <if test="item.productStockPointId != null and item.productStockPointId != ''">
                    product_stock_point_id = #{item.productStockPointId,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierCode != null and item.supplierCode != ''">
                    supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierName != null and item.supplierName != ''">
                    supplier_name = #{item.supplierName,jdbcType=VARCHAR},
                </if>
                <if test="item.demandNumber != null">
                    demand_number = #{item.demandNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.demandTime != null">
                    demand_time = #{item.demandTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.expectedArrivalTime != null">
                    expected_arrival_time = #{item.expectedArrivalTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.expectedArrivalNumber != null">
                    expected_arrival_number = #{item.expectedArrivalNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.confirmationStatus != null and item.confirmationStatus != ''">
                    confirmation_status = #{item.confirmationStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.confirmationTime != null">
                    confirmation_time = #{item.confirmationTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mrp_material_arrival_feedback_record where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_arrival_feedback_record where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
