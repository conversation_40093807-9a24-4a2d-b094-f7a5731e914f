package com.yhl.scp.mrp.compare.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <code>ProductChangeCompare2DO</code>
 * <p>
 * 材料计划变动提醒-产品对比DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-18 09:56:44
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ProductChangeCompareDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 278106252694234389L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 车型编码
     */
    private String vehicleModelCode;
    /**
     * 版本
     */
    private String versionCode;
    /**
     * MRP计算状态
     */
    private String mrpClalStatus;
    /**
     * 主生产1周现数量
     */
    private BigDecimal masterPlanOneWeekCurrentQuantity;
    /**
     * 主生产1周变化量
     */
    private BigDecimal masterPlanOneWeekChangeQuantity;
    /**
     * 主生产2周现数量
     */
    private BigDecimal masterPlanTwoWeekCurrentQuantity;
    /**
     * 主生产2周变化量
     */
    private BigDecimal masterPlanTwoWeekChangeQuantity;
    /**
     * 主生产3周现数量
     */
    private BigDecimal masterPlanThreeWeekCurrentQuantity;
    /**
     * 主生产3周变化量
     */
    private BigDecimal masterPlanThreeWeekChangeQuantity;
    /**
     * 主生产4周现数量
     */
    private BigDecimal masterPlanFourWeekCurrentQuantity;
    /**
     * 主生产4周变化量
     */
    private BigDecimal masterPlanFourWeekChangeQuantity;
    /**
     * 发货计划1周现数量
     */
    private BigDecimal deliveryPlanOneWeekCurrentQuantity;
    /**
     * 发货计划1周变化量
     */
    private BigDecimal deliveryPlanOneWeekChangeQuantity;
    /**
     * 发货计划2周现数量
     */
    private BigDecimal deliveryPlanTwoWeekCurrentQuantity;
    /**
     * 发货计划2周变化量
     */
    private BigDecimal deliveryPlanTwoWeekChangeQuantity;
    /**
     * 发货计划3周现数量
     */
    private BigDecimal deliveryPlanThreeWeekCurrentQuantity;
    /**
     * 发货计划3周变化量
     */
    private BigDecimal deliveryPlanThreeWeekChangeQuantity;
    /**
     * 发货计划4周现数量
     */
    private BigDecimal deliveryPlanFourWeekCurrentQuantity;
    /**
     * 发货计划4周变化量
     */
    private BigDecimal deliveryPlanFourWeekChangeQuantity;
    /**
     * 月预测第1月现数量
     */
    private BigDecimal monthlyForecastOneMonthCurrentQuantity;
    /**
     * 月预测第1月变化量
     */
    private BigDecimal monthlyForecastOneMonthChangeQuantity;
    /**
     * 月预测第2月现数量
     */
    private BigDecimal monthlyForecastTwoMonthCurrentQuantity;
    /**
     * 月预测第2月变化量
     */
    private BigDecimal monthlyForecastTwoMonthChangeQuantity;
    /**
     * 月预测第3月现数量
     */
    private BigDecimal monthlyForecastThreeMonthCurrentQuantity;
    /**
     * 月预测第3月变化量
     */
    private BigDecimal monthlyForecastThreeMonthChangeQuantity;
    /**
     * 月预测第4月现数量
     */
    private BigDecimal monthlyForecastFourMonthCurrentQuantity;
    /**
     * 月预测第4月变化量
     */
    private BigDecimal monthlyForecastFourMonthChangeQuantity;
    /**
     * 月预测第5月现数量
     */
    private BigDecimal monthlyForecastFiveMonthCurrentQuantity;
    /**
     * 月预测第5月变化量
     */
    private BigDecimal monthlyForecastFiveMonthChangeQuantity;
    /**
     * 月预测第6月现数量
     */
    private BigDecimal monthlyForecastSixMonthCurrentQuantity;
    /**
     * 月预测第6月变化量
     */
    private BigDecimal monthlyForecastSixMonthChangeQuantity;
    /**
     * 月预测第7月现数量
     */
    private BigDecimal monthlyForecastSenveMonthCurrentQuantity;
    /**
     * 月预测第7月变化量
     */
    private BigDecimal monthlyForecastSenveMonthChangeQuantity;
    /**
     * 月预测第8月现数量
     */
    private BigDecimal monthlyForecastEightMonthCurrentQuantity;
    /**
     * 月预测第8月变化量
     */
    private BigDecimal monthlyForecastEightMonthChangeQuantity;
    /**
     * 波动率
     */
    private BigDecimal volatility;
    /**
     * 版本
     */
    private Integer versionValue;

}
