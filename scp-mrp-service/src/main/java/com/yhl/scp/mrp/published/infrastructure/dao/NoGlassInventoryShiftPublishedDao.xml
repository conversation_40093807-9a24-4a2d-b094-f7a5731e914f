<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.published.infrastructure.dao.NoGlassInventoryShiftPublishedDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.published.infrastructure.po.NoGlassInventoryShiftPublishedPO">
        <!--@Table mrp_no_glass_inventory_shift_published-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="material_plan_version_published_id" jdbcType="VARCHAR"
                property="materialPlanVersionPublishedId"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_classify" jdbcType="VARCHAR" property="productClassify"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="safety_stock_days_min" jdbcType="INTEGER" property="safetyStockDaysMin"/>
        <result column="safety_stock_days_standard" jdbcType="INTEGER" property="safetyStockDaysStandard"/>
        <result column="safety_stock_days_max" jdbcType="INTEGER" property="safetyStockDaysMax"/>
        <result column="safety_stock_level_min" jdbcType="VARCHAR" property="safetyStockLevelMin"/>
        <result column="safety_stock_level_standard" jdbcType="VARCHAR" property="safetyStockLevelStandard"/>
        <result column="safety_stock_level_max" jdbcType="VARCHAR" property="safetyStockLevelMax"/>
        <result column="opening_inventory" jdbcType="VARCHAR" property="openingInventory"/>
        <result column="demand_quantity" jdbcType="VARCHAR" property="demandQuantity"/>
        <result column="supply_quantity" jdbcType="VARCHAR" property="supplyQuantity"/>
        <result column="adjust_quantity" jdbcType="VARCHAR" property="adjustQuantity"/>
        <result column="used_as_replace_quantity" jdbcType="VARCHAR" property="usedAsReplaceQuantity"/>
        <result column="before_ending_inventory" jdbcType="VARCHAR" property="beforeEndingInventory"/>
        <result column="ending_inventory" jdbcType="VARCHAR" property="endingInventory"/>
        <result column="inventory_gap" jdbcType="VARCHAR" property="inventoryGap"/>
        <result column="inventory_date" jdbcType="TIMESTAMP" property="inventoryDate"/>
        <result column="before_warning_remind" jdbcType="VARCHAR" property="beforeWarningRemind"/>
        <result column="warning_remind" jdbcType="VARCHAR" property="warningRemind"/>
        <result column="product_category" jdbcType="VARCHAR" property="productCategory"/>
        <result column="plan_purchase" jdbcType="VARCHAR" property="planPurchase"/>
        <result column="real_inventory_gap" jdbcType="VARCHAR" property="realInventoryGap"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.published.vo.NoGlassInventoryShiftPublishedVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,material_plan_version_published_id,product_code,product_classify,stock_point_code,safety_stock_days_min,safety_stock_days_standard,safety_stock_days_max,safety_stock_level_min,safety_stock_level_standard,safety_stock_level_max,opening_inventory,demand_quantity,supply_quantity,adjust_quantity,used_as_replace_quantity,before_ending_inventory,ending_inventory,inventory_gap,inventory_date,before_warning_remind,warning_remind,product_category,plan_purchase,real_inventory_gap,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.materialPlanVersionPublishedId != null and params.materialPlanVersionPublishedId != ''">
                and material_plan_version_published_id = #{params.materialPlanVersionPublishedId,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productClassify != null and params.productClassify != ''">
                and product_classify = #{params.productClassify,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.safetyStockDaysMin != null">
                and safety_stock_days_min = #{params.safetyStockDaysMin,jdbcType=INTEGER}
            </if>
            <if test="params.safetyStockDaysStandard != null">
                and safety_stock_days_standard = #{params.safetyStockDaysStandard,jdbcType=INTEGER}
            </if>
            <if test="params.safetyStockDaysMax != null">
                and safety_stock_days_max = #{params.safetyStockDaysMax,jdbcType=INTEGER}
            </if>
            <if test="params.safetyStockLevelMin != null">
                and safety_stock_level_min = #{params.safetyStockLevelMin,jdbcType=VARCHAR}
            </if>
            <if test="params.safetyStockLevelStandard != null">
                and safety_stock_level_standard = #{params.safetyStockLevelStandard,jdbcType=VARCHAR}
            </if>
            <if test="params.safetyStockLevelMax != null">
                and safety_stock_level_max = #{params.safetyStockLevelMax,jdbcType=VARCHAR}
            </if>
            <if test="params.openingInventory != null">
                and opening_inventory = #{params.openingInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.demandQuantity != null">
                and demand_quantity = #{params.demandQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyQuantity != null">
                and supply_quantity = #{params.supplyQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.adjustQuantity != null">
                and adjust_quantity = #{params.adjustQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.usedAsReplaceQuantity != null">
                and used_as_replace_quantity = #{params.usedAsReplaceQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.beforeEndingInventory != null">
                and before_ending_inventory = #{params.beforeEndingInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.endingInventory != null">
                and ending_inventory = #{params.endingInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryGap != null">
                and inventory_gap = #{params.inventoryGap,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryDate != null">
                and inventory_date = #{params.inventoryDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.beforeWarningRemind != null and params.beforeWarningRemind != ''">
                and before_warning_remind = #{params.beforeWarningRemind,jdbcType=VARCHAR}
            </if>
            <if test="params.warningRemind != null and params.warningRemind != ''">
                and warning_remind = #{params.warningRemind,jdbcType=VARCHAR}
            </if>
            <if test="params.productCategory != null and params.productCategory != ''">
                and product_category = #{params.productCategory,jdbcType=VARCHAR}
            </if>
            <if test="params.planPurchase != null">
                and plan_purchase = #{params.planPurchase,jdbcType=VARCHAR}
            </if>
            <if test="params.realInventoryGap != null">
                and real_inventory_gap = #{params.realInventoryGap,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_no_glass_inventory_shift_published
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_no_glass_inventory_shift_published
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mrp_no_glass_inventory_shift_published
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_no_glass_inventory_shift_published
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.published.infrastructure.po.NoGlassInventoryShiftPublishedPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_no_glass_inventory_shift_published(
        id,
        material_plan_version_published_id,
        product_code,
        product_classify,
        stock_point_code,
        safety_stock_days_min,
        safety_stock_days_standard,
        safety_stock_days_max,
        safety_stock_level_min,
        safety_stock_level_standard,
        safety_stock_level_max,
        opening_inventory,
        demand_quantity,
        supply_quantity,
        adjust_quantity,
        used_as_replace_quantity,
        before_ending_inventory,
        ending_inventory,
        inventory_gap,
        inventory_date,
        before_warning_remind,
        warning_remind,
        product_category,
        plan_purchase,
        real_inventory_gap,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{materialPlanVersionPublishedId,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productClassify,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{safetyStockDaysMin,jdbcType=INTEGER},
        #{safetyStockDaysStandard,jdbcType=INTEGER},
        #{safetyStockDaysMax,jdbcType=INTEGER},
        #{safetyStockLevelMin,jdbcType=VARCHAR},
        #{safetyStockLevelStandard,jdbcType=VARCHAR},
        #{safetyStockLevelMax,jdbcType=VARCHAR},
        #{openingInventory,jdbcType=VARCHAR},
        #{demandQuantity,jdbcType=VARCHAR},
        #{supplyQuantity,jdbcType=VARCHAR},
        #{adjustQuantity,jdbcType=VARCHAR},
        #{usedAsReplaceQuantity,jdbcType=VARCHAR},
        #{beforeEndingInventory,jdbcType=VARCHAR},
        #{endingInventory,jdbcType=VARCHAR},
        #{inventoryGap,jdbcType=VARCHAR},
        #{inventoryDate,jdbcType=TIMESTAMP},
        #{beforeWarningRemind,jdbcType=VARCHAR},
        #{warningRemind,jdbcType=VARCHAR},
        #{productCategory,jdbcType=VARCHAR},
        #{planPurchase,jdbcType=VARCHAR},
        #{realInventoryGap,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.published.infrastructure.po.NoGlassInventoryShiftPublishedPO">
        insert into mrp_no_glass_inventory_shift_published(id,
                                                           material_plan_version_published_id,
                                                           product_code,
                                                           product_classify,
                                                           stock_point_code,
                                                           safety_stock_days_min,
                                                           safety_stock_days_standard,
                                                           safety_stock_days_max,
                                                           safety_stock_level_min,
                                                           safety_stock_level_standard,
                                                           safety_stock_level_max,
                                                           opening_inventory,
                                                           demand_quantity,
                                                           supply_quantity,
                                                           adjust_quantity,
                                                           used_as_replace_quantity,
                                                           before_ending_inventory,
                                                           ending_inventory,
                                                           inventory_gap,
                                                           inventory_date,
                                                           before_warning_remind,
                                                           warning_remind,
                                                           product_category,
                                                           plan_purchase,
                                                           real_inventory_gap,
                                                           remark,
                                                           enabled,
                                                           creator,
                                                           create_time,
                                                           modifier,
                                                           modify_time,
                                                           version_value)
        values (#{id,jdbcType=VARCHAR},
                #{materialPlanVersionPublishedId,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{productClassify,jdbcType=VARCHAR},
                #{stockPointCode,jdbcType=VARCHAR},
                #{safetyStockDaysMin,jdbcType=INTEGER},
                #{safetyStockDaysStandard,jdbcType=INTEGER},
                #{safetyStockDaysMax,jdbcType=INTEGER},
                #{safetyStockLevelMin,jdbcType=VARCHAR},
                #{safetyStockLevelStandard,jdbcType=VARCHAR},
                #{safetyStockLevelMax,jdbcType=VARCHAR},
                #{openingInventory,jdbcType=VARCHAR},
                #{demandQuantity,jdbcType=VARCHAR},
                #{supplyQuantity,jdbcType=VARCHAR},
                #{adjustQuantity,jdbcType=VARCHAR},
                #{usedAsReplaceQuantity,jdbcType=VARCHAR},
                #{beforeEndingInventory,jdbcType=VARCHAR},
                #{endingInventory,jdbcType=VARCHAR},
                #{inventoryGap,jdbcType=VARCHAR},
                #{inventoryDate,jdbcType=TIMESTAMP},
                #{beforeWarningRemind,jdbcType=VARCHAR},
                #{warningRemind,jdbcType=VARCHAR},
                #{productCategory,jdbcType=VARCHAR},
                #{planPurchase,jdbcType=VARCHAR},
                #{realInventoryGap,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_no_glass_inventory_shift_published(
        id,
        material_plan_version_published_id,
        product_code,
        product_classify,
        stock_point_code,
        safety_stock_days_min,
        safety_stock_days_standard,
        safety_stock_days_max,
        safety_stock_level_min,
        safety_stock_level_standard,
        safety_stock_level_max,
        opening_inventory,
        demand_quantity,
        supply_quantity,
        adjust_quantity,
        used_as_replace_quantity,
        before_ending_inventory,
        ending_inventory,
        inventory_gap,
        inventory_date,
        before_warning_remind,
        warning_remind,
        product_category,
        plan_purchase,
        real_inventory_gap,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.materialPlanVersionPublishedId,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productClassify,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.safetyStockDaysMin,jdbcType=INTEGER},
            #{entity.safetyStockDaysStandard,jdbcType=INTEGER},
            #{entity.safetyStockDaysMax,jdbcType=INTEGER},
            #{entity.safetyStockLevelMin,jdbcType=VARCHAR},
            #{entity.safetyStockLevelStandard,jdbcType=VARCHAR},
            #{entity.safetyStockLevelMax,jdbcType=VARCHAR},
            #{entity.openingInventory,jdbcType=VARCHAR},
            #{entity.demandQuantity,jdbcType=VARCHAR},
            #{entity.supplyQuantity,jdbcType=VARCHAR},
            #{entity.adjustQuantity,jdbcType=VARCHAR},
            #{entity.usedAsReplaceQuantity,jdbcType=VARCHAR},
            #{entity.beforeEndingInventory,jdbcType=VARCHAR},
            #{entity.endingInventory,jdbcType=VARCHAR},
            #{entity.inventoryGap,jdbcType=VARCHAR},
            #{entity.inventoryDate,jdbcType=TIMESTAMP},
            #{entity.beforeWarningRemind,jdbcType=VARCHAR},
            #{entity.warningRemind,jdbcType=VARCHAR},
            #{entity.productCategory,jdbcType=VARCHAR},
            #{entity.planPurchase,jdbcType=VARCHAR},
            #{entity.realInventoryGap,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_no_glass_inventory_shift_published(
        id,
        material_plan_version_published_id,
        product_code,
        product_classify,
        stock_point_code,
        safety_stock_days_min,
        safety_stock_days_standard,
        safety_stock_days_max,
        safety_stock_level_min,
        safety_stock_level_standard,
        safety_stock_level_max,
        opening_inventory,
        demand_quantity,
        supply_quantity,
        adjust_quantity,
        used_as_replace_quantity,
        before_ending_inventory,
        ending_inventory,
        inventory_gap,
        inventory_date,
        before_warning_remind,
        warning_remind,
        product_category,
        plan_purchase,
        real_inventory_gap,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.materialPlanVersionPublishedId,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productClassify,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.safetyStockDaysMin,jdbcType=INTEGER},
            #{entity.safetyStockDaysStandard,jdbcType=INTEGER},
            #{entity.safetyStockDaysMax,jdbcType=INTEGER},
            #{entity.safetyStockLevelMin,jdbcType=VARCHAR},
            #{entity.safetyStockLevelStandard,jdbcType=VARCHAR},
            #{entity.safetyStockLevelMax,jdbcType=VARCHAR},
            #{entity.openingInventory,jdbcType=VARCHAR},
            #{entity.demandQuantity,jdbcType=VARCHAR},
            #{entity.supplyQuantity,jdbcType=VARCHAR},
            #{entity.adjustQuantity,jdbcType=VARCHAR},
            #{entity.usedAsReplaceQuantity,jdbcType=VARCHAR},
            #{entity.beforeEndingInventory,jdbcType=VARCHAR},
            #{entity.endingInventory,jdbcType=VARCHAR},
            #{entity.inventoryGap,jdbcType=VARCHAR},
            #{entity.inventoryDate,jdbcType=TIMESTAMP},
            #{entity.beforeWarningRemind,jdbcType=VARCHAR},
            #{entity.warningRemind,jdbcType=VARCHAR},
            #{entity.productCategory,jdbcType=VARCHAR},
            #{entity.planPurchase,jdbcType=VARCHAR},
            #{entity.realInventoryGap,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.published.infrastructure.po.NoGlassInventoryShiftPublishedPO">
        update mrp_no_glass_inventory_shift_published
        set material_plan_version_published_id = #{materialPlanVersionPublishedId,jdbcType=VARCHAR},
            product_code                       = #{productCode,jdbcType=VARCHAR},
            product_classify                   = #{productClassify,jdbcType=VARCHAR},
            stock_point_code                   = #{stockPointCode,jdbcType=VARCHAR},
            safety_stock_days_min              = #{safetyStockDaysMin,jdbcType=INTEGER},
            safety_stock_days_standard         = #{safetyStockDaysStandard,jdbcType=INTEGER},
            safety_stock_days_max              = #{safetyStockDaysMax,jdbcType=INTEGER},
            safety_stock_level_min             = #{safetyStockLevelMin,jdbcType=VARCHAR},
            safety_stock_level_standard        = #{safetyStockLevelStandard,jdbcType=VARCHAR},
            safety_stock_level_max             = #{safetyStockLevelMax,jdbcType=VARCHAR},
            opening_inventory                  = #{openingInventory,jdbcType=VARCHAR},
            demand_quantity                    = #{demandQuantity,jdbcType=VARCHAR},
            supply_quantity                    = #{supplyQuantity,jdbcType=VARCHAR},
            adjust_quantity                    = #{adjustQuantity,jdbcType=VARCHAR},
            used_as_replace_quantity           = #{usedAsReplaceQuantity,jdbcType=VARCHAR},
            before_ending_inventory            = #{beforeEndingInventory,jdbcType=VARCHAR},
            ending_inventory                   = #{endingInventory,jdbcType=VARCHAR},
            inventory_gap                      = #{inventoryGap,jdbcType=VARCHAR},
            inventory_date                     = #{inventoryDate,jdbcType=TIMESTAMP},
            before_warning_remind              = #{beforeWarningRemind,jdbcType=VARCHAR},
            warning_remind                     = #{warningRemind,jdbcType=VARCHAR},
            product_category                   = #{productCategory,jdbcType=VARCHAR},
            plan_purchase                      = #{planPurchase,jdbcType=VARCHAR},
            real_inventory_gap                 = #{realInventoryGap,jdbcType=VARCHAR},
            remark                             = #{remark,jdbcType=VARCHAR},
            enabled                            = #{enabled,jdbcType=VARCHAR},
            modifier                           = #{modifier,jdbcType=VARCHAR},
            modify_time                        = #{modifyTime,jdbcType=TIMESTAMP},
            version_value      = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.published.infrastructure.po.NoGlassInventoryShiftPublishedPO">
        update mrp_no_glass_inventory_shift_published
        <set>
            <if test="item.materialPlanVersionPublishedId != null and item.materialPlanVersionPublishedId != ''">
                material_plan_version_published_id = #{item.materialPlanVersionPublishedId,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productClassify != null and item.productClassify != ''">
                product_classify = #{item.productClassify,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.safetyStockDaysMin != null">
                safety_stock_days_min = #{item.safetyStockDaysMin,jdbcType=INTEGER},
            </if>
            <if test="item.safetyStockDaysStandard != null">
                safety_stock_days_standard = #{item.safetyStockDaysStandard,jdbcType=INTEGER},
            </if>
            <if test="item.safetyStockDaysMax != null">
                safety_stock_days_max = #{item.safetyStockDaysMax,jdbcType=INTEGER},
            </if>
            <if test="item.safetyStockLevelMin != null">
                safety_stock_level_min = #{item.safetyStockLevelMin,jdbcType=VARCHAR},
            </if>
            <if test="item.safetyStockLevelStandard != null">
                safety_stock_level_standard = #{item.safetyStockLevelStandard,jdbcType=VARCHAR},
            </if>
            <if test="item.safetyStockLevelMax != null">
                safety_stock_level_max = #{item.safetyStockLevelMax,jdbcType=VARCHAR},
            </if>
            <if test="item.openingInventory != null">
                opening_inventory = #{item.openingInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.demandQuantity != null">
                demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyQuantity != null">
                supply_quantity = #{item.supplyQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.adjustQuantity != null">
                adjust_quantity = #{item.adjustQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.usedAsReplaceQuantity != null">
                used_as_replace_quantity = #{item.usedAsReplaceQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.beforeEndingInventory != null">
                before_ending_inventory = #{item.beforeEndingInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.endingInventory != null">
                ending_inventory = #{item.endingInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.inventoryGap != null">
                inventory_gap = #{item.inventoryGap,jdbcType=VARCHAR},
            </if>
            <if test="item.inventoryDate != null">
                inventory_date = #{item.inventoryDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.beforeWarningRemind != null and item.beforeWarningRemind != ''">
                before_warning_remind = #{item.beforeWarningRemind,jdbcType=VARCHAR},
            </if>
            <if test="item.warningRemind != null and item.warningRemind != ''">
                warning_remind = #{item.warningRemind,jdbcType=VARCHAR},
            </if>
            <if test="item.productCategory != null and item.productCategory != ''">
                product_category = #{item.productCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.planPurchase != null">
                plan_purchase = #{item.planPurchase,jdbcType=VARCHAR},
            </if>
            <if test="item.realInventoryGap != null">
                real_inventory_gap = #{item.realInventoryGap,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_no_glass_inventory_shift_published
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="material_plan_version_published_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialPlanVersionPublishedId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_classify = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productClassify,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="safety_stock_days_min = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockDaysMin,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="safety_stock_days_standard = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockDaysStandard,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="safety_stock_days_max = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockDaysMax,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="safety_stock_level_min = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockLevelMin,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="safety_stock_level_standard = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockLevelStandard,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="safety_stock_level_max = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockLevelMax,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="opening_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.openingInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="adjust_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.adjustQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="used_as_replace_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.usedAsReplaceQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="before_ending_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.beforeEndingInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ending_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.endingInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="inventory_gap = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inventoryGap,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="inventory_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inventoryDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="before_warning_remind = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.beforeWarningRemind,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="warning_remind = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.warningRemind,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCategory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plan_purchase = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planPurchase,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="real_inventory_gap = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.realInventoryGap,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_no_glass_inventory_shift_published
            <set>
                <if test="item.materialPlanVersionPublishedId != null and item.materialPlanVersionPublishedId != ''">
                    material_plan_version_published_id = #{item.materialPlanVersionPublishedId,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productClassify != null and item.productClassify != ''">
                    product_classify = #{item.productClassify,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointCode != null and item.stockPointCode != ''">
                    stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
                </if>
                <if test="item.safetyStockDaysMin != null">
                    safety_stock_days_min = #{item.safetyStockDaysMin,jdbcType=INTEGER},
                </if>
                <if test="item.safetyStockDaysStandard != null">
                    safety_stock_days_standard = #{item.safetyStockDaysStandard,jdbcType=INTEGER},
                </if>
                <if test="item.safetyStockDaysMax != null">
                    safety_stock_days_max = #{item.safetyStockDaysMax,jdbcType=INTEGER},
                </if>
                <if test="item.safetyStockLevelMin != null">
                    safety_stock_level_min = #{item.safetyStockLevelMin,jdbcType=VARCHAR},
                </if>
                <if test="item.safetyStockLevelStandard != null">
                    safety_stock_level_standard = #{item.safetyStockLevelStandard,jdbcType=VARCHAR},
                </if>
                <if test="item.safetyStockLevelMax != null">
                    safety_stock_level_max = #{item.safetyStockLevelMax,jdbcType=VARCHAR},
                </if>
                <if test="item.openingInventory != null">
                    opening_inventory = #{item.openingInventory,jdbcType=VARCHAR},
                </if>
                <if test="item.demandQuantity != null">
                    demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.supplyQuantity != null">
                    supply_quantity = #{item.supplyQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.adjustQuantity != null">
                    adjust_quantity = #{item.adjustQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.usedAsReplaceQuantity != null">
                    used_as_replace_quantity = #{item.usedAsReplaceQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.beforeEndingInventory != null">
                    before_ending_inventory = #{item.beforeEndingInventory,jdbcType=VARCHAR},
                </if>
                <if test="item.endingInventory != null">
                    ending_inventory = #{item.endingInventory,jdbcType=VARCHAR},
                </if>
                <if test="item.inventoryGap != null">
                    inventory_gap = #{item.inventoryGap,jdbcType=VARCHAR},
                </if>
                <if test="item.inventoryDate != null">
                    inventory_date = #{item.inventoryDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.beforeWarningRemind != null and item.beforeWarningRemind != ''">
                    before_warning_remind = #{item.beforeWarningRemind,jdbcType=VARCHAR},
                </if>
                <if test="item.warningRemind != null and item.warningRemind != ''">
                    warning_remind = #{item.warningRemind,jdbcType=VARCHAR},
                </if>
                <if test="item.productCategory != null and item.productCategory != ''">
                    product_category = #{item.productCategory,jdbcType=VARCHAR},
                </if>
                <if test="item.planPurchase != null">
                    plan_purchase = #{item.planPurchase,jdbcType=VARCHAR},
                </if>
                <if test="item.realInventoryGap != null">
                    real_inventory_gap = #{item.realInventoryGap,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_no_glass_inventory_shift_published
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_no_glass_inventory_shift_published where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
