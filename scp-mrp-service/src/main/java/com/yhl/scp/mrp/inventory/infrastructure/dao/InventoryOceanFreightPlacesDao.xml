<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.inventory.infrastructure.dao.InventoryOceanFreightPlacesDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryOceanFreightPlacesPO">
        <!--@Table mrp_inventory_ocean_freight_places-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="port_name_ls" jdbcType="VARCHAR" property="portNameLs"/>
        <result column="name_cn" jdbcType="VARCHAR" property="nameCn"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="bl_id" jdbcType="VARCHAR" property="blId"/>
        <result column="address_code" jdbcType="VARCHAR" property="addressCode"/>
        <result column="address_name" jdbcType="VARCHAR" property="addressName"/>
        <result column="name_origin" jdbcType="VARCHAR" property="nameOrigin"/>
        <result column="places_type" jdbcType="VARCHAR" property="placesType"/>
        <result column="latitude" jdbcType="VARCHAR" property="latitude"/>
        <result column="longitude" jdbcType="VARCHAR" property="longitude"/>
        <result column="shipping_house" jdbcType="VARCHAR" property="shippingHouse"/>
        <result column="mmsi" jdbcType="VARCHAR" property="mmsi"/>
        <result column="voyage" jdbcType="VARCHAR" property="voyage"/>
        <result column="etd_time" jdbcType="TIMESTAMP" property="etdTime"/>
        <result column="eta_time" jdbcType="TIMESTAMP" property="etaTime"/>
        <result column="eta_ls_time" jdbcType="TIMESTAMP" property="etaLsTime"/>
        <result column="ata_time" jdbcType="TIMESTAMP" property="ataTime"/>
        <result column="atd_time" jdbcType="TIMESTAMP" property="atdTime"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.inventory.vo.InventoryOceanFreightPlacesVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,port_name_ls,name_cn,creator,create_time,modifier,version_value,modify_time,enabled,end_time,bl_id,address_code,address_name,name_origin,places_type,latitude,longitude,shipping_house,mmsi,voyage,etd_time,eta_time,eta_ls_time,ata_time,atd_time,start_time,remark
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.portNameLs != null and params.portNameLs != ''">
                and port_name_ls = #{params.portNameLs,jdbcType=VARCHAR}
            </if>
            <if test="params.nameCn != null and params.nameCn != ''">
                and name_cn = #{params.nameCn,jdbcType=VARCHAR}
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.endTime != null">
                and end_time = #{params.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.blId != null and params.blId != ''">
                and bl_id = #{params.blId,jdbcType=VARCHAR}
            </if>
            <if test="params.blIds != null and params.blIds != ''">
                and bl_id in
                <foreach collection="params.blIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.addressCode != null and params.addressCode != ''">
                and address_code = #{params.addressCode,jdbcType=VARCHAR}
            </if>
            <if test="params.addressName != null and params.addressName != ''">
                and address_name = #{params.addressName,jdbcType=VARCHAR}
            </if>
            <if test="params.nameOrigin != null and params.nameOrigin != ''">
                and name_origin = #{params.nameOrigin,jdbcType=VARCHAR}
            </if>
            <if test="params.placesType != null and params.placesType != ''">
                and places_type = #{params.placesType,jdbcType=VARCHAR}
            </if>
            <if test="params.latitude != null and params.latitude != ''">
                and latitude = #{params.latitude,jdbcType=VARCHAR}
            </if>
            <if test="params.longitude != null and params.longitude != ''">
                and longitude = #{params.longitude,jdbcType=VARCHAR}
            </if>
            <if test="params.shippingHouse != null and params.shippingHouse != ''">
                and shipping_house = #{params.shippingHouse,jdbcType=VARCHAR}
            </if>
            <if test="params.mmsi != null and params.mmsi != ''">
                and mmsi = #{params.mmsi,jdbcType=VARCHAR}
            </if>
            <if test="params.voyage != null and params.voyage != ''">
                and voyage = #{params.voyage,jdbcType=VARCHAR}
            </if>
            <if test="params.etdTime != null">
                and etd_time = #{params.etdTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.etaTime != null">
                and eta_time = #{params.etaTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.etaLsTime != null">
                and eta_ls_time = #{params.etaLsTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.ataTime != null">
                and ata_time = #{params.ataTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.atdTime != null">
                and atd_time = #{params.atdTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.startTime != null">
                and start_time = #{params.startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_inventory_ocean_freight_places
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_inventory_ocean_freight_places
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from mrp_inventory_ocean_freight_places
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_inventory_ocean_freight_places
        <include refid="Base_Where_Condition" />
    </select>
    <select id="selectByOceanFreightPlacesIds" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_inventory_ocean_freight_places
        where places_type in
        <foreach collection="placesTypes" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        or address_code in
        <foreach collection="addressCodes" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryOceanFreightPlacesPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_inventory_ocean_freight_places(
        id,
        port_name_ls,
        name_cn,
        creator,
        create_time,
        modifier,
        version_value,
        modify_time,
        enabled,
        end_time,
        bl_id,
        address_code,
        address_name,
        name_origin,
        places_type,
        latitude,
        longitude,
        shipping_house,
        mmsi,
        voyage,
        etd_time,
        eta_time,
        eta_ls_time,
        ata_time,
        atd_time,
        start_time,
        remark)
        values (
        #{id,jdbcType=VARCHAR},
        #{portNameLs,jdbcType=VARCHAR},
        #{nameCn,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{enabled,jdbcType=VARCHAR},
        #{endTime,jdbcType=TIMESTAMP},
        #{blId,jdbcType=VARCHAR},
        #{addressCode,jdbcType=VARCHAR},
        #{addressName,jdbcType=VARCHAR},
        #{nameOrigin,jdbcType=VARCHAR},
        #{placesType,jdbcType=VARCHAR},
        #{latitude,jdbcType=VARCHAR},
        #{longitude,jdbcType=VARCHAR},
        #{shippingHouse,jdbcType=VARCHAR},
        #{mmsi,jdbcType=VARCHAR},
        #{voyage,jdbcType=VARCHAR},
        #{etdTime,jdbcType=TIMESTAMP},
        #{etaTime,jdbcType=TIMESTAMP},
        #{etaLsTime,jdbcType=TIMESTAMP},
        #{ataTime,jdbcType=TIMESTAMP},
        #{atdTime,jdbcType=TIMESTAMP},
        #{startTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryOceanFreightPlacesPO">
        insert into mrp_inventory_ocean_freight_places(
        id,
        port_name_ls,
        name_cn,
        creator,
        create_time,
        modifier,
        version_value,
        modify_time,
        enabled,
        end_time,
        bl_id,
        address_code,
        address_name,
        name_origin,
        places_type,
        latitude,
        longitude,
        shipping_house,
        mmsi,
        voyage,
        etd_time,
        eta_time,
        eta_ls_time,
        ata_time,
        atd_time,
        start_time,
        remark)
        values (
        #{id,jdbcType=VARCHAR},
        #{portNameLs,jdbcType=VARCHAR},
        #{nameCn,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{enabled,jdbcType=VARCHAR},
        #{endTime,jdbcType=TIMESTAMP},
        #{blId,jdbcType=VARCHAR},
        #{addressCode,jdbcType=VARCHAR},
        #{addressName,jdbcType=VARCHAR},
        #{nameOrigin,jdbcType=VARCHAR},
        #{placesType,jdbcType=VARCHAR},
        #{latitude,jdbcType=VARCHAR},
        #{longitude,jdbcType=VARCHAR},
        #{shippingHouse,jdbcType=VARCHAR},
        #{mmsi,jdbcType=VARCHAR},
        #{voyage,jdbcType=VARCHAR},
        #{etdTime,jdbcType=TIMESTAMP},
        #{etaTime,jdbcType=TIMESTAMP},
        #{etaLsTime,jdbcType=TIMESTAMP},
        #{ataTime,jdbcType=TIMESTAMP},
        #{atdTime,jdbcType=TIMESTAMP},
        #{startTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_inventory_ocean_freight_places(
        id,
        port_name_ls,
        name_cn,
        creator,
        create_time,
        modifier,
        version_value,
        modify_time,
        enabled,
        end_time,
        bl_id,
        address_code,
        address_name,
        name_origin,
        places_type,
        latitude,
        longitude,
        shipping_house,
        mmsi,
        voyage,
        etd_time,
        eta_time,
        eta_ls_time,
        ata_time,
        atd_time,
        start_time,
        remark)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.portNameLs,jdbcType=VARCHAR},
        #{entity.nameCn,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.endTime,jdbcType=TIMESTAMP},
        #{entity.blId,jdbcType=VARCHAR},
        #{entity.addressCode,jdbcType=VARCHAR},
        #{entity.addressName,jdbcType=VARCHAR},
        #{entity.nameOrigin,jdbcType=VARCHAR},
        #{entity.placesType,jdbcType=VARCHAR},
        #{entity.latitude,jdbcType=VARCHAR},
        #{entity.longitude,jdbcType=VARCHAR},
        #{entity.shippingHouse,jdbcType=VARCHAR},
        #{entity.mmsi,jdbcType=VARCHAR},
        #{entity.voyage,jdbcType=VARCHAR},
        #{entity.etdTime,jdbcType=TIMESTAMP},
        #{entity.etaTime,jdbcType=TIMESTAMP},
        #{entity.etaLsTime,jdbcType=TIMESTAMP},
        #{entity.ataTime,jdbcType=TIMESTAMP},
        #{entity.atdTime,jdbcType=TIMESTAMP},
        #{entity.startTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_inventory_ocean_freight_places(
        id,
        port_name_ls,
        name_cn,
        creator,
        create_time,
        modifier,
        version_value,
        modify_time,
        enabled,
        end_time,
        bl_id,
        address_code,
        address_name,
        name_origin,
        places_type,
        latitude,
        longitude,
        shipping_house,
        mmsi,
        voyage,
        etd_time,
        eta_time,
        eta_ls_time,
        ata_time,
        atd_time,
        start_time,
        remark)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.portNameLs,jdbcType=VARCHAR},
        #{entity.nameCn,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.endTime,jdbcType=TIMESTAMP},
        #{entity.blId,jdbcType=VARCHAR},
        #{entity.addressCode,jdbcType=VARCHAR},
        #{entity.addressName,jdbcType=VARCHAR},
        #{entity.nameOrigin,jdbcType=VARCHAR},
        #{entity.placesType,jdbcType=VARCHAR},
        #{entity.latitude,jdbcType=VARCHAR},
        #{entity.longitude,jdbcType=VARCHAR},
        #{entity.shippingHouse,jdbcType=VARCHAR},
        #{entity.mmsi,jdbcType=VARCHAR},
        #{entity.voyage,jdbcType=VARCHAR},
        #{entity.etdTime,jdbcType=TIMESTAMP},
        #{entity.etaTime,jdbcType=TIMESTAMP},
        #{entity.etaLsTime,jdbcType=TIMESTAMP},
        #{entity.ataTime,jdbcType=TIMESTAMP},
        #{entity.atdTime,jdbcType=TIMESTAMP},
        #{entity.startTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryOceanFreightPlacesPO">
        update mrp_inventory_ocean_freight_places set
        port_name_ls = #{portNameLs,jdbcType=VARCHAR},
        name_cn = #{nameCn,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        version_value = #{versionValue,jdbcType=INTEGER},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        enabled = #{enabled,jdbcType=VARCHAR},
        end_time = #{endTime,jdbcType=TIMESTAMP},
        bl_id = #{blId,jdbcType=VARCHAR},
        address_code = #{addressCode,jdbcType=VARCHAR},
        address_name = #{addressName,jdbcType=VARCHAR},
        name_origin = #{nameOrigin,jdbcType=VARCHAR},
        places_type = #{placesType,jdbcType=VARCHAR},
        latitude = #{latitude,jdbcType=VARCHAR},
        longitude = #{longitude,jdbcType=VARCHAR},
        shipping_house = #{shippingHouse,jdbcType=VARCHAR},
        mmsi = #{mmsi,jdbcType=VARCHAR},
        voyage = #{voyage,jdbcType=VARCHAR},
        etd_time = #{etdTime,jdbcType=TIMESTAMP},
        eta_time = #{etaTime,jdbcType=TIMESTAMP},
        eta_ls_time = #{etaLsTime,jdbcType=TIMESTAMP},
        ata_time = #{ataTime,jdbcType=TIMESTAMP},
        atd_time = #{atdTime,jdbcType=TIMESTAMP},
        start_time = #{startTime,jdbcType=TIMESTAMP},
        remark             = #{remark,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryOceanFreightPlacesPO">
        update mrp_inventory_ocean_freight_places
        <set>
            <if test="item.portNameLs != null and item.portNameLs != ''">
                port_name_ls = #{item.portNameLs,jdbcType=VARCHAR},
            </if>
            <if test="item.nameCn != null and item.nameCn != ''">
                name_cn = #{item.nameCn,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.endTime != null">
                end_time = #{item.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.blId != null and item.blId != ''">
                bl_id = #{item.blId,jdbcType=VARCHAR},
            </if>
            <if test="item.addressCode != null and item.addressCode != ''">
                address_code = #{item.addressCode,jdbcType=VARCHAR},
            </if>
            <if test="item.addressName != null and item.addressName != ''">
                address_name = #{item.addressName,jdbcType=VARCHAR},
            </if>
            <if test="item.nameOrigin != null and item.nameOrigin != ''">
                name_origin = #{item.nameOrigin,jdbcType=VARCHAR},
            </if>
            <if test="item.placesType != null and item.placesType != ''">
                places_type = #{item.placesType,jdbcType=VARCHAR},
            </if>
            <if test="item.latitude != null and item.latitude != ''">
                latitude = #{item.latitude,jdbcType=VARCHAR},
            </if>
            <if test="item.longitude != null and item.longitude != ''">
                longitude = #{item.longitude,jdbcType=VARCHAR},
            </if>
            <if test="item.shippingHouse != null and item.shippingHouse != ''">
                shipping_house = #{item.shippingHouse,jdbcType=VARCHAR},
            </if>
            <if test="item.mmsi != null and item.mmsi != ''">
                mmsi = #{item.mmsi,jdbcType=VARCHAR},
            </if>
            <if test="item.voyage != null and item.voyage != ''">
                voyage = #{item.voyage,jdbcType=VARCHAR},
            </if>
            <if test="item.etdTime != null">
                etd_time = #{item.etdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.etaTime != null">
                eta_time = #{item.etaTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.etaLsTime != null">
                eta_ls_time = #{item.etaLsTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.ataTime != null">
                ata_time = #{item.ataTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.atdTime != null">
                atd_time = #{item.atdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.startTime != null">
                start_time = #{item.startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_inventory_ocean_freight_places
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="port_name_ls = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.portNameLs,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="name_cn = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.nameCn,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.endTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="bl_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.blId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="address_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.addressCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="address_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.addressName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="name_origin = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.nameOrigin,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="places_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.placesType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="latitude = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.latitude,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="longitude = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.longitude,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="shipping_house = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.shippingHouse,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mmsi = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.mmsi,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="voyage = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.voyage,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="etd_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.etdTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="eta_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.etaTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="eta_ls_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.etaLsTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="ata_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ataTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="atd_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.atdTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.startTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mrp_inventory_ocean_freight_places 
        <set>
            <if test="item.portNameLs != null and item.portNameLs != ''">
                port_name_ls = #{item.portNameLs,jdbcType=VARCHAR},
            </if>
            <if test="item.nameCn != null and item.nameCn != ''">
                name_cn = #{item.nameCn,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.endTime != null">
                end_time = #{item.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.blId != null and item.blId != ''">
                bl_id = #{item.blId,jdbcType=VARCHAR},
            </if>
            <if test="item.addressCode != null and item.addressCode != ''">
                address_code = #{item.addressCode,jdbcType=VARCHAR},
            </if>
            <if test="item.addressName != null and item.addressName != ''">
                address_name = #{item.addressName,jdbcType=VARCHAR},
            </if>
            <if test="item.nameOrigin != null and item.nameOrigin != ''">
                name_origin = #{item.nameOrigin,jdbcType=VARCHAR},
            </if>
            <if test="item.placesType != null and item.placesType != ''">
                places_type = #{item.placesType,jdbcType=VARCHAR},
            </if>
            <if test="item.latitude != null and item.latitude != ''">
                latitude = #{item.latitude,jdbcType=VARCHAR},
            </if>
            <if test="item.longitude != null and item.longitude != ''">
                longitude = #{item.longitude,jdbcType=VARCHAR},
            </if>
            <if test="item.shippingHouse != null and item.shippingHouse != ''">
                shipping_house = #{item.shippingHouse,jdbcType=VARCHAR},
            </if>
            <if test="item.mmsi != null and item.mmsi != ''">
                mmsi = #{item.mmsi,jdbcType=VARCHAR},
            </if>
            <if test="item.voyage != null and item.voyage != ''">
                voyage = #{item.voyage,jdbcType=VARCHAR},
            </if>
            <if test="item.etdTime != null">
                etd_time = #{item.etdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.etaTime != null">
                eta_time = #{item.etaTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.etaLsTime != null">
                eta_ls_time = #{item.etaLsTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.ataTime != null">
                ata_time = #{item.ataTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.atdTime != null">
                atd_time = #{item.atdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.startTime != null">
                start_time = #{item.startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mrp_inventory_ocean_freight_places where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_inventory_ocean_freight_places where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
