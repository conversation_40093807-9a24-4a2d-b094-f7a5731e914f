package com.yhl.scp.mrp.compare.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.mrp.compare.domain.entity.MaterialChangeCompareDO;
import com.yhl.scp.mrp.compare.infrastructure.dao.MaterialChangeCompareDao;
import com.yhl.scp.mrp.compare.infrastructure.po.MaterialChangeComparePO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>MaterialChangeCompareDomainService</code>
 * <p>
 * 材料计划变动提醒-材料对比领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-17 15:59:59
 */
@Service
public class MaterialChangeCompareDomainService {

    @Resource
    private MaterialChangeCompareDao materialChangeCompareDao;

    /**
     * 数据校验
     *
     * @param materialChangeCompareDO 领域对象
     */
    public void validation(MaterialChangeCompareDO materialChangeCompareDO) {
        checkNotNull(materialChangeCompareDO);
        checkUniqueCode(materialChangeCompareDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param materialChangeCompareDO 领域对象
     */
    private void checkNotNull(MaterialChangeCompareDO materialChangeCompareDO) {

    }

    /**
     * 唯一性校验
     *
     * @param materialChangeCompareDO 领域对象
     */
    private void checkUniqueCode(MaterialChangeCompareDO materialChangeCompareDO) {

    }

}
