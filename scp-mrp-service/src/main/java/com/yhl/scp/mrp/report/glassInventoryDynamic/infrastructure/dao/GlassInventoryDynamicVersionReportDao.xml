<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.dao.GlassInventoryDynamicVersionReportDao">
    <resultMap id="BaseResultMap"
               type="com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.po.GlassInventoryDynamicVersionReportPO">
        <!--@Table mrp_glass_inventory_dynamic_version_report-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="glass_next_one_month_growth_rate" jdbcType="VARCHAR" property="glassNextOneMonthGrowthRate"/>
        <result column="glass_next_two_month_growth_rate" jdbcType="VARCHAR" property="glassNextTwoMonthGrowthRate"/>
        <result column="glass_next_three_month_growth_rate" jdbcType="VARCHAR"
                property="glassNextThreeMonthGrowthRate"/>
        <result column="main_operation_output_current_month" jdbcType="VARCHAR"
                property="mainOperationOutputCurrentMonth"/>
        <result column="main_operation_output_next_one_month" jdbcType="VARCHAR"
                property="mainOperationOutputNextOneMonth"/>
        <result column="main_operation_output_next_two_month" jdbcType="VARCHAR"
                property="mainOperationOutputNextTwoMonth"/>
        <result column="main_operation_output_next_three_month" jdbcType="VARCHAR"
                property="mainOperationOutputNextThreeMonth"/>
        <result column="plan_next_one_month_growth_rate" jdbcType="VARCHAR" property="planNextOneMonthGrowthRate"/>
        <result column="plan_next_two_month_growth_rate" jdbcType="VARCHAR" property="planNextTwoMonthGrowthRate"/>
        <result column="plan_next_three_month_growth_rate" jdbcType="VARCHAR" property="planNextThreeMonthGrowthRate"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.report.glassInventoryDynamic.vo.GlassInventoryDynamicVersionReportVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,glass_next_one_month_growth_rate,glass_next_two_month_growth_rate,glass_next_three_month_growth_rate,main_operation_output_current_month,main_operation_output_next_one_month,main_operation_output_next_two_month,main_operation_output_next_three_month,plan_next_one_month_growth_rate,plan_next_two_month_growth_rate,plan_next_three_month_growth_rate,enabled,creator,create_time,modifier,modify_time,remark,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.glassNextOneMonthGrowthRate != null and params.glassNextOneMonthGrowthRate != ''">
                and glass_next_one_month_growth_rate = #{params.glassNextOneMonthGrowthRate,jdbcType=VARCHAR}
            </if>
            <if test="params.glassNextTwoMonthGrowthRate != null and params.glassNextTwoMonthGrowthRate != ''">
                and glass_next_two_month_growth_rate = #{params.glassNextTwoMonthGrowthRate,jdbcType=VARCHAR}
            </if>
            <if test="params.glassNextThreeMonthGrowthRate != null and params.glassNextThreeMonthGrowthRate != ''">
                and glass_next_three_month_growth_rate = #{params.glassNextThreeMonthGrowthRate,jdbcType=VARCHAR}
            </if>
            <if test="params.mainOperationOutputCurrentMonth != null">
                and main_operation_output_current_month = #{params.mainOperationOutputCurrentMonth,jdbcType=VARCHAR}
            </if>
            <if test="params.mainOperationOutputNextOneMonth != null">
                and main_operation_output_next_one_month = #{params.mainOperationOutputNextOneMonth,jdbcType=VARCHAR}
            </if>
            <if test="params.mainOperationOutputNextTwoMonth != null">
                and main_operation_output_next_two_month = #{params.mainOperationOutputNextTwoMonth,jdbcType=VARCHAR}
            </if>
            <if test="params.mainOperationOutputNextThreeMonth != null">
                and main_operation_output_next_three_month =
                #{params.mainOperationOutputNextThreeMonth,jdbcType=VARCHAR}
            </if>
            <if test="params.planNextOneMonthGrowthRate != null and params.planNextOneMonthGrowthRate != ''">
                and plan_next_one_month_growth_rate = #{params.planNextOneMonthGrowthRate,jdbcType=VARCHAR}
            </if>
            <if test="params.planNextTwoMonthGrowthRate != null and params.planNextTwoMonthGrowthRate != ''">
                and plan_next_two_month_growth_rate = #{params.planNextTwoMonthGrowthRate,jdbcType=VARCHAR}
            </if>
            <if test="params.planNextThreeMonthGrowthRate != null and params.planNextThreeMonthGrowthRate != ''">
                and plan_next_three_month_growth_rate = #{params.planNextThreeMonthGrowthRate,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_glass_inventory_dynamic_version_report
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_glass_inventory_dynamic_version_report
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mrp_glass_inventory_dynamic_version_report
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_glass_inventory_dynamic_version_report
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert"
            parameterType="com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.po.GlassInventoryDynamicVersionReportPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_glass_inventory_dynamic_version_report(
        id,
        glass_next_one_month_growth_rate,
        glass_next_two_month_growth_rate,
        glass_next_three_month_growth_rate,
        main_operation_output_current_month,
        main_operation_output_next_one_month,
        main_operation_output_next_two_month,
        main_operation_output_next_three_month,
        plan_next_one_month_growth_rate,
        plan_next_two_month_growth_rate,
        plan_next_three_month_growth_rate,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        remark,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{glassNextOneMonthGrowthRate,jdbcType=VARCHAR},
        #{glassNextTwoMonthGrowthRate,jdbcType=VARCHAR},
        #{glassNextThreeMonthGrowthRate,jdbcType=VARCHAR},
        #{mainOperationOutputCurrentMonth,jdbcType=VARCHAR},
        #{mainOperationOutputNextOneMonth,jdbcType=VARCHAR},
        #{mainOperationOutputNextTwoMonth,jdbcType=VARCHAR},
        #{mainOperationOutputNextThreeMonth,jdbcType=VARCHAR},
        #{planNextOneMonthGrowthRate,jdbcType=VARCHAR},
        #{planNextTwoMonthGrowthRate,jdbcType=VARCHAR},
        #{planNextThreeMonthGrowthRate,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.po.GlassInventoryDynamicVersionReportPO">
        insert into mrp_glass_inventory_dynamic_version_report(id,
                                                               glass_next_one_month_growth_rate,
                                                               glass_next_two_month_growth_rate,
                                                               glass_next_three_month_growth_rate,
                                                               main_operation_output_current_month,
                                                               main_operation_output_next_one_month,
                                                               main_operation_output_next_two_month,
                                                               main_operation_output_next_three_month,
                                                               plan_next_one_month_growth_rate,
                                                               plan_next_two_month_growth_rate,
                                                               plan_next_three_month_growth_rate,
                                                               enabled,
                                                               creator,
                                                               create_time,
                                                               modifier,
                                                               modify_time,
                                                               remark,
                                                               version_value)
        values (#{id,jdbcType=VARCHAR},
                #{glassNextOneMonthGrowthRate,jdbcType=VARCHAR},
                #{glassNextTwoMonthGrowthRate,jdbcType=VARCHAR},
                #{glassNextThreeMonthGrowthRate,jdbcType=VARCHAR},
                #{mainOperationOutputCurrentMonth,jdbcType=VARCHAR},
                #{mainOperationOutputNextOneMonth,jdbcType=VARCHAR},
                #{mainOperationOutputNextTwoMonth,jdbcType=VARCHAR},
                #{mainOperationOutputNextThreeMonth,jdbcType=VARCHAR},
                #{planNextOneMonthGrowthRate,jdbcType=VARCHAR},
                #{planNextTwoMonthGrowthRate,jdbcType=VARCHAR},
                #{planNextThreeMonthGrowthRate,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{remark,jdbcType=VARCHAR},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_glass_inventory_dynamic_version_report(
        id,
        glass_next_one_month_growth_rate,
        glass_next_two_month_growth_rate,
        glass_next_three_month_growth_rate,
        main_operation_output_current_month,
        main_operation_output_next_one_month,
        main_operation_output_next_two_month,
        main_operation_output_next_three_month,
        plan_next_one_month_growth_rate,
        plan_next_two_month_growth_rate,
        plan_next_three_month_growth_rate,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        remark,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.glassNextOneMonthGrowthRate,jdbcType=VARCHAR},
            #{entity.glassNextTwoMonthGrowthRate,jdbcType=VARCHAR},
            #{entity.glassNextThreeMonthGrowthRate,jdbcType=VARCHAR},
            #{entity.mainOperationOutputCurrentMonth,jdbcType=VARCHAR},
            #{entity.mainOperationOutputNextOneMonth,jdbcType=VARCHAR},
            #{entity.mainOperationOutputNextTwoMonth,jdbcType=VARCHAR},
            #{entity.mainOperationOutputNextThreeMonth,jdbcType=VARCHAR},
            #{entity.planNextOneMonthGrowthRate,jdbcType=VARCHAR},
            #{entity.planNextTwoMonthGrowthRate,jdbcType=VARCHAR},
            #{entity.planNextThreeMonthGrowthRate,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_glass_inventory_dynamic_version_report(
        id,
        glass_next_one_month_growth_rate,
        glass_next_two_month_growth_rate,
        glass_next_three_month_growth_rate,
        main_operation_output_current_month,
        main_operation_output_next_one_month,
        main_operation_output_next_two_month,
        main_operation_output_next_three_month,
        plan_next_one_month_growth_rate,
        plan_next_two_month_growth_rate,
        plan_next_three_month_growth_rate,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        remark,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.glassNextOneMonthGrowthRate,jdbcType=VARCHAR},
            #{entity.glassNextTwoMonthGrowthRate,jdbcType=VARCHAR},
            #{entity.glassNextThreeMonthGrowthRate,jdbcType=VARCHAR},
            #{entity.mainOperationOutputCurrentMonth,jdbcType=VARCHAR},
            #{entity.mainOperationOutputNextOneMonth,jdbcType=VARCHAR},
            #{entity.mainOperationOutputNextTwoMonth,jdbcType=VARCHAR},
            #{entity.mainOperationOutputNextThreeMonth,jdbcType=VARCHAR},
            #{entity.planNextOneMonthGrowthRate,jdbcType=VARCHAR},
            #{entity.planNextTwoMonthGrowthRate,jdbcType=VARCHAR},
            #{entity.planNextThreeMonthGrowthRate,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update"
            parameterType="com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.po.GlassInventoryDynamicVersionReportPO">
        update mrp_glass_inventory_dynamic_version_report
        set glass_next_one_month_growth_rate       = #{glassNextOneMonthGrowthRate,jdbcType=VARCHAR},
            glass_next_two_month_growth_rate       = #{glassNextTwoMonthGrowthRate,jdbcType=VARCHAR},
            glass_next_three_month_growth_rate     = #{glassNextThreeMonthGrowthRate,jdbcType=VARCHAR},
            main_operation_output_current_month    = #{mainOperationOutputCurrentMonth,jdbcType=VARCHAR},
            main_operation_output_next_one_month   = #{mainOperationOutputNextOneMonth,jdbcType=VARCHAR},
            main_operation_output_next_two_month   = #{mainOperationOutputNextTwoMonth,jdbcType=VARCHAR},
            main_operation_output_next_three_month = #{mainOperationOutputNextThreeMonth,jdbcType=VARCHAR},
            plan_next_one_month_growth_rate        = #{planNextOneMonthGrowthRate,jdbcType=VARCHAR},
            plan_next_two_month_growth_rate        = #{planNextTwoMonthGrowthRate,jdbcType=VARCHAR},
            plan_next_three_month_growth_rate      = #{planNextThreeMonthGrowthRate,jdbcType=VARCHAR},
            enabled                                = #{enabled,jdbcType=VARCHAR},
            modifier                               = #{modifier,jdbcType=VARCHAR},
            modify_time                            = #{modifyTime,jdbcType=TIMESTAMP},
            remark             = #{remark,jdbcType=VARCHAR},
            version_value      = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.po.GlassInventoryDynamicVersionReportPO">
        update mrp_glass_inventory_dynamic_version_report
        <set>
            <if test="item.glassNextOneMonthGrowthRate != null and item.glassNextOneMonthGrowthRate != ''">
                glass_next_one_month_growth_rate = #{item.glassNextOneMonthGrowthRate,jdbcType=VARCHAR},
            </if>
            <if test="item.glassNextTwoMonthGrowthRate != null and item.glassNextTwoMonthGrowthRate != ''">
                glass_next_two_month_growth_rate = #{item.glassNextTwoMonthGrowthRate,jdbcType=VARCHAR},
            </if>
            <if test="item.glassNextThreeMonthGrowthRate != null and item.glassNextThreeMonthGrowthRate != ''">
                glass_next_three_month_growth_rate = #{item.glassNextThreeMonthGrowthRate,jdbcType=VARCHAR},
            </if>
            <if test="item.mainOperationOutputCurrentMonth != null">
                main_operation_output_current_month = #{item.mainOperationOutputCurrentMonth,jdbcType=VARCHAR},
            </if>
            <if test="item.mainOperationOutputNextOneMonth != null">
                main_operation_output_next_one_month = #{item.mainOperationOutputNextOneMonth,jdbcType=VARCHAR},
            </if>
            <if test="item.mainOperationOutputNextTwoMonth != null">
                main_operation_output_next_two_month = #{item.mainOperationOutputNextTwoMonth,jdbcType=VARCHAR},
            </if>
            <if test="item.mainOperationOutputNextThreeMonth != null">
                main_operation_output_next_three_month = #{item.mainOperationOutputNextThreeMonth,jdbcType=VARCHAR},
            </if>
            <if test="item.planNextOneMonthGrowthRate != null and item.planNextOneMonthGrowthRate != ''">
                plan_next_one_month_growth_rate = #{item.planNextOneMonthGrowthRate,jdbcType=VARCHAR},
            </if>
            <if test="item.planNextTwoMonthGrowthRate != null and item.planNextTwoMonthGrowthRate != ''">
                plan_next_two_month_growth_rate = #{item.planNextTwoMonthGrowthRate,jdbcType=VARCHAR},
            </if>
            <if test="item.planNextThreeMonthGrowthRate != null and item.planNextThreeMonthGrowthRate != ''">
                plan_next_three_month_growth_rate = #{item.planNextThreeMonthGrowthRate,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_glass_inventory_dynamic_version_report
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="glass_next_one_month_growth_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.glassNextOneMonthGrowthRate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="glass_next_two_month_growth_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.glassNextTwoMonthGrowthRate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="glass_next_three_month_growth_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.glassNextThreeMonthGrowthRate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="main_operation_output_current_month = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.mainOperationOutputCurrentMonth,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="main_operation_output_next_one_month = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.mainOperationOutputNextOneMonth,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="main_operation_output_next_two_month = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.mainOperationOutputNextTwoMonth,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="main_operation_output_next_three_month = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.mainOperationOutputNextThreeMonth,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plan_next_one_month_growth_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planNextOneMonthGrowthRate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plan_next_two_month_growth_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planNextTwoMonthGrowthRate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plan_next_three_month_growth_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planNextThreeMonthGrowthRate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_glass_inventory_dynamic_version_report
            <set>
                <if test="item.glassNextOneMonthGrowthRate != null and item.glassNextOneMonthGrowthRate != ''">
                    glass_next_one_month_growth_rate = #{item.glassNextOneMonthGrowthRate,jdbcType=VARCHAR},
                </if>
                <if test="item.glassNextTwoMonthGrowthRate != null and item.glassNextTwoMonthGrowthRate != ''">
                    glass_next_two_month_growth_rate = #{item.glassNextTwoMonthGrowthRate,jdbcType=VARCHAR},
                </if>
                <if test="item.glassNextThreeMonthGrowthRate != null and item.glassNextThreeMonthGrowthRate != ''">
                    glass_next_three_month_growth_rate = #{item.glassNextThreeMonthGrowthRate,jdbcType=VARCHAR},
                </if>
                <if test="item.mainOperationOutputCurrentMonth != null">
                    main_operation_output_current_month = #{item.mainOperationOutputCurrentMonth,jdbcType=VARCHAR},
                </if>
                <if test="item.mainOperationOutputNextOneMonth != null">
                    main_operation_output_next_one_month = #{item.mainOperationOutputNextOneMonth,jdbcType=VARCHAR},
                </if>
                <if test="item.mainOperationOutputNextTwoMonth != null">
                    main_operation_output_next_two_month = #{item.mainOperationOutputNextTwoMonth,jdbcType=VARCHAR},
                </if>
                <if test="item.mainOperationOutputNextThreeMonth != null">
                    main_operation_output_next_three_month = #{item.mainOperationOutputNextThreeMonth,jdbcType=VARCHAR},
                </if>
                <if test="item.planNextOneMonthGrowthRate != null and item.planNextOneMonthGrowthRate != ''">
                    plan_next_one_month_growth_rate = #{item.planNextOneMonthGrowthRate,jdbcType=VARCHAR},
                </if>
                <if test="item.planNextTwoMonthGrowthRate != null and item.planNextTwoMonthGrowthRate != ''">
                    plan_next_two_month_growth_rate = #{item.planNextTwoMonthGrowthRate,jdbcType=VARCHAR},
                </if>
                <if test="item.planNextThreeMonthGrowthRate != null and item.planNextThreeMonthGrowthRate != ''">
                    plan_next_three_month_growth_rate = #{item.planNextThreeMonthGrowthRate,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_glass_inventory_dynamic_version_report
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_glass_inventory_dynamic_version_report where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
