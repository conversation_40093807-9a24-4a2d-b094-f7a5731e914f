<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.halfsubinventory.infrastructure.dao.WarehouseHalfSubinventoryDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.halfsubinventory.infrastructure.po.WarehouseHalfSubinventoryPO">
        <!--@Table mrp_warehouse_half_subinventory-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="organization" jdbcType="VARCHAR" property="organization"/>
        <result column="auxiliary_subinventory" jdbcType="VARCHAR" property="auxiliarySubinventory"/>
        <result column="half_finished_subinventory" jdbcType="VARCHAR" property="halfFinishedSubinventory"/>
        <result column="auxiliary_organization" jdbcType="VARCHAR" property="auxiliaryOrganization"/>
        <result column="auxiliary_storage" jdbcType="VARCHAR" property="auxiliaryStorage"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.halfsubinventory.vo.WarehouseHalfSubinventoryVO">
        <!-- TODO -->
        <result column="organization_name" jdbcType="VARCHAR" property="organizationName"/>
    </resultMap>
    <sql id="Base_Column_List">
         id
         ,organization,auxiliary_subinventory,half_finished_subinventory,auxiliary_organization,auxiliary_storage,remark
         ,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />,organization_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.organization != null and params.organization != ''">
                and organization = #{params.organization,jdbcType=VARCHAR}
            </if>
            <if test="params.auxiliarySubinventory != null and params.auxiliarySubinventory != ''">
                and auxiliary_subinventory = #{params.auxiliarySubinventory,jdbcType=VARCHAR}
            </if>
            <if test="params.halfFinishedSubinventory != null and params.halfFinishedSubinventory != ''">
                and half_finished_subinventory = #{params.halfFinishedSubinventory,jdbcType=VARCHAR}
            </if>
            <if test="params.auxiliaryOrganization != null and params.auxiliaryOrganization != ''">
                and auxiliary_organization = #{params.auxiliaryOrganization,jdbcType=VARCHAR}
            </if>
            <if test="params.auxiliaryStorage != null and params.auxiliaryStorage != ''">
                and auxiliary_storage = #{params.auxiliaryStorage,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_warehouse_half_subinventory
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_warehouse_half_subinventory
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_mrp_warehouse_half_subinventory
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_warehouse_half_subinventory
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.halfsubinventory.infrastructure.po.WarehouseHalfSubinventoryPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_warehouse_half_subinventory(
        id,
        organization,
        auxiliary_subinventory,
        half_finished_subinventory,
        auxiliary_organization,
        auxiliary_storage,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{organization,jdbcType=VARCHAR},
        #{auxiliarySubinventory,jdbcType=VARCHAR},
        #{halfFinishedSubinventory,jdbcType=VARCHAR},
        #{auxiliaryOrganization,jdbcType=VARCHAR},
        #{auxiliaryStorage,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mrp.halfsubinventory.infrastructure.po.WarehouseHalfSubinventoryPO">
        insert into mrp_warehouse_half_subinventory(
        id,
        organization,
        auxiliary_subinventory,
        half_finished_subinventory,
        auxiliary_organization,
        auxiliary_storage,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{organization,jdbcType=VARCHAR},
        #{auxiliarySubinventory,jdbcType=VARCHAR},
        #{halfFinishedSubinventory,jdbcType=VARCHAR},
        #{auxiliaryOrganization,jdbcType=VARCHAR},
        #{auxiliaryStorage,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_warehouse_half_subinventory(
        id,
        organization,
        auxiliary_subinventory,
        half_finished_subinventory,
        auxiliary_organization,
        auxiliary_storage,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.organization,jdbcType=VARCHAR},
        #{entity.auxiliarySubinventory,jdbcType=VARCHAR},
        #{entity.halfFinishedSubinventory,jdbcType=VARCHAR},
        #{entity.auxiliaryOrganization,jdbcType=VARCHAR},
        #{entity.auxiliaryStorage,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_warehouse_half_subinventory(
        id,
        organization,
        auxiliary_subinventory,
        half_finished_subinventory,
        auxiliary_organization,
        auxiliary_storage,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.organization,jdbcType=VARCHAR},
        #{entity.auxiliarySubinventory,jdbcType=VARCHAR},
        #{entity.halfFinishedSubinventory,jdbcType=VARCHAR},
        #{entity.auxiliaryOrganization,jdbcType=VARCHAR},
        #{entity.auxiliaryStorage,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.halfsubinventory.infrastructure.po.WarehouseHalfSubinventoryPO">
        update mrp_warehouse_half_subinventory set
        organization = #{organization,jdbcType=VARCHAR},
        auxiliary_subinventory = #{auxiliarySubinventory,jdbcType=VARCHAR},
        half_finished_subinventory = #{halfFinishedSubinventory,jdbcType=VARCHAR},
        auxiliary_organization = #{auxiliaryOrganization,jdbcType=VARCHAR},
        auxiliary_storage = #{auxiliaryStorage,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value      = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mrp.halfsubinventory.infrastructure.po.WarehouseHalfSubinventoryPO">
        update mrp_warehouse_half_subinventory
        <set>
            <if test="item.organization != null and item.organization != ''">
                organization = #{item.organization,jdbcType=VARCHAR},
            </if>
            <if test="item.auxiliarySubinventory != null and item.auxiliarySubinventory != ''">
                auxiliary_subinventory = #{item.auxiliarySubinventory,jdbcType=VARCHAR},
            </if>
            <if test="item.halfFinishedSubinventory != null and item.halfFinishedSubinventory != ''">
                half_finished_subinventory = #{item.halfFinishedSubinventory,jdbcType=VARCHAR},
            </if>
            <if test="item.auxiliaryOrganization != null and item.auxiliaryOrganization != ''">
                auxiliary_organization = #{item.auxiliaryOrganization,jdbcType=VARCHAR},
            </if>
            <if test="item.auxiliaryStorage != null and item.auxiliaryStorage != ''">
                auxiliary_storage = #{item.auxiliaryStorage,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_warehouse_half_subinventory
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="organization = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.organization,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="auxiliary_subinventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.auxiliarySubinventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="half_finished_subinventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.halfFinishedSubinventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="auxiliary_organization = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.auxiliaryOrganization,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="auxiliary_storage = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.auxiliaryStorage,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mrp_warehouse_half_subinventory 
        <set>
            <if test="item.organization != null and item.organization != ''">
                organization = #{item.organization,jdbcType=VARCHAR},
            </if>
            <if test="item.auxiliarySubinventory != null and item.auxiliarySubinventory != ''">
                auxiliary_subinventory = #{item.auxiliarySubinventory,jdbcType=VARCHAR},
            </if>
            <if test="item.halfFinishedSubinventory != null and item.halfFinishedSubinventory != ''">
                half_finished_subinventory = #{item.halfFinishedSubinventory,jdbcType=VARCHAR},
            </if>
            <if test="item.auxiliaryOrganization != null and item.auxiliaryOrganization != ''">
                auxiliary_organization = #{item.auxiliaryOrganization,jdbcType=VARCHAR},
            </if>
            <if test="item.auxiliaryStorage != null and item.auxiliaryStorage != ''">
                auxiliary_storage = #{item.auxiliaryStorage,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mrp_warehouse_half_subinventory where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_warehouse_half_subinventory where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
