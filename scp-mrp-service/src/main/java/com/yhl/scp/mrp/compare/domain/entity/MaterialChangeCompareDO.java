package com.yhl.scp.mrp.compare.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MaterialChangeCompareDO</code>
 * <p>
 * 材料计划变动提醒-材料对比DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-17 15:59:59
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialChangeCompareDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 810880783786174005L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 材料编码
     */
    private String materialCode;
    /**
     * 车型编码
     */
    private String vehicleModelCode;
    /**
     * 供应商编码
     */
    private String supplierCode;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 采购需求第1周现数量
     */
    private BigDecimal purchaseDemandOneWeekCurrentQuantity;
    /**
     * 采购需求第1周变化量
     */
    private BigDecimal purchaseDemandOneWeekChangeQuantity;
    /**
     * 采购需求第2周现数量
     */
    private BigDecimal purchaseDemandTwoWeekCurrentQuantity;
    /**
     * 采购需求第2周变化量
     */
    private BigDecimal purchaseDemandTwoWeekChangeQuantity;
    /**
     * 采购需求第3周现数量
     */
    private BigDecimal purchaseDemandThreeWeekCurrentQuantity;
    /**
     * 采购需求第3周变化量
     */
    private BigDecimal purchaseDemandThreeWeekChangeQuantity;
    /**
     * 采购需求第4周现数量
     */
    private BigDecimal purchaseDemandFourWeekCurrentQuantity;
    /**
     * 采购需求第4周变化量
     */
    private BigDecimal purchaseDemandFourWeekChangeQuantity;
    /**
     * 采购需求第1月现数量
     */
    private BigDecimal purchaseDemandOneMonthCurrentQuantity;
    /**
     * 采购需求第1月变化量
     */
    private BigDecimal purchaseDemandOneMonthChangeQuantity;
    /**
     * 采购需求第2月现数量
     */
    private BigDecimal purchaseDemandTwoMonthCurrentQuantity;
    /**
     * 采购需求第2月变化量
     */
    private BigDecimal purchaseDemandTwoMonthChangeQuantity;
    /**
     * 采购需求第3月现数量
     */
    private BigDecimal purchaseDemandThreeMonthCurrentQuantity;
    /**
     * 采购需求第3月变化量
     */
    private BigDecimal purchaseDemandThreeMonthChangeQuantity;
    /**
     * 采购需求第4月现数量
     */
    private BigDecimal purchaseDemandFourMonthCurrentQuantity;
    /**
     * 采购需求第4月变化量
     */
    private BigDecimal purchaseDemandFourMonthChangeQuantity;
    /**
     * 采购需求第5月现数量
     */
    private BigDecimal purchaseDemandFiveMonthCurrentQuantity;
    /**
     * 采购需求第5月变化量
     */
    private BigDecimal purchaseDemandFiveMonthChangeQuantity;
    /**
     * 采购需求第6月现数量
     */
    private BigDecimal purchaseDemandSixMonthCurrentQuantity;
    /**
     * 采购需求第6月变化量
     */
    private BigDecimal purchaseDemandSixMonthChangeQuantity;
    /**
     * 采购需求第7月现数量
     */
    private BigDecimal purchaseDemandSenveMonthCurrentQuantity;
    /**
     * 采购需求第7月变化量
     */
    private BigDecimal purchaseDemandSenveMonthChangeQuantity;
    /**
     * 采购需求第8月现数量
     */
    private BigDecimal purchaseDemandEightMonthCurrentQuantity;
    /**
     * 采购需求第8月变化量
     */
    private BigDecimal purchaseDemandEightMonthChangeQuantity;
    /**
     * 波动率
     */
    private BigDecimal volatility;
    /**
     * 版本
     */
    private Integer versionValue;

}
