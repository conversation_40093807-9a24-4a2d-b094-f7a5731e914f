<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.material.forecast.infrastructure.dao.MaterialLongTermForecastIssueDetailDao">
    <resultMap id="BaseResultMap"
               type="com.yhl.scp.mrp.material.forecast.infrastructure.po.MaterialLongTermForecastIssueDetailPO">
        <!--@Table mrp_material_long_term_forecast_issue_detail-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="demand_date" jdbcType="TIMESTAMP" property="demandDate"/>
        <result column="demand_quantity" jdbcType="VARCHAR" property="demandQuantity"/>
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="demand_pattern" jdbcType="VARCHAR" property="demandPattern"/>
        <result column="issue_version" jdbcType="INTEGER" property="issueVersion"/>
        <result column="issue_no" jdbcType="VARCHAR" property="issueNo"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.material.forecast.vo.MaterialLongTermForecastIssueDetailVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,product_code,product_name,demand_date,demand_quantity,supplier_code,supplier_name,demand_pattern,issue_version,issue_no,status,remark,enabled,creator,create_time,modifier,modify_time,version_value

    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productName != null and params.productName != ''">
                and product_name = #{params.productName,jdbcType=VARCHAR}
            </if>
            <if test="params.demandDate != null">
                and demand_date = #{params.demandDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.demandQuantity != null">
                and demand_quantity = #{params.demandQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierCode != null and params.supplierCode != ''">
                and supplier_code = #{params.supplierCode,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierName != null and params.supplierName != ''">
                and supplier_name = #{params.supplierName,jdbcType=VARCHAR}
            </if>
            <if test="params.demandPattern != null and params.demandPattern != ''">
                and demand_pattern = #{params.demandPattern,jdbcType=VARCHAR}
            </if>
            <if test="params.issueVersion != null">
                and issue_version = #{params.issueVersion,jdbcType=INTEGER}
            </if>
            <if test="params.issueNo != null and params.issueNo != ''">
                and issue_no = #{params.issueNo,jdbcType=VARCHAR}
            </if>
            <if test="params.issueNos != null and params.issueNos.size() > 0">
                and issue_no in
                <foreach collection="params.issueNos" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.status != null and params.status != ''">
                and status = #{params.status,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_long_term_forecast_issue_detail
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_long_term_forecast_issue_detail
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mrp_material_long_term_forecast_issue_detail
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_long_term_forecast_issue_detail
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert"
            parameterType="com.yhl.scp.mrp.material.forecast.infrastructure.po.MaterialLongTermForecastIssueDetailPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_material_long_term_forecast_issue_detail(
        id,
        product_code,
        product_name,
        demand_date,
        demand_quantity,
        supplier_code,
        supplier_name,
        demand_pattern,
        issue_version,
        issue_no,
        status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{demandDate,jdbcType=TIMESTAMP},
        #{demandQuantity,jdbcType=VARCHAR},
        #{supplierCode,jdbcType=VARCHAR},
        #{supplierName,jdbcType=VARCHAR},
        #{demandPattern,jdbcType=VARCHAR},
        #{issueVersion,jdbcType=INTEGER},
        #{issueNo,jdbcType=VARCHAR},
        #{status,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.material.forecast.infrastructure.po.MaterialLongTermForecastIssueDetailPO">
        insert into mrp_material_long_term_forecast_issue_detail(id,
                                                                 product_code,
                                                                 product_name,
                                                                 demand_date,
                                                                 demand_quantity,
                                                                 supplier_code,
                                                                 supplier_name,
                                                                 demand_pattern,
                                                                 issue_version,
                                                                 issue_no,
                                                                 status,
                                                                 remark,
                                                                 enabled,
                                                                 creator,
                                                                 create_time,
                                                                 modifier,
                                                                 modify_time,
                                                                 version_value)
        values (#{id,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{productName,jdbcType=VARCHAR},
                #{demandDate,jdbcType=TIMESTAMP},
                #{demandQuantity,jdbcType=VARCHAR},
                #{supplierCode,jdbcType=VARCHAR},
                #{supplierName,jdbcType=VARCHAR},
                #{demandPattern,jdbcType=VARCHAR},
                #{issueVersion,jdbcType=INTEGER},
                #{issueNo,jdbcType=VARCHAR},
                #{status,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_long_term_forecast_issue_detail(
        id,
        product_code,
        product_name,
        demand_date,
        demand_quantity,
        supplier_code,
        supplier_name,
        demand_pattern,
        issue_version,
        issue_no,
        status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productName,jdbcType=VARCHAR},
            #{entity.demandDate,jdbcType=TIMESTAMP},
            #{entity.demandQuantity,jdbcType=VARCHAR},
            #{entity.supplierCode,jdbcType=VARCHAR},
            #{entity.supplierName,jdbcType=VARCHAR},
            #{entity.demandPattern,jdbcType=VARCHAR},
            #{entity.issueVersion,jdbcType=INTEGER},
            #{entity.issueNo,jdbcType=VARCHAR},
            #{entity.status,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_material_long_term_forecast_issue_detail(
        id,
        product_code,
        product_name,
        demand_date,
        demand_quantity,
        supplier_code,
        supplier_name,
        demand_pattern,
        issue_version,
        issue_no,
        status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productName,jdbcType=VARCHAR},
            #{entity.demandDate,jdbcType=TIMESTAMP},
            #{entity.demandQuantity,jdbcType=VARCHAR},
            #{entity.supplierCode,jdbcType=VARCHAR},
            #{entity.supplierName,jdbcType=VARCHAR},
            #{entity.demandPattern,jdbcType=VARCHAR},
            #{entity.issueVersion,jdbcType=INTEGER},
            #{entity.issueNo,jdbcType=VARCHAR},
            #{entity.status,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update"
            parameterType="com.yhl.scp.mrp.material.forecast.infrastructure.po.MaterialLongTermForecastIssueDetailPO">
        update mrp_material_long_term_forecast_issue_detail
        set product_code    = #{productCode,jdbcType=VARCHAR},
            product_name    = #{productName,jdbcType=VARCHAR},
            demand_date     = #{demandDate,jdbcType=TIMESTAMP},
            demand_quantity = #{demandQuantity,jdbcType=VARCHAR},
            supplier_code   = #{supplierCode,jdbcType=VARCHAR},
            supplier_name   = #{supplierName,jdbcType=VARCHAR},
            demand_pattern  = #{demandPattern,jdbcType=VARCHAR},
            issue_version   = #{issueVersion,jdbcType=INTEGER},
            issue_no        = #{issueNo,jdbcType=VARCHAR},
            status          = #{status,jdbcType=VARCHAR},
            remark          = #{remark,jdbcType=VARCHAR},
            enabled         = #{enabled,jdbcType=VARCHAR},
            modifier        = #{modifier,jdbcType=VARCHAR},
            modify_time     = #{modifyTime,jdbcType=TIMESTAMP},
            version_value   = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.material.forecast.infrastructure.po.MaterialLongTermForecastIssueDetailPO">
        update mrp_material_long_term_forecast_issue_detail
        <set>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productName != null and item.productName != ''">
                product_name = #{item.productName,jdbcType=VARCHAR},
            </if>
            <if test="item.demandDate != null">
                demand_date = #{item.demandDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.demandQuantity != null">
                demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierCode != null and item.supplierCode != ''">
                supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierName != null and item.supplierName != ''">
                supplier_name = #{item.supplierName,jdbcType=VARCHAR},
            </if>
            <if test="item.demandPattern != null and item.demandPattern != ''">
                demand_pattern = #{item.demandPattern,jdbcType=VARCHAR},
            </if>
            <if test="item.issueVersion != null">
                issue_version = #{item.issueVersion,jdbcType=INTEGER},
            </if>
            <if test="item.issueNo != null and item.issueNo != ''">
                issue_no = #{item.issueNo,jdbcType=VARCHAR},
            </if>
            <if test="item.status != null and item.status != ''">
                status = #{item.status,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_long_term_forecast_issue_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="demand_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_pattern = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandPattern,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="issue_version = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.issueVersion,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="issue_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.issueNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.status,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_material_long_term_forecast_issue_detail
            <set>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productName != null and item.productName != ''">
                    product_name = #{item.productName,jdbcType=VARCHAR},
                </if>
                <if test="item.demandDate != null">
                    demand_date = #{item.demandDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.demandQuantity != null">
                    demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierCode != null and item.supplierCode != ''">
                    supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierName != null and item.supplierName != ''">
                    supplier_name = #{item.supplierName,jdbcType=VARCHAR},
                </if>
                <if test="item.demandPattern != null and item.demandPattern != ''">
                    demand_pattern = #{item.demandPattern,jdbcType=VARCHAR},
                </if>
                <if test="item.issueVersion != null">
                    issue_version = #{item.issueVersion,jdbcType=INTEGER},
                </if>
                <if test="item.issueNo != null and item.issueNo != ''">
                    issue_no = #{item.issueNo,jdbcType=VARCHAR},
                </if>
                <if test="item.status != null and item.status != ''">
                    status = #{item.status,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_material_long_term_forecast_issue_detail
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_long_term_forecast_issue_detail where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
