package com.yhl.scp.mrp.inventory.service.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.yhl.scp.biz.common.excel.SheetStyleHandler;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.utils.BulkOperationUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpOriginalFilmFFInventory;
import com.yhl.scp.dcp.feign.NewDcpFeign;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.NewIpsFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.feign.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mrp.enums.AlternativeTypeEnum;
import com.yhl.scp.mrp.enums.SourceTypeEnum;
import com.yhl.scp.mrp.inventory.convertor.InventoryFloatGlassDetailConvertor;
import com.yhl.scp.mrp.inventory.domain.entity.InventoryFloatGlassDetailDO;
import com.yhl.scp.mrp.inventory.domain.service.InventoryFloatGlassDetailDomainService;
import com.yhl.scp.mrp.inventory.dto.*;
import com.yhl.scp.mrp.inventory.infrastructure.dao.InventoryFloatGlassDetailDao;
import com.yhl.scp.mrp.inventory.infrastructure.po.InventoryFloatGlassDetailPO;
import com.yhl.scp.mrp.inventory.service.InventoryAlternativeRelationshipService;
import com.yhl.scp.mrp.inventory.service.InventoryFloatGlassDetailService;
import com.yhl.scp.mrp.inventory.vo.InventoryAlternativeRelationshipVO;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassDetailVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <code>InventoryFloatGlassDetailServiceImpl</code>
 * <p>
 * 原片浮法库存批次明细应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 20:25:16
 */
@Slf4j
@Service
public class InventoryFloatGlassDetailServiceImpl extends AbstractService implements InventoryFloatGlassDetailService {

    @Resource
    private InventoryFloatGlassDetailDao inventoryFloatGlassDetailDao;

    @Resource
    private InventoryFloatGlassDetailDomainService inventoryFloatGlassDetailDomainService;

    @Resource
    private InventoryAlternativeRelationshipService inventoryAlternativeRelationshipService;
    @Resource
    private NewDcpFeign newDcpFeign;
    @Resource
    private NewMdsFeign mdsFeign;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private NewIpsFeign newIpsFeign;
    @Resource
    private IpsFeign ipsFeign;
    @Override
    public BaseResponse<Void> doCreate(InventoryFloatGlassDetailDTO inventoryFloatGlassDetailDTO) {
        // 0.数据转换
        InventoryFloatGlassDetailDO inventoryFloatGlassDetailDO = InventoryFloatGlassDetailConvertor.INSTANCE.dto2Do(inventoryFloatGlassDetailDTO);
        InventoryFloatGlassDetailPO inventoryFloatGlassDetailPO = InventoryFloatGlassDetailConvertor.INSTANCE.dto2Po(inventoryFloatGlassDetailDTO);
        // 1.数据校验
        inventoryFloatGlassDetailDomainService.validation(inventoryFloatGlassDetailDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(inventoryFloatGlassDetailPO);
        if (StringUtils.isEmpty(inventoryFloatGlassDetailPO.getSourceType())){
            inventoryFloatGlassDetailPO.setSourceType(SourceTypeEnum.MANUAL.getCode());
        }
        inventoryFloatGlassDetailDao.insert(inventoryFloatGlassDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(InventoryFloatGlassDetailDTO inventoryFloatGlassDetailDTO) {
        // 0.数据转换
        InventoryFloatGlassDetailDO inventoryFloatGlassDetailDO = InventoryFloatGlassDetailConvertor.INSTANCE.dto2Do(inventoryFloatGlassDetailDTO);
        InventoryFloatGlassDetailPO inventoryFloatGlassDetailPO = InventoryFloatGlassDetailConvertor.INSTANCE.dto2Po(inventoryFloatGlassDetailDTO);
        // 1.数据校验
        inventoryFloatGlassDetailDomainService.validation(inventoryFloatGlassDetailDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(inventoryFloatGlassDetailPO);
        inventoryFloatGlassDetailDao.update(inventoryFloatGlassDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<InventoryFloatGlassDetailDTO> list) {
        List<InventoryFloatGlassDetailPO> newList = InventoryFloatGlassDetailConvertor.INSTANCE.dto2Pos(list);

        for (InventoryFloatGlassDetailDTO inventoryFloatGlassDetailDTO : list) {
            if (StringUtils.isEmpty(inventoryFloatGlassDetailDTO.getSourceType())){
                inventoryFloatGlassDetailDTO.setSourceType(SourceTypeEnum.MANUAL.getCode());
            }
        }
        BasePOUtils.insertBatchFiller(newList);
        inventoryFloatGlassDetailDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<InventoryFloatGlassDetailDTO> list) {
        List<InventoryFloatGlassDetailPO> newList = InventoryFloatGlassDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        inventoryFloatGlassDetailDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return inventoryFloatGlassDetailDao.deleteBatch(idList);
        }
        return inventoryFloatGlassDetailDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public InventoryFloatGlassDetailVO selectByPrimaryKey(String id) {
        InventoryFloatGlassDetailPO po = inventoryFloatGlassDetailDao.selectByPrimaryKey(id);
        return InventoryFloatGlassDetailConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_sds_material_inventory_float_glass_detail")
    public List<InventoryFloatGlassDetailVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_sds_material_inventory_float_glass_detail")
    public List<InventoryFloatGlassDetailVO> selectByPage(Pagination pagination, String sortParam,
                                                          String queryCriteriaParam, String overdueSort) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam, overdueSort);
    }

    @Override
    public List<InventoryAlternativeRelationshipVO> availableSearch(String productCode, String alternativeType, String cuttingRatePercentage) {
        // 积压
        if (alternativeType.equals(AlternativeTypeEnum.BACKLOG.getCode()))
            return inventoryAlternativeRelationshipService.backlogReplace(productCode, cuttingRatePercentage);
            // 缺料
        else if (alternativeType.equals(AlternativeTypeEnum.MATERIAL_SHORTAGE.getCode()))
            return inventoryAlternativeRelationshipService.materialShortageReplace(productCode, cuttingRatePercentage);
        return Collections.emptyList();
    }

    @Override
    public BaseResponse<Void> alternative(List<InventoryAlternativeRelationshipDTO> list) {
        List<String> demandProductCodes = list.stream().map(InventoryAlternativeRelationshipDTO::getDemandProductCode).distinct().collect(Collectors.toList());
        List<InventoryAlternativeRelationshipVO> inventoryAlternativeRelationshipVOList = inventoryAlternativeRelationshipService.selectByParams(ImmutableMap.of("demandProductCodes", demandProductCodes));
        Map<String, List<InventoryAlternativeRelationshipVO>> inventoryAlternativeRelationshipMap = inventoryAlternativeRelationshipVOList.stream()
                .collect(Collectors.groupingBy(data -> data.getDemandProductCode() + "&" + data.getReplacedProductCode()));

        // 校验该原片和替代原片，是否已设置关系且开始结束时间范围有交叉，有交叉的不允许重复保存
        for (InventoryAlternativeRelationshipDTO inventoryAlternativeRelationshipDTO : list) {
            // 获取当前时间范围
            String timeSlot = DateUtils.formatDate(inventoryAlternativeRelationshipDTO.getStartTime(), "yyyy-MM-dd") + "&" + DateUtils.formatDate(inventoryAlternativeRelationshipDTO.getEndTime(), "yyyy-MM-dd");

            List<InventoryAlternativeRelationshipVO> inventoryAlternativeRelationshipVOS =
                    inventoryAlternativeRelationshipMap.get(inventoryAlternativeRelationshipDTO.getDemandProductCode() + "&" + inventoryAlternativeRelationshipDTO.getReplacedProductCode());

            if (CollectionUtils.isNotEmpty(inventoryAlternativeRelationshipVOS)) {
                // 获取需要检查的时间范围
                List<String> checkTimeSlot = inventoryAlternativeRelationshipVOS.stream()
                        .map(data -> DateUtils.formatDate(data.getStartTime(), "yyyy-MM-dd") + "&" + DateUtils.formatDate(data.getEndTime(), "yyyy-MM-dd")).distinct().collect(Collectors.toList());

                for (String checkSlot : checkTimeSlot) {
                    Date[] currentRange = parseTimeSlot(timeSlot);
                    Date[] checkRange = parseTimeSlot(checkSlot);
                    if (isRangeContained(currentRange[0], currentRange[1], checkRange[0], checkRange[1])) {
                        throw new BusinessException("时间范围有交叉，不允许重复保存");
                    }
                }
            }
        }

        //创建替代关系
        inventoryAlternativeRelationshipService.doCreateBatch(list);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatchWithPartition(List<InventoryFloatGlassDetailDTO> list, Integer pageSize) {
        List<InventoryFloatGlassDetailPO> newList = InventoryFloatGlassDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, (poList) -> inventoryFloatGlassDetailDao.insertBatch(poList), pageSize);
    }

    @Override
    public void doUpdateBatchWithPartition(List<InventoryFloatGlassDetailDTO> list, Integer pageSize) {
        List<InventoryFloatGlassDetailPO> newList = InventoryFloatGlassDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, (poList) -> inventoryFloatGlassDetailDao.updateBatch(poList), pageSize);
    }

    @Override
    public BaseResponse<Void> syncOriginalFilmFFInventory(Scenario scenario) {
        Map stockParams = MapUtil.builder().put("enabled", YesOrNoEnum.YES.getCode()).build();
        List<NewStockPointVO> newStockPointVOS = mdsFeign.selectStockPointByParams(scenario.getDataBaseName(), stockParams);
        if (CollectionUtils.isEmpty(newStockPointVOS)) {
            return BaseResponse.error("库存点信息为空");
        }
        List<NewStockPointVO> saleStockPoints = newStockPointVOS.stream().filter(t ->
                Objects.equals(t.getOrganizeType(), StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode())
                        && Objects.equals(t.getStockPointType(), StockPointTypeEnum.FF.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(saleStockPoints)){
            return BaseResponse.error("浮法库存点信息为空");
        }
        List<NewStockPointVO> purchaseStockPoints = newStockPointVOS.stream().filter(t ->
                Objects.equals(t.getOrganizeType(), StockPointOrganizeTypeEnum.PURCHASE_ORGANIZATION.getCode())
                        && Objects.equals(t.getStockPointType(), StockPointTypeEnum.BC.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(purchaseStockPoints)){
            return BaseResponse.error("本厂采购组织信息为空");
        }
        String customerCode = purchaseStockPoints.get(0).getCustomerCode();
        log.info("客户号为：{}",customerCode);
        List<CollectionValueVO> customerList = ipsFeign.getByCollectionCode("CUST_CODE_MAP");
        if(CollectionUtils.isEmpty(customerList)){
            log.error("CUST_CODE_MAP字典信息为空");
            return BaseResponse.error("CUST_CODE_MAP字典信息为空");
        }
        CollectionValueVO collectionValueVO = customerList.stream().filter(t -> t.getCollectionValue().equals(customerCode)).findFirst().orElse(null);
        if(Objects.isNull(collectionValueVO)){
            log.error("CUST_CODE_MAP字典中未找到customerCode:{}",customerCode);
            return BaseResponse.error("CUST_CODE_MAP字典中未找到customerCode:"+customerCode);
        }
        String customer=collectionValueVO.getValueMeaning();
        Map<String, NewStockPointVO> stockPointMap = saleStockPoints.stream()
                .collect(Collectors.toMap(NewStockPointVO::getStockPointCode, Function.identity(), (v1, v2) -> v1));
        for (String stockPoint : stockPointMap.keySet()) {
            // 调用浮法库存接口
            Map<String, Object> newStockPoingMap = new HashMap<>(4);
            newStockPoingMap.put("customer", customer);
            newStockPoingMap.put("stockPointCode", stockPoint);
            newStockPoingMap.put("orgId", stockPointMap.get(stockPoint).getOrganizeId());
            newStockPoingMap.put("scenario", scenario.getDataBaseName());
            newDcpFeign.callExternalApi(scenario.getTenantId(), ApiSourceEnum.ERP.getCode(),
                    ApiCategoryEnum.ORIGINAL_FILM_FF_INVENTORY.getCode(), newStockPoingMap);
        }
        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> sync(String scenario,List<ErpOriginalFilmFFInventory> o) {

        if (CollectionUtils.isEmpty(o)) {
            log.error("原片浮法库存数据为空");
            return BaseResponse.error("原片浮法库存数据为空");
        }
        List<String> productCodes = o.stream().map(ErpOriginalFilmFFInventory::getItem).distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> newProductStockPointVOS = mdsFeign.selectByProductCode(scenario,productCodes);
        Map<String, String> productStockPointVOMap =
                CollectionUtils.isEmpty(newProductStockPointVOS) ?
                        MapUtil.newHashMap() :
                        newProductStockPointVOS.stream().collect(
                                Collectors.toMap(t -> t.getStockPointCode() + "|" + t.getProductCode(),
                                        NewProductStockPointVO::getId, (v1, v2) -> v1));

        List<InventoryFloatGlassDetailDTO> insertErpOriginalFilmFFInventoryList= Lists.newArrayList();

        for (ErpOriginalFilmFFInventory erpOriginalFilmFFInventory : o) {
            InventoryFloatGlassDetailDTO inventoryFloatGlassDetailDTO = new InventoryFloatGlassDetailDTO();

            inventoryFloatGlassDetailDTO.setEnabled(YesOrNoEnum.YES.getCode());
            inventoryFloatGlassDetailDTO.setProductCode(erpOriginalFilmFFInventory.getItem());
            inventoryFloatGlassDetailDTO.setLotNumber(erpOriginalFilmFFInventory.getLotNumber());
            inventoryFloatGlassDetailDTO.setLotLevelCode(erpOriginalFilmFFInventory.getGradeChange());
            if(Objects.nonNull(erpOriginalFilmFFInventory.getArea())){
                inventoryFloatGlassDetailDTO.setArea(new BigDecimal(erpOriginalFilmFFInventory.getArea()));
            }
            if(Objects.nonNull(erpOriginalFilmFFInventory.getPerBox())){
                inventoryFloatGlassDetailDTO.setPerBox(new BigDecimal(erpOriginalFilmFFInventory.getPerBox()));
            }
            if(Objects.nonNull(erpOriginalFilmFFInventory.getWeight())){
                inventoryFloatGlassDetailDTO.setWeight(new BigDecimal(erpOriginalFilmFFInventory.getWeight()));
            }
            if(Objects.nonNull(erpOriginalFilmFFInventory.getWeightBox())){
                inventoryFloatGlassDetailDTO.setWeightBox(new BigDecimal(erpOriginalFilmFFInventory.getWeightBox()));
            }
            if(Objects.nonNull(erpOriginalFilmFFInventory.getQty())){
                BigDecimal qty=new BigDecimal(erpOriginalFilmFFInventory.getQty());
                inventoryFloatGlassDetailDTO.setQty(qty);

                if("超期".equals(erpOriginalFilmFFInventory.getExtended())){
                    inventoryFloatGlassDetailDTO.setOverdueCount(qty);
                    log.info("原片浮法库存接口超期数据，更新超期件数为0");
                }
            }
            inventoryFloatGlassDetailDTO.setLevel(erpOriginalFilmFFInventory.getGrade());
            inventoryFloatGlassDetailDTO.setProductSpec(erpOriginalFilmFFInventory.getSpecifications());
            // 计划使用量=现存件数*（片/箱）
            inventoryFloatGlassDetailDTO.setOrgId(erpOriginalFilmFFInventory.getOrgId());
            inventoryFloatGlassDetailDTO.setStockPointCode(erpOriginalFilmFFInventory.getOrgCode());
            inventoryFloatGlassDetailDTO.setSub(erpOriginalFilmFFInventory.getSub());
            inventoryFloatGlassDetailDTO.setLocation(erpOriginalFilmFFInventory.getLocation());
            inventoryFloatGlassDetailDTO.setStorageTime(erpOriginalFilmFFInventory.getDateReceived());
            String productKey = erpOriginalFilmFFInventory.getOrgCode() + "|" + erpOriginalFilmFFInventory.getItem();
            if(productStockPointVOMap.containsKey(productKey)) {
                inventoryFloatGlassDetailDTO.setProductId(productStockPointVOMap.get(productKey));
                log.info("原片浮法库存接口数据匹配到库存点，更新物料id为{}",inventoryFloatGlassDetailDTO.getProductId());
            }
            inventoryFloatGlassDetailDTO.setSourceType(SourceTypeEnum.INTERFACE.getCode());
            insertErpOriginalFilmFFInventoryList.add(inventoryFloatGlassDetailDTO);
        }
        if(CollectionUtils.isNotEmpty(insertErpOriginalFilmFFInventoryList)){
            this.doDeleteByStockPointCode(insertErpOriginalFilmFFInventoryList.get(0).getStockPointCode());
            com.google.common.collect.Lists.partition(insertErpOriginalFilmFFInventoryList,500).forEach(this::doCreateBatch);
            log.info("原片浮法库存批次新增数据条数：{}", insertErpOriginalFilmFFInventoryList.size());
        }

        return BaseResponse.success("同步成功");
    }

    @Override
    @Expression(value = "v_sds_material_inventory_float_glass_detail")
    public List<InventoryFloatGlassDetailVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<InventoryFloatGlassDetailVO> dataList = inventoryFloatGlassDetailDao.selectByCondition(sortParam, queryCriteriaParam);
        InventoryFloatGlassDetailServiceImpl target = SpringBeanUtils.getBean(InventoryFloatGlassDetailServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Expression(value = "v_sds_material_inventory_float_glass_detail")
    public List<InventoryFloatGlassDetailVO> selectByCondition(String sortParam, String queryCriteriaParam,
                                                               String overdueSort) {
        List<InventoryFloatGlassDetailVO> dataList =
                inventoryFloatGlassDetailDao.selectByCondition(sortParam, queryCriteriaParam, overdueSort);
        if (CollectionUtils.isNotEmpty(dataList)){
            for (InventoryFloatGlassDetailVO inventoryFloatGlassDetailVO : dataList) {
                inventoryFloatGlassDetailVO.setQtySum(inventoryFloatGlassDetailVO.getQty());
                if (null != inventoryFloatGlassDetailVO.getPerBox() && null != inventoryFloatGlassDetailVO.getQty()){
                    inventoryFloatGlassDetailVO.setQtySum(inventoryFloatGlassDetailVO.getQty().multiply(inventoryFloatGlassDetailVO.getPerBox()));
                }
            }
        }
        InventoryFloatGlassDetailServiceImpl target = SpringBeanUtils.getBean(InventoryFloatGlassDetailServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<InventoryFloatGlassDetailVO> selectByParams(Map<String, Object> params) {
        List<InventoryFloatGlassDetailPO> list = inventoryFloatGlassDetailDao.selectByParams(params);
        return InventoryFloatGlassDetailConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<InventoryFloatGlassDetailVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MATERIAL_INVENTORY_FLOAT_GLASS_DETAIL.getCode();
    }

    @Override
    public List<InventoryFloatGlassDetailVO> invocation(List<InventoryFloatGlassDetailVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public List<InventoryFloatGlassDetailVO> selectProductInventory() {
        return inventoryFloatGlassDetailDao.selectProductInventory();
    }

    @Override
    public int doDeleteByStockPointCode(String stockPointCode) {
        return inventoryFloatGlassDetailDao.doDeleteByStockPointCode(stockPointCode);
    }

    @SneakyThrows
    @Override
    public void exportTemplateOutsourcing(HttpServletResponse response) {
        List<List<String>> headers = com.google.common.collect.Lists.newArrayList();
        headers.add(Collections.singletonList("组织*"));
        headers.add(Collections.singletonList("物品编码*"));
        headers.add(Collections.singletonList("颜色"));
        headers.add(Collections.singletonList("规格"));
        headers.add(Collections.singletonList("厚度"));
        headers.add(Collections.singletonList("等级"));
        headers.add(Collections.singletonList("批次等级代码"));
        headers.add(Collections.singletonList("批次号"));
        headers.add(Collections.singletonList("现存件数*"));
        headers.add(Collections.singletonList("总数量"));
        headers.add(Collections.singletonList("片/箱*"));
        headers.add(Collections.singletonList("吨/箱"));
        headers.add(Collections.singletonList("吨数"));
        headers.add(Collections.singletonList("超期件数"));
        headers.add(Collections.singletonList("计划使用量"));
        headers.add(Collections.singletonList("发货时间"));
        headers.add(Collections.singletonList("面积"));
        headers.add(Collections.singletonList("计划使用量"));
        headers.add(Collections.singletonList("子库存"));
        headers.add(Collections.singletonList("货位"));
        headers.add(Collections.singletonList("入库时间"));

        List<List<Object>> exampleData = new ArrayList<>();
        List<Object> exampleRow = new ArrayList<>();
        exampleRow.add("SJG");
        exampleRow.add("ABT115513950180C");
        exampleRow.add("YP");
        exampleRow.add("1155*1395");
        exampleRow.add(BigDecimal.ONE);
        exampleRow.add("T");
        exampleRow.add("E");
        exampleRow.add("B220240419CFSHQFY0XE");
        exampleRow.add(BigDecimal.ONE);
        exampleRow.add(BigDecimal.ONE);
        exampleRow.add(BigDecimal.ONE);
        exampleRow.add(BigDecimal.ONE);
        exampleRow.add(BigDecimal.ONE);
        exampleRow.add(BigDecimal.ONE);
        exampleRow.add(BigDecimal.ONE);
        exampleRow.add(DateUtils.dateToString(new Date()));
        exampleRow.add(BigDecimal.ONE);
        exampleRow.add(BigDecimal.ONE);
        exampleRow.add("BXCP");
        exampleRow.add("I16");
        exampleRow.add(DateUtils.dateToString(new Date()));
        exampleData.add(exampleRow);

        try (SheetStyleHandler handler = new SheetStyleHandler();
             BufferedOutputStream outputStream = new BufferedOutputStream(response.getOutputStream())) {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename="
                    + URLEncoder.encode("浮法（外购）导入模板.xlsx", "UTF-8"));
            EasyExcelFactory.write(outputStream).sheet().head(headers).registerWriteHandler(handler).doWrite(exampleData);
        } catch (IOException e) {
            throw new BusinessException(e.getMessage());
        }
    }

    @Override
    public void uploadOutsourcing(MultipartFile file) {
        List<InventoryFloatGlassDetailOutsourcingExcelDTO> fileList = null;
        try {
            fileList = EasyExcelFactory.read(file.getInputStream())
                    .head(InventoryFloatGlassDetailOutsourcingExcelDTO.class)
                    .sheet()
                    .doReadSync();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        if (CollectionUtils.isEmpty(fileList)) throw new BusinessException("文件数据为空");

        for (InventoryFloatGlassDetailOutsourcingExcelDTO excelDTO : fileList) {
            if (StringUtils.isEmpty(excelDTO.getProductCode())) throw new BusinessException("物品编码*不能为空");
            if (StringUtils.isEmpty(excelDTO.getStockPointCode())) throw new BusinessException("组织*不能为空");
            if (null == excelDTO.getQty()) throw new BusinessException("现存件数*不能为空");
            if (null == excelDTO.getPerBox()) throw new BusinessException("片/箱*不能为空");
        }

        // 获取库存点 根据库存点编码分组
        Map<String, NewStockPointVO> stockPointVOMap = newMdsFeign.selectAllStockPoint(SystemHolder.getScenario()).stream()
                .collect(Collectors.toMap(NewStockPointVO::getStockPointCode, Function.identity(), (v1, v2) -> v1));

        // 获取数据来源导入类型得浮法库存批次
        List<String> deleteIdList = this.selectByParams(ImmutableMap.of("sourceType", SourceTypeEnum.IMPORT.getCode())).stream()
                .map(InventoryFloatGlassDetailVO::getId).collect(Collectors.toList());

        List<InventoryFloatGlassDetailDTO> addList = new ArrayList<>();

        for (InventoryFloatGlassDetailOutsourcingExcelDTO excelDTO : fileList) {
            InventoryFloatGlassDetailDTO dto = new InventoryFloatGlassDetailDTO();
            BeanUtils.copyProperties(excelDTO, dto);
            dto.setSourceType(SourceTypeEnum.IMPORT.getCode());
            NewStockPointVO newStockPointVO = stockPointVOMap.get(dto.getStockPointCode());
            if (Objects.nonNull(newStockPointVO)){
                dto.setOrgId(newStockPointVO.getId());
            }
            addList.add(dto);
        }

        if (CollectionUtils.isNotEmpty(addList)) {
            com.google.common.collect.Lists.partition(addList, 500).forEach(this::doCreateBatch);
        }

        if (CollectionUtils.isNotEmpty(deleteIdList)) {
            com.google.common.collect.Lists.partition(deleteIdList, 500).forEach(this::doDelete);
        }
    }

    private Date[] parseTimeSlot(String timeSlot) {
        String[] parts = timeSlot.split("&");
        Date start = DateUtils.stringToDate(parts[0], "yyyy-MM-dd");
        Date end = DateUtils.stringToDate(parts[1], "yyyy-MM-dd");
        return new Date[]{start, end};
    }

    private boolean isRangeContained(Date start1, Date end1, Date start2, Date end2) {
        return start1.compareTo(start2) >= 0 && end1.compareTo(end2) <= 0;
    }
}
