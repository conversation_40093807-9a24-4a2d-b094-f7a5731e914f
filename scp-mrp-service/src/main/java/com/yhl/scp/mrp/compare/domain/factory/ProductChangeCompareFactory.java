package com.yhl.scp.mrp.compare.domain.factory;

import com.yhl.scp.mrp.compare.domain.entity.ProductChangeCompareDO;
import com.yhl.scp.mrp.compare.dto.ProductChangeCompareDTO;
import com.yhl.scp.mrp.compare.infrastructure.dao.ProductChangeCompareDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>ProductChangeCompare2Factory</code>
 * <p>
 * 材料计划变动提醒-产品对比领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-18 09:56:44
 */
@Component
public class ProductChangeCompareFactory {

    @Resource
    private ProductChangeCompareDao productChangeCompareDao;

    ProductChangeCompareDO create(ProductChangeCompareDTO dto) {
        // TODO
        return null;
    }

}
