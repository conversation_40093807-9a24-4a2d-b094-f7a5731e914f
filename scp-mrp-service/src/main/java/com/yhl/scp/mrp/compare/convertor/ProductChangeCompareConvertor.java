package com.yhl.scp.mrp.compare.convertor;

import com.yhl.scp.mrp.compare.domain.entity.ProductChangeCompareDO;
import com.yhl.scp.mrp.compare.dto.ProductChangeCompareDTO;
import com.yhl.scp.mrp.compare.infrastructure.po.ProductChangeComparePO;
import com.yhl.scp.mrp.compare.vo.ProductChangeCompareVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>ProductChangeCompare2Convertor</code>
 * <p>
 * 材料计划变动提醒-产品对比转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-18 09:56:44
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProductChangeCompareConvertor {

    ProductChangeCompareConvertor INSTANCE = Mappers.getMapper(ProductChangeCompareConvertor.class);

    ProductChangeCompareDO dto2Do(ProductChangeCompareDTO obj);

    ProductChangeCompareDTO do2Dto(ProductChangeCompareDO obj);

    List<ProductChangeCompareDO> dto2Dos(List<ProductChangeCompareDTO> list);

    List<ProductChangeCompareDTO> do2Dtos(List<ProductChangeCompareDO> list);

    ProductChangeCompareVO do2Vo(ProductChangeCompareDO obj);

    ProductChangeCompareVO po2Vo(ProductChangeComparePO obj);

    List<ProductChangeCompareVO> po2Vos(List<ProductChangeComparePO> list);

    ProductChangeComparePO do2Po(ProductChangeCompareDO obj);

    ProductChangeCompareDO po2Do(ProductChangeComparePO obj);

    ProductChangeComparePO dto2Po(ProductChangeCompareDTO obj);

    List<ProductChangeComparePO> dto2Pos(List<ProductChangeCompareDTO> obj);

}
