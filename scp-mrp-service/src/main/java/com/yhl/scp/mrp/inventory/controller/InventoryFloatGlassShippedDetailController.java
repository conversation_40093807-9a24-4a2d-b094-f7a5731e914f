package com.yhl.scp.mrp.inventory.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mrp.inventory.dto.InventoryFloatGlassShippedDetailDTO;
import com.yhl.scp.mrp.inventory.service.InventoryFloatGlassShippedDetailService;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassShippedDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <code>InventoryFloatGlassShippedDetailController</code>
 * <p>
 * 原片浮法已发运库存批次明细控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 20:25:15
 */
@Slf4j
@Api(tags = "原片浮法已发运库存批次明细控制器")
@RestController
@RequestMapping("inventoryFloatGlassShippedDetail")
public class InventoryFloatGlassShippedDetailController extends BaseController {

    @Resource
    private InventoryFloatGlassShippedDetailService inventoryFloatGlassShippedDetailService;
    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<InventoryFloatGlassShippedDetailVO>> page() {
        List<InventoryFloatGlassShippedDetailVO> inventoryFloatGlassShippedDetailList = inventoryFloatGlassShippedDetailService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<InventoryFloatGlassShippedDetailVO> pageInfo = new PageInfo<>(inventoryFloatGlassShippedDetailList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody InventoryFloatGlassShippedDetailDTO inventoryFloatGlassShippedDetailDTO) {
        return inventoryFloatGlassShippedDetailService.doCreate(inventoryFloatGlassShippedDetailDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody InventoryFloatGlassShippedDetailDTO inventoryFloatGlassShippedDetailDTO) {
        return inventoryFloatGlassShippedDetailService.doUpdate(inventoryFloatGlassShippedDetailDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        inventoryFloatGlassShippedDetailService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<InventoryFloatGlassShippedDetailVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, inventoryFloatGlassShippedDetailService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "导入")
    @PostMapping(value = "upload")
    public BaseResponse<Void> upload(@RequestPart MultipartFile file) {
        try {
            inventoryFloatGlassShippedDetailService.doUpload(file);
            return BaseResponse.success(BaseResponse.OP_SUCCESS);
        } catch (Exception e) {
            log.error("浮法已发运导入失败", e);
            throw new BusinessException(e.getLocalizedMessage());
        }
    }

    @ApiOperation(value = "模板导出（内部）")
    @GetMapping(value = "exportTemplateInternal")
    public void exportTemplate(HttpServletResponse response) {
        inventoryFloatGlassShippedDetailService.exportTemplateInternal(response);
    }

    @ApiOperation(value = "导入（内部）")
    @PostMapping(value = "uploadInternal")
    public BaseResponse<String> uploadInternal(@RequestPart MultipartFile file) {
        try {
            return inventoryFloatGlassShippedDetailService.doUploadInternal(file);
        } catch (Exception e) {
            log.error("浮法已发运（内部）导入失败", e);
            throw new BusinessException(e.getLocalizedMessage());
        }
    }

    @ApiOperation(value = "模板导出（外购）")
    @GetMapping(value = "exportTemplateOutsourcing")
    public void exportTemplateOutsourcing(HttpServletResponse response) {
        inventoryFloatGlassShippedDetailService.exportTemplateOutsourcing(response);
    }

    @ApiOperation(value = "导入（外购）")
    @PostMapping(value = "uploadOutsourcing")
    public BaseResponse<Void> uploadOutsourcing(@RequestPart MultipartFile file) {
        inventoryFloatGlassShippedDetailService.uploadOutsourcing(file);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "同步")
    @PostMapping(value = "sync")
    public BaseResponse<Void> syncInventoryFloatGlassShippedDetail() {
        Scenario scenario=new Scenario();
        scenario.setTenantId( SystemHolder.getTenantId());
        scenario.setDataBaseName(SystemHolder.getScenario());
        return inventoryFloatGlassShippedDetailService.syncOriginalFilmFFOnway(scenario);
    }
    @ApiOperation("下载错误数据")
    @GetMapping(value = "downloadErrorData")
    public void downloadErrorData(HttpServletResponse response) {
        inventoryFloatGlassShippedDetailService.downloadErrorData(response);
    }

    @ApiOperation(value = "批量创建PO")
    @PostMapping(value = "batchCreatPo")
    @BusinessMonitorLog(businessCode = "创建PO", moduleCode = "MRP", businessFrequency = "DAY")
    public BaseResponse<String> doBatchCreatPo(@RequestBody List<String> ids) {
        String result = inventoryFloatGlassShippedDetailService.doBatchCreatPo(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, result);
    }
    @ApiOperation(value = "自动创建PO")
    @PostMapping(value = "syncAutoCreatPo")
    public BaseResponse<String> syncAutoCreatPo() {
        String result = inventoryFloatGlassShippedDetailService.syncAutoCreatPo(SystemHolder.getTenantCode(),
                SystemHolder.getScenario());
        return BaseResponse.success(BaseResponse.OP_SUCCESS, result);
    }
}
