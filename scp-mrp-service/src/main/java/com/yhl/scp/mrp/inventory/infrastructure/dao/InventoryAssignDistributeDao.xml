<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.inventory.infrastructure.dao.InventoryAssignDistributeDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryAssignDistributePO">
        <!--@Table mrp_material_inventory_assign_distribute-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="inventory_batch_detail_id" jdbcType="VARCHAR" property="inventoryBatchDetailId"/>
        <result column="detail_product_code" jdbcType="VARCHAR" property="detailProductCode"/>
        <result column="finished_product_code" jdbcType="VARCHAR" property="finishedProductCode"/>
        <result column="finished_to_glass_product_code" jdbcType="VARCHAR" property="finishedToGlassProductCode"/>
        <result column="distribute_quantity" jdbcType="VARCHAR" property="distributeQuantity"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.inventory.vo.InventoryAssignDistributeVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,inventory_batch_detail_id,detail_product_code,finished_product_code,finished_to_glass_product_code,distribute_quantity,start_time,end_time,remark,enabled,creator,create_time,modifier,modify_time,version_value

    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryBatchDetailId != null and params.inventoryBatchDetailId != ''">
                and inventory_batch_detail_id = #{params.inventoryBatchDetailId,jdbcType=VARCHAR}
            </if>
            <if test="params.detailProductCode != null and params.detailProductCode != ''">
                and detail_product_code = #{params.detailProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.finishedProductCode != null and params.finishedProductCode != ''">
                and finished_product_code = #{params.finishedProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.finishedToGlassProductCode != null and params.finishedToGlassProductCode != ''">
                and finished_to_glass_product_code = #{params.finishedToGlassProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.distributeQuantity != null">
                and distribute_quantity = #{params.distributeQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.startTime != null">
                and start_time = #{params.startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.endTime != null">
                and end_time = #{params.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_inventory_assign_distribute
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_inventory_assign_distribute
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mrp_material_inventory_assign_distribute
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_inventory_assign_distribute
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryAssignDistributePO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_material_inventory_assign_distribute(
        id,
        inventory_batch_detail_id,
        detail_product_code,
        finished_product_code,
        finished_to_glass_product_code,
        distribute_quantity,
        start_time,
        end_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{inventoryBatchDetailId,jdbcType=VARCHAR},
        #{detailProductCode,jdbcType=VARCHAR},
        #{finishedProductCode,jdbcType=VARCHAR},
        #{finishedToGlassProductCode,jdbcType=VARCHAR},
        #{distributeQuantity,jdbcType=VARCHAR},
        #{startTime,jdbcType=TIMESTAMP},
        #{endTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryAssignDistributePO">
        insert into mrp_material_inventory_assign_distribute(id,
                                                             inventory_batch_detail_id,
                                                             detail_product_code,
                                                             finished_product_code,
                                                             finished_to_glass_product_code,
                                                             distribute_quantity,
                                                             start_time,
                                                             end_time,
                                                             remark,
                                                             enabled,
                                                             creator,
                                                             create_time,
                                                             modifier,
                                                             modify_time,
                                                             version_value)
        values (#{id,jdbcType=VARCHAR},
                #{inventoryBatchDetailId,jdbcType=VARCHAR},
                #{detailProductCode,jdbcType=VARCHAR},
                #{finishedProductCode,jdbcType=VARCHAR},
                #{finishedToGlassProductCode,jdbcType=VARCHAR},
                #{distributeQuantity,jdbcType=VARCHAR},
                #{startTime,jdbcType=TIMESTAMP},
                #{endTime,jdbcType=TIMESTAMP},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_inventory_assign_distribute(
        id,
        inventory_batch_detail_id,
        detail_product_code,
        finished_product_code,
        finished_to_glass_product_code,
        distribute_quantity,
        start_time,
        end_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.inventoryBatchDetailId,jdbcType=VARCHAR},
            #{entity.detailProductCode,jdbcType=VARCHAR},
            #{entity.finishedProductCode,jdbcType=VARCHAR},
            #{entity.finishedToGlassProductCode,jdbcType=VARCHAR},
            #{entity.distributeQuantity,jdbcType=VARCHAR},
            #{entity.startTime,jdbcType=TIMESTAMP},
            #{entity.endTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_material_inventory_assign_distribute(
        id,
        inventory_batch_detail_id,
        detail_product_code,
        finished_product_code,
        finished_to_glass_product_code,
        distribute_quantity,
        start_time,
        end_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.inventoryBatchDetailId,jdbcType=VARCHAR},
            #{entity.detailProductCode,jdbcType=VARCHAR},
            #{entity.finishedProductCode,jdbcType=VARCHAR},
            #{entity.finishedToGlassProductCode,jdbcType=VARCHAR},
            #{entity.distributeQuantity,jdbcType=VARCHAR},
            #{entity.startTime,jdbcType=TIMESTAMP},
            #{entity.endTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryAssignDistributePO">
        update mrp_material_inventory_assign_distribute
        set inventory_batch_detail_id      = #{inventoryBatchDetailId,jdbcType=VARCHAR},
            detail_product_code            = #{detailProductCode,jdbcType=VARCHAR},
            finished_product_code          = #{finishedProductCode,jdbcType=VARCHAR},
            finished_to_glass_product_code = #{finishedToGlassProductCode,jdbcType=VARCHAR},
            distribute_quantity            = #{distributeQuantity,jdbcType=VARCHAR},
            start_time                     = #{startTime,jdbcType=TIMESTAMP},
            end_time                       = #{endTime,jdbcType=TIMESTAMP},
            remark                         = #{remark,jdbcType=VARCHAR},
            enabled                        = #{enabled,jdbcType=VARCHAR},
            modifier                       = #{modifier,jdbcType=VARCHAR},
            modify_time                    = #{modifyTime,jdbcType=TIMESTAMP},
            version_value      = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryAssignDistributePO">
        update mrp_material_inventory_assign_distribute
        <set>
            <if test="item.inventoryBatchDetailId != null and item.inventoryBatchDetailId != ''">
                inventory_batch_detail_id = #{item.inventoryBatchDetailId,jdbcType=VARCHAR},
            </if>
            <if test="item.detailProductCode != null and item.detailProductCode != ''">
                detail_product_code = #{item.detailProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.finishedProductCode != null and item.finishedProductCode != ''">
                finished_product_code = #{item.finishedProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.finishedToGlassProductCode != null and item.finishedToGlassProductCode != ''">
                finished_to_glass_product_code = #{item.finishedToGlassProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.distributeQuantity != null">
                distribute_quantity = #{item.distributeQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.startTime != null">
                start_time = #{item.startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.endTime != null">
                end_time = #{item.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_inventory_assign_distribute
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="inventory_batch_detail_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inventoryBatchDetailId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="detail_product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.detailProductCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="finished_product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.finishedProductCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="finished_to_glass_product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.finishedToGlassProductCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="distribute_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.distributeQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.startTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.endTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_material_inventory_assign_distribute
            <set>
                <if test="item.inventoryBatchDetailId != null and item.inventoryBatchDetailId != ''">
                    inventory_batch_detail_id = #{item.inventoryBatchDetailId,jdbcType=VARCHAR},
                </if>
                <if test="item.detailProductCode != null and item.detailProductCode != ''">
                    detail_product_code = #{item.detailProductCode,jdbcType=VARCHAR},
                </if>
                <if test="item.finishedProductCode != null and item.finishedProductCode != ''">
                    finished_product_code = #{item.finishedProductCode,jdbcType=VARCHAR},
                </if>
                <if test="item.finishedToGlassProductCode != null and item.finishedToGlassProductCode != ''">
                    finished_to_glass_product_code = #{item.finishedToGlassProductCode,jdbcType=VARCHAR},
                </if>
                <if test="item.distributeQuantity != null">
                    distribute_quantity = #{item.distributeQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.startTime != null">
                    start_time = #{item.startTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.endTime != null">
                    end_time = #{item.endTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_material_inventory_assign_distribute
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_inventory_assign_distribute where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
