<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.material.plan.infrastructure.dao.GlassInventoryShiftDetailPublishedDao">
    <resultMap id="BaseResultMap"
               type="com.yhl.scp.mrp.material.plan.infrastructure.po.GlassInventoryShiftDetailPublishedPO">
        <!--@Table mrp_glass_inventory_shift_detail_published-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="inventory_shift_data_id" jdbcType="VARCHAR" property="inventoryShiftDataId"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="stock_point_type" jdbcType="VARCHAR" property="stockPointType"/>
        <result column="inventory_date" jdbcType="TIMESTAMP" property="inventoryDate"/>
        <result column="all_ff_inventory" jdbcType="VARCHAR" property="allFfInventory"/>
        <result column="safety_stock_level_min" jdbcType="VARCHAR" property="safetyStockLevelMin"/>
        <result column="safety_stock_level_standard" jdbcType="VARCHAR" property="safetyStockLevelStandard"/>
        <result column="safety_stock_level_max" jdbcType="VARCHAR" property="safetyStockLevelMax"/>
        <result column="opening_inventory" jdbcType="VARCHAR" property="openingInventory"/>
        <result column="bc_opening_inventory" jdbcType="VARCHAR" property="bcOpeningInventory"/>
        <result column="demand_quantity" jdbcType="VARCHAR" property="demandQuantity"/>
        <result column="adjust_quantity_from_float" jdbcType="VARCHAR" property="adjustQuantityFromFloat"/>
        <result column="input_quantity" jdbcType="VARCHAR" property="inputQuantity"/>
        <result column="adjust_quantity_from_port" jdbcType="VARCHAR" property="adjustQuantityFromPort"/>
        <result column="transit_quantity_from_port" jdbcType="VARCHAR" property="transitQuantityFromPort"/>
        <result column="transit_quantity_from_float" jdbcType="VARCHAR" property="transitQuantityFromFloat"/>
        <result column="output_quantity_to_bc" jdbcType="VARCHAR" property="outputQuantityToBc"/>
        <result column="output_quantity_to_port" jdbcType="VARCHAR" property="outputQuantityToPort"/>
        <result column="decision_output_quantity_to_bc" jdbcType="VARCHAR" property="decisionOutputQuantityToBc"/>
        <result column="decision_output_quantity_to_port" jdbcType="VARCHAR" property="decisionOutputQuantityToPort"/>
        <result column="use_replace_quantity" jdbcType="VARCHAR" property="useReplaceQuantity"/>
        <result column="used_as_replace_quantity" jdbcType="VARCHAR" property="usedAsReplaceQuantity"/>
        <result column="safety_stock_gap" jdbcType="VARCHAR" property="safetyStockGap"/>
        <result column="ending_inventory" jdbcType="VARCHAR" property="endingInventory"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.material.plan.vo.GlassInventoryShiftDetailPublishedVO">
        <result column="warning_remind_start" jdbcType="TIMESTAMP" property="warningRemindStart"/>
        <result column="warning_remind_end" jdbcType="TIMESTAMP" property="warningRemindEnd"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,inventory_shift_data_id,stock_point_code,stock_point_type,inventory_date,all_ff_inventory,safety_stock_level_min,safety_stock_level_standard,safety_stock_level_max,opening_inventory,bc_opening_inventory,demand_quantity,adjust_quantity_from_float,input_quantity,adjust_quantity_from_port,transit_quantity_from_port,transit_quantity_from_float,output_quantity_to_bc,output_quantity_to_port,decision_output_quantity_to_bc,decision_output_quantity_to_port,use_replace_quantity,used_as_replace_quantity,safety_stock_gap,ending_inventory,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List"/>,warning_remind_start,warning_remind_end
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryShiftDataId != null and params.inventoryShiftDataId != ''">
                and inventory_shift_data_id = #{params.inventoryShiftDataId,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointType != null and params.stockPointType != ''">
                and stock_point_type = #{params.stockPointType,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryDate != null">
                and inventory_date = #{params.inventoryDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.allFfInventory != null">
                and all_ff_inventory = #{params.allFfInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.safetyStockLevelMin != null">
                and safety_stock_level_min = #{params.safetyStockLevelMin,jdbcType=VARCHAR}
            </if>
            <if test="params.safetyStockLevelStandard != null">
                and safety_stock_level_standard = #{params.safetyStockLevelStandard,jdbcType=VARCHAR}
            </if>
            <if test="params.safetyStockLevelMax != null">
                and safety_stock_level_max = #{params.safetyStockLevelMax,jdbcType=VARCHAR}
            </if>
            <if test="params.openingInventory != null">
                and opening_inventory = #{params.openingInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.bcOpeningInventory != null">
                and bc_opening_inventory = #{params.bcOpeningInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.demandQuantity != null">
                and demand_quantity = #{params.demandQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.adjustQuantityFromFloat != null">
                and adjust_quantity_from_float = #{params.adjustQuantityFromFloat,jdbcType=VARCHAR}
            </if>
            <if test="params.inputQuantity != null">
                and input_quantity = #{params.inputQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.adjustQuantityFromPort != null">
                and adjust_quantity_from_port = #{params.adjustQuantityFromPort,jdbcType=VARCHAR}
            </if>
            <if test="params.transitQuantityFromPort != null">
                and transit_quantity_from_port = #{params.transitQuantityFromPort,jdbcType=VARCHAR}
            </if>
            <if test="params.transitQuantityFromFloat != null">
                and transit_quantity_from_float = #{params.transitQuantityFromFloat,jdbcType=VARCHAR}
            </if>
            <if test="params.outputQuantityToBc != null">
                and output_quantity_to_bc = #{params.outputQuantityToBc,jdbcType=VARCHAR}
            </if>
            <if test="params.outputQuantityToPort != null">
                and output_quantity_to_port = #{params.outputQuantityToPort,jdbcType=VARCHAR}
            </if>
            <if test="params.decisionOutputQuantityToBc != null">
                and decision_output_quantity_to_bc = #{params.decisionOutputQuantityToBc,jdbcType=VARCHAR}
            </if>
            <if test="params.decisionOutputQuantityToPort != null">
                and decision_output_quantity_to_port = #{params.decisionOutputQuantityToPort,jdbcType=VARCHAR}
            </if>
            <if test="params.useReplaceQuantity != null">
                and use_replace_quantity = #{params.useReplaceQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.usedAsReplaceQuantity != null">
                and used_as_replace_quantity = #{params.usedAsReplaceQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.safetyStockGap != null">
                and safety_stock_gap = #{params.safetyStockGap,jdbcType=VARCHAR}
            </if>
            <if test="params.endingInventory != null">
                and ending_inventory = #{params.endingInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_glass_inventory_shift_detail_published
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_glass_inventory_shift_detail_published
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mrp_glass_inventory_shift_detail_published
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_glass_inventory_shift_detail_published
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mrp_glass_inventory_shift_detail_published
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert"
            parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.GlassInventoryShiftDetailPublishedPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_glass_inventory_shift_detail_published(
        id,
        inventory_shift_data_id,
        stock_point_code,
        stock_point_type,
        inventory_date,
        all_ff_inventory,
        safety_stock_level_min,
        safety_stock_level_standard,
        safety_stock_level_max,
        opening_inventory,
        bc_opening_inventory,
        demand_quantity,
        adjust_quantity_from_float,
        input_quantity,
        adjust_quantity_from_port,
        transit_quantity_from_port,
        transit_quantity_from_float,
        output_quantity_to_bc,
        output_quantity_to_port,
        decision_output_quantity_to_bc,
        decision_output_quantity_to_port,
        use_replace_quantity,
        used_as_replace_quantity,
        safety_stock_gap,
        ending_inventory,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{inventoryShiftDataId,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{stockPointType,jdbcType=VARCHAR},
        #{inventoryDate,jdbcType=TIMESTAMP},
        #{allFfInventory,jdbcType=VARCHAR},
        #{safetyStockLevelMin,jdbcType=VARCHAR},
        #{safetyStockLevelStandard,jdbcType=VARCHAR},
        #{safetyStockLevelMax,jdbcType=VARCHAR},
        #{openingInventory,jdbcType=VARCHAR},
        #{bcOpeningInventory,jdbcType=VARCHAR},
        #{demandQuantity,jdbcType=VARCHAR},
        #{adjustQuantityFromFloat,jdbcType=VARCHAR},
        #{inputQuantity,jdbcType=VARCHAR},
        #{adjustQuantityFromPort,jdbcType=VARCHAR},
        #{transitQuantityFromPort,jdbcType=VARCHAR},
        #{transitQuantityFromFloat,jdbcType=VARCHAR},
        #{outputQuantityToBc,jdbcType=VARCHAR},
        #{outputQuantityToPort,jdbcType=VARCHAR},
        #{decisionOutputQuantityToBc,jdbcType=VARCHAR},
        #{decisionOutputQuantityToPort,jdbcType=VARCHAR},
        #{useReplaceQuantity,jdbcType=VARCHAR},
        #{usedAsReplaceQuantity,jdbcType=VARCHAR},
        #{safetyStockGap,jdbcType=VARCHAR},
        #{endingInventory,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.GlassInventoryShiftDetailPublishedPO">
        insert into mrp_glass_inventory_shift_detail_published(id,
                                                               inventory_shift_data_id,
                                                               stock_point_code,
                                                               stock_point_type,
                                                               inventory_date,
                                                               all_ff_inventory,
                                                               safety_stock_level_min,
                                                               safety_stock_level_standard,
                                                               safety_stock_level_max,
                                                               opening_inventory,
                                                               bc_opening_inventory,
                                                               demand_quantity,
                                                               adjust_quantity_from_float,
                                                               input_quantity,
                                                               adjust_quantity_from_port,
                                                               transit_quantity_from_port,
                                                               transit_quantity_from_float,
                                                               output_quantity_to_bc,
                                                               output_quantity_to_port,
                                                               decision_output_quantity_to_bc,
                                                               decision_output_quantity_to_port,
                                                               use_replace_quantity,
                                                               used_as_replace_quantity,
                                                               safety_stock_gap,
                                                               ending_inventory,
                                                               remark,
                                                               enabled,
                                                               creator,
                                                               create_time,
                                                               modifier,
                                                               modify_time,
                                                               version_value)
        values (#{id,jdbcType=VARCHAR},
                #{inventoryShiftDataId,jdbcType=VARCHAR},
                #{stockPointCode,jdbcType=VARCHAR},
                #{stockPointType,jdbcType=VARCHAR},
                #{inventoryDate,jdbcType=TIMESTAMP},
                #{allFfInventory,jdbcType=VARCHAR},
                #{safetyStockLevelMin,jdbcType=VARCHAR},
                #{safetyStockLevelStandard,jdbcType=VARCHAR},
                #{safetyStockLevelMax,jdbcType=VARCHAR},
                #{openingInventory,jdbcType=VARCHAR},
                #{bcOpeningInventory,jdbcType=VARCHAR},
                #{demandQuantity,jdbcType=VARCHAR},
                #{adjustQuantityFromFloat,jdbcType=VARCHAR},
                #{inputQuantity,jdbcType=VARCHAR},
                #{adjustQuantityFromPort,jdbcType=VARCHAR},
                #{transitQuantityFromPort,jdbcType=VARCHAR},
                #{transitQuantityFromFloat,jdbcType=VARCHAR},
                #{outputQuantityToBc,jdbcType=VARCHAR},
                #{outputQuantityToPort,jdbcType=VARCHAR},
                #{decisionOutputQuantityToBc,jdbcType=VARCHAR},
                #{decisionOutputQuantityToPort,jdbcType=VARCHAR},
                #{useReplaceQuantity,jdbcType=VARCHAR},
                #{usedAsReplaceQuantity,jdbcType=VARCHAR},
                #{safetyStockGap,jdbcType=VARCHAR},
                #{endingInventory,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_glass_inventory_shift_detail_published(
        id,
        inventory_shift_data_id,
        stock_point_code,
        stock_point_type,
        inventory_date,
        all_ff_inventory,
        safety_stock_level_min,
        safety_stock_level_standard,
        safety_stock_level_max,
        opening_inventory,
        bc_opening_inventory,
        demand_quantity,
        adjust_quantity_from_float,
        input_quantity,
        adjust_quantity_from_port,
        transit_quantity_from_port,
        transit_quantity_from_float,
        output_quantity_to_bc,
        output_quantity_to_port,
        decision_output_quantity_to_bc,
        decision_output_quantity_to_port,
        use_replace_quantity,
        used_as_replace_quantity,
        safety_stock_gap,
        ending_inventory,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.inventoryShiftDataId,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.stockPointType,jdbcType=VARCHAR},
            #{entity.inventoryDate,jdbcType=TIMESTAMP},
            #{entity.allFfInventory,jdbcType=VARCHAR},
            #{entity.safetyStockLevelMin,jdbcType=VARCHAR},
            #{entity.safetyStockLevelStandard,jdbcType=VARCHAR},
            #{entity.safetyStockLevelMax,jdbcType=VARCHAR},
            #{entity.openingInventory,jdbcType=VARCHAR},
            #{entity.bcOpeningInventory,jdbcType=VARCHAR},
            #{entity.demandQuantity,jdbcType=VARCHAR},
            #{entity.adjustQuantityFromFloat,jdbcType=VARCHAR},
            #{entity.inputQuantity,jdbcType=VARCHAR},
            #{entity.adjustQuantityFromPort,jdbcType=VARCHAR},
            #{entity.transitQuantityFromPort,jdbcType=VARCHAR},
            #{entity.transitQuantityFromFloat,jdbcType=VARCHAR},
            #{entity.outputQuantityToBc,jdbcType=VARCHAR},
            #{entity.outputQuantityToPort,jdbcType=VARCHAR},
            #{entity.decisionOutputQuantityToBc,jdbcType=VARCHAR},
            #{entity.decisionOutputQuantityToPort,jdbcType=VARCHAR},
            #{entity.useReplaceQuantity,jdbcType=VARCHAR},
            #{entity.usedAsReplaceQuantity,jdbcType=VARCHAR},
            #{entity.safetyStockGap,jdbcType=VARCHAR},
            #{entity.endingInventory,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_glass_inventory_shift_detail_published(
        id,
        inventory_shift_data_id,
        stock_point_code,
        stock_point_type,
        inventory_date,
        all_ff_inventory,
        safety_stock_level_min,
        safety_stock_level_standard,
        safety_stock_level_max,
        opening_inventory,
        bc_opening_inventory,
        demand_quantity,
        adjust_quantity_from_float,
        input_quantity,
        adjust_quantity_from_port,
        transit_quantity_from_port,
        transit_quantity_from_float,
        output_quantity_to_bc,
        output_quantity_to_port,
        decision_output_quantity_to_bc,
        decision_output_quantity_to_port,
        use_replace_quantity,
        used_as_replace_quantity,
        safety_stock_gap,
        ending_inventory,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.inventoryShiftDataId,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.stockPointType,jdbcType=VARCHAR},
            #{entity.inventoryDate,jdbcType=TIMESTAMP},
            #{entity.allFfInventory,jdbcType=VARCHAR},
            #{entity.safetyStockLevelMin,jdbcType=VARCHAR},
            #{entity.safetyStockLevelStandard,jdbcType=VARCHAR},
            #{entity.safetyStockLevelMax,jdbcType=VARCHAR},
            #{entity.openingInventory,jdbcType=VARCHAR},
            #{entity.bcOpeningInventory,jdbcType=VARCHAR},
            #{entity.demandQuantity,jdbcType=VARCHAR},
            #{entity.adjustQuantityFromFloat,jdbcType=VARCHAR},
            #{entity.inputQuantity,jdbcType=VARCHAR},
            #{entity.adjustQuantityFromPort,jdbcType=VARCHAR},
            #{entity.transitQuantityFromPort,jdbcType=VARCHAR},
            #{entity.transitQuantityFromFloat,jdbcType=VARCHAR},
            #{entity.outputQuantityToBc,jdbcType=VARCHAR},
            #{entity.outputQuantityToPort,jdbcType=VARCHAR},
            #{entity.decisionOutputQuantityToBc,jdbcType=VARCHAR},
            #{entity.decisionOutputQuantityToPort,jdbcType=VARCHAR},
            #{entity.useReplaceQuantity,jdbcType=VARCHAR},
            #{entity.usedAsReplaceQuantity,jdbcType=VARCHAR},
            #{entity.safetyStockGap,jdbcType=VARCHAR},
            #{entity.endingInventory,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update"
            parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.GlassInventoryShiftDetailPublishedPO">
        update mrp_glass_inventory_shift_detail_published
        set inventory_shift_data_id          = #{inventoryShiftDataId,jdbcType=VARCHAR},
            stock_point_code                 = #{stockPointCode,jdbcType=VARCHAR},
            stock_point_type                 = #{stockPointType,jdbcType=VARCHAR},
            inventory_date                   = #{inventoryDate,jdbcType=TIMESTAMP},
            all_ff_inventory                 = #{allFfInventory,jdbcType=VARCHAR},
            safety_stock_level_min           = #{safetyStockLevelMin,jdbcType=VARCHAR},
            safety_stock_level_standard      = #{safetyStockLevelStandard,jdbcType=VARCHAR},
            safety_stock_level_max           = #{safetyStockLevelMax,jdbcType=VARCHAR},
            opening_inventory                = #{openingInventory,jdbcType=VARCHAR},
            bc_opening_inventory             = #{bcOpeningInventory,jdbcType=VARCHAR},
            demand_quantity                  = #{demandQuantity,jdbcType=VARCHAR},
            adjust_quantity_from_float       = #{adjustQuantityFromFloat,jdbcType=VARCHAR},
            input_quantity                   = #{inputQuantity,jdbcType=VARCHAR},
            adjust_quantity_from_port        = #{adjustQuantityFromPort,jdbcType=VARCHAR},
            transit_quantity_from_port       = #{transitQuantityFromPort,jdbcType=VARCHAR},
            transit_quantity_from_float      = #{transitQuantityFromFloat,jdbcType=VARCHAR},
            output_quantity_to_bc            = #{outputQuantityToBc,jdbcType=VARCHAR},
            output_quantity_to_port          = #{outputQuantityToPort,jdbcType=VARCHAR},
            decision_output_quantity_to_bc   = #{decisionOutputQuantityToBc,jdbcType=VARCHAR},
            decision_output_quantity_to_port = #{decisionOutputQuantityToPort,jdbcType=VARCHAR},
            use_replace_quantity             = #{useReplaceQuantity,jdbcType=VARCHAR},
            used_as_replace_quantity         = #{usedAsReplaceQuantity,jdbcType=VARCHAR},
            safety_stock_gap                 = #{safetyStockGap,jdbcType=VARCHAR},
            ending_inventory                 = #{endingInventory,jdbcType=VARCHAR},
            remark                           = #{remark,jdbcType=VARCHAR},
            enabled                          = #{enabled,jdbcType=VARCHAR},
            modifier                         = #{modifier,jdbcType=VARCHAR},
            modify_time                      = #{modifyTime,jdbcType=TIMESTAMP},
            version_value      = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.GlassInventoryShiftDetailPublishedPO">
        update mrp_glass_inventory_shift_detail_published
        <set>
            <if test="item.inventoryShiftDataId != null and item.inventoryShiftDataId != ''">
                inventory_shift_data_id = #{item.inventoryShiftDataId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointType != null and item.stockPointType != ''">
                stock_point_type = #{item.stockPointType,jdbcType=VARCHAR},
            </if>
            <if test="item.inventoryDate != null">
                inventory_date = #{item.inventoryDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.allFfInventory != null">
                all_ff_inventory = #{item.allFfInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.safetyStockLevelMin != null">
                safety_stock_level_min = #{item.safetyStockLevelMin,jdbcType=VARCHAR},
            </if>
            <if test="item.safetyStockLevelStandard != null">
                safety_stock_level_standard = #{item.safetyStockLevelStandard,jdbcType=VARCHAR},
            </if>
            <if test="item.safetyStockLevelMax != null">
                safety_stock_level_max = #{item.safetyStockLevelMax,jdbcType=VARCHAR},
            </if>
            <if test="item.openingInventory != null">
                opening_inventory = #{item.openingInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.bcOpeningInventory != null">
                bc_opening_inventory = #{item.bcOpeningInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.demandQuantity != null">
                demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.adjustQuantityFromFloat != null">
                adjust_quantity_from_float = #{item.adjustQuantityFromFloat,jdbcType=VARCHAR},
            </if>
            <if test="item.inputQuantity != null">
                input_quantity = #{item.inputQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.adjustQuantityFromPort != null">
                adjust_quantity_from_port = #{item.adjustQuantityFromPort,jdbcType=VARCHAR},
            </if>
            <if test="item.transitQuantityFromPort != null">
                transit_quantity_from_port = #{item.transitQuantityFromPort,jdbcType=VARCHAR},
            </if>
            <if test="item.transitQuantityFromFloat != null">
                transit_quantity_from_float = #{item.transitQuantityFromFloat,jdbcType=VARCHAR},
            </if>
            <if test="item.outputQuantityToBc != null">
                output_quantity_to_bc = #{item.outputQuantityToBc,jdbcType=VARCHAR},
            </if>
            <if test="item.outputQuantityToPort != null">
                output_quantity_to_port = #{item.outputQuantityToPort,jdbcType=VARCHAR},
            </if>
            <if test="item.decisionOutputQuantityToBc != null">
                decision_output_quantity_to_bc = #{item.decisionOutputQuantityToBc,jdbcType=VARCHAR},
            </if>
            <if test="item.decisionOutputQuantityToPort != null">
                decision_output_quantity_to_port = #{item.decisionOutputQuantityToPort,jdbcType=VARCHAR},
            </if>
            <if test="item.useReplaceQuantity != null">
                use_replace_quantity = #{item.useReplaceQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.usedAsReplaceQuantity != null">
                used_as_replace_quantity = #{item.usedAsReplaceQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.safetyStockGap != null">
                safety_stock_gap = #{item.safetyStockGap,jdbcType=VARCHAR},
            </if>
            <if test="item.endingInventory != null">
                ending_inventory = #{item.endingInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_glass_inventory_shift_detail_published
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="inventory_shift_data_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inventoryShiftDataId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="inventory_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inventoryDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="all_ff_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.allFfInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="safety_stock_level_min = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockLevelMin,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="safety_stock_level_standard = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockLevelStandard,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="safety_stock_level_max = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockLevelMax,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="opening_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.openingInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bc_opening_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.bcOpeningInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="adjust_quantity_from_float = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.adjustQuantityFromFloat,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="input_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inputQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="adjust_quantity_from_port = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.adjustQuantityFromPort,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transit_quantity_from_port = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transitQuantityFromPort,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transit_quantity_from_float = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transitQuantityFromFloat,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="output_quantity_to_bc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.outputQuantityToBc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="output_quantity_to_port = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.outputQuantityToPort,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="decision_output_quantity_to_bc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.decisionOutputQuantityToBc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="decision_output_quantity_to_port = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.decisionOutputQuantityToPort,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="use_replace_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.useReplaceQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="used_as_replace_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.usedAsReplaceQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="safety_stock_gap = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockGap,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ending_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.endingInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_glass_inventory_shift_detail_published
            <set>
                <if test="item.inventoryShiftDataId != null and item.inventoryShiftDataId != ''">
                    inventory_shift_data_id = #{item.inventoryShiftDataId,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointCode != null and item.stockPointCode != ''">
                    stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointType != null and item.stockPointType != ''">
                    stock_point_type = #{item.stockPointType,jdbcType=VARCHAR},
                </if>
                <if test="item.inventoryDate != null">
                    inventory_date = #{item.inventoryDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.allFfInventory != null">
                    all_ff_inventory = #{item.allFfInventory,jdbcType=VARCHAR},
                </if>
                <if test="item.safetyStockLevelMin != null">
                    safety_stock_level_min = #{item.safetyStockLevelMin,jdbcType=VARCHAR},
                </if>
                <if test="item.safetyStockLevelStandard != null">
                    safety_stock_level_standard = #{item.safetyStockLevelStandard,jdbcType=VARCHAR},
                </if>
                <if test="item.safetyStockLevelMax != null">
                    safety_stock_level_max = #{item.safetyStockLevelMax,jdbcType=VARCHAR},
                </if>
                <if test="item.openingInventory != null">
                    opening_inventory = #{item.openingInventory,jdbcType=VARCHAR},
                </if>
                <if test="item.bcOpeningInventory != null">
                    bc_opening_inventory = #{item.bcOpeningInventory,jdbcType=VARCHAR},
                </if>
                <if test="item.demandQuantity != null">
                    demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.adjustQuantityFromFloat != null">
                    adjust_quantity_from_float = #{item.adjustQuantityFromFloat,jdbcType=VARCHAR},
                </if>
                <if test="item.inputQuantity != null">
                    input_quantity = #{item.inputQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.adjustQuantityFromPort != null">
                    adjust_quantity_from_port = #{item.adjustQuantityFromPort,jdbcType=VARCHAR},
                </if>
                <if test="item.transitQuantityFromPort != null">
                    transit_quantity_from_port = #{item.transitQuantityFromPort,jdbcType=VARCHAR},
                </if>
                <if test="item.transitQuantityFromFloat != null">
                    transit_quantity_from_float = #{item.transitQuantityFromFloat,jdbcType=VARCHAR},
                </if>
                <if test="item.outputQuantityToBc != null">
                    output_quantity_to_bc = #{item.outputQuantityToBc,jdbcType=VARCHAR},
                </if>
                <if test="item.outputQuantityToPort != null">
                    output_quantity_to_port = #{item.outputQuantityToPort,jdbcType=VARCHAR},
                </if>
                <if test="item.decisionOutputQuantityToBc != null">
                    decision_output_quantity_to_bc = #{item.decisionOutputQuantityToBc,jdbcType=VARCHAR},
                </if>
                <if test="item.decisionOutputQuantityToPort != null">
                    decision_output_quantity_to_port = #{item.decisionOutputQuantityToPort,jdbcType=VARCHAR},
                </if>
                <if test="item.useReplaceQuantity != null">
                    use_replace_quantity = #{item.useReplaceQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.usedAsReplaceQuantity != null">
                    used_as_replace_quantity = #{item.usedAsReplaceQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.safetyStockGap != null">
                    safety_stock_gap = #{item.safetyStockGap,jdbcType=VARCHAR},
                </if>
                <if test="item.endingInventory != null">
                    ending_inventory = #{item.endingInventory,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_glass_inventory_shift_detail_published
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_glass_inventory_shift_detail_published where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
