<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.paramSetting.infrastructure.dao.MrpParParamSettingDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.paramSetting.infrastructure.po.MrpParParamSettingPO">
        <!--@Table mrp_par_param_setting-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="version_id" jdbcType="VARCHAR" property="versionId"/>
        <result column="lock_period" jdbcType="INTEGER" property="lockPeriod"/>
        <result column="thi_lack_alarm" jdbcType="VARCHAR" property="thiLackAlarm"/>
        <result column="thi_lack_push" jdbcType="VARCHAR" property="thiLackPush"/>
        <result column="sev_safe_upper_alarm" jdbcType="VARCHAR" property="sevSafeUpperAlarm"/>
        <result column="sev_safe_upper_push" jdbcType="VARCHAR" property="sevSafeUpperPush"/>
        <result column="sev_safe_lower_alarm" jdbcType="VARCHAR" property="sevSafeLowerAlarm"/>
        <result column="sev_safe_lower_push" jdbcType="VARCHAR" property="sevSafeLowerPush"/>
        <result column="fou_safe_upper_alarm" jdbcType="VARCHAR" property="fouSafeUpperAlarm"/>
        <result column="fou_safe_upper_push" jdbcType="VARCHAR" property="fouSafeUpperPush"/>
        <result column="fou_safe_lower_alarm" jdbcType="VARCHAR" property="fouSafeLowerAlarm"/>
        <result column="fou_safe_lower_push" jdbcType="VARCHAR" property="fouSafeLowerPush"/>
        <result column="thi_safe_upper_alarm" jdbcType="VARCHAR" property="thiSafeUpperAlarm"/>
        <result column="thi_safe_upper_push" jdbcType="VARCHAR" property="thiSafeUpperPush"/>
        <result column="thi_safe_lower_alarm" jdbcType="VARCHAR" property="thiSafeLowerAlarm"/>
        <result column="thi_safe_lower_push" jdbcType="VARCHAR" property="thiSafeLowerPush"/>
        <result column="half_safe_upper_alarm" jdbcType="VARCHAR" property="halfSafeUpperAlarm"/>
        <result column="half_safe_upper_push" jdbcType="VARCHAR" property="halfSafeUpperPush"/>
        <result column="half_safe_lower_alarm" jdbcType="VARCHAR" property="halfSafeLowerAlarm"/>
        <result column="half_safe_lower_push" jdbcType="VARCHAR" property="halfSafeLowerPush"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.paramSetting.vo.MrpParParamSettingVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,version_id,lock_period,thi_lack_alarm,thi_lack_push,sev_safe_upper_alarm,sev_safe_upper_push,sev_safe_lower_alarm,sev_safe_lower_push,fou_safe_upper_alarm,fou_safe_upper_push,fou_safe_lower_alarm,fou_safe_lower_push,thi_safe_upper_alarm,thi_safe_upper_push,thi_safe_lower_alarm,thi_safe_lower_push,half_safe_upper_alarm,half_safe_upper_push,half_safe_lower_alarm,half_safe_lower_push,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.versionId != null and params.versionId != ''">
                and version_id = #{params.versionId,jdbcType=VARCHAR}
            </if>
            <if test="params.lockPeriod != null">
                and lock_period = #{params.lockPeriod,jdbcType=INTEGER}
            </if>
            <if test="params.thiLackAlarm != null and params.thiLackAlarm != ''">
                and thi_lack_alarm = #{params.thiLackAlarm,jdbcType=VARCHAR}
            </if>
            <if test="params.thiLackPush != null and params.thiLackPush != ''">
                and thi_lack_push = #{params.thiLackPush,jdbcType=VARCHAR}
            </if>
            <if test="params.sevSafeUpperAlarm != null and params.sevSafeUpperAlarm != ''">
                and sev_safe_upper_alarm = #{params.sevSafeUpperAlarm,jdbcType=VARCHAR}
            </if>
            <if test="params.sevSafeUpperPush != null and params.sevSafeUpperPush != ''">
                and sev_safe_upper_push = #{params.sevSafeUpperPush,jdbcType=VARCHAR}
            </if>
            <if test="params.sevSafeLowerAlarm != null and params.sevSafeLowerAlarm != ''">
                and sev_safe_lower_alarm = #{params.sevSafeLowerAlarm,jdbcType=VARCHAR}
            </if>
            <if test="params.sevSafeLowerPush != null and params.sevSafeLowerPush != ''">
                and sev_safe_lower_push = #{params.sevSafeLowerPush,jdbcType=VARCHAR}
            </if>
            <if test="params.fouSafeUpperAlarm != null and params.fouSafeUpperAlarm != ''">
                and fou_safe_upper_alarm = #{params.fouSafeUpperAlarm,jdbcType=VARCHAR}
            </if>
            <if test="params.fouSafeUpperPush != null and params.fouSafeUpperPush != ''">
                and fou_safe_upper_push = #{params.fouSafeUpperPush,jdbcType=VARCHAR}
            </if>
            <if test="params.fouSafeLowerAlarm != null and params.fouSafeLowerAlarm != ''">
                and fou_safe_lower_alarm = #{params.fouSafeLowerAlarm,jdbcType=VARCHAR}
            </if>
            <if test="params.fouSafeLowerPush != null and params.fouSafeLowerPush != ''">
                and fou_safe_lower_push = #{params.fouSafeLowerPush,jdbcType=VARCHAR}
            </if>
            <if test="params.thiSafeUpperAlarm != null and params.thiSafeUpperAlarm != ''">
                and thi_safe_upper_alarm = #{params.thiSafeUpperAlarm,jdbcType=VARCHAR}
            </if>
            <if test="params.thiSafeUpperPush != null and params.thiSafeUpperPush != ''">
                and thi_safe_upper_push = #{params.thiSafeUpperPush,jdbcType=VARCHAR}
            </if>
            <if test="params.thiSafeLowerAlarm != null and params.thiSafeLowerAlarm != ''">
                and thi_safe_lower_alarm = #{params.thiSafeLowerAlarm,jdbcType=VARCHAR}
            </if>
            <if test="params.thiSafeLowerPush != null and params.thiSafeLowerPush != ''">
                and thi_safe_lower_push = #{params.thiSafeLowerPush,jdbcType=VARCHAR}
            </if>
            <if test="params.halfSafeUpperAlarm != null and params.halfSafeUpperAlarm != ''">
                and half_safe_upper_alarm = #{params.halfSafeUpperAlarm,jdbcType=VARCHAR}
            </if>
            <if test="params.halfSafeUpperPush != null and params.halfSafeUpperPush != ''">
                and half_safe_upper_push = #{params.halfSafeUpperPush,jdbcType=VARCHAR}
            </if>
            <if test="params.halfSafeLowerAlarm != null and params.halfSafeLowerAlarm != ''">
                and half_safe_lower_alarm = #{params.halfSafeLowerAlarm,jdbcType=VARCHAR}
            </if>
            <if test="params.halfSafeLowerPush != null and params.halfSafeLowerPush != ''">
                and half_safe_lower_push = #{params.halfSafeLowerPush,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_par_param_setting
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_par_param_setting
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from mrp_par_param_setting
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_par_param_setting
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.paramSetting.infrastructure.po.MrpParParamSettingPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_par_param_setting(
        id,
        version_id,
        lock_period,
        thi_lack_alarm,
        thi_lack_push,
        sev_safe_upper_alarm,
        sev_safe_upper_push,
        sev_safe_lower_alarm,
        sev_safe_lower_push,
        fou_safe_upper_alarm,
        fou_safe_upper_push,
        fou_safe_lower_alarm,
        fou_safe_lower_push,
        thi_safe_upper_alarm,
        thi_safe_upper_push,
        thi_safe_lower_alarm,
        thi_safe_lower_push,
        half_safe_upper_alarm,
        half_safe_upper_push,
        half_safe_lower_alarm,
        half_safe_lower_push,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{versionId,jdbcType=VARCHAR},
        #{lockPeriod,jdbcType=INTEGER},
        #{thiLackAlarm,jdbcType=VARCHAR},
        #{thiLackPush,jdbcType=VARCHAR},
        #{sevSafeUpperAlarm,jdbcType=VARCHAR},
        #{sevSafeUpperPush,jdbcType=VARCHAR},
        #{sevSafeLowerAlarm,jdbcType=VARCHAR},
        #{sevSafeLowerPush,jdbcType=VARCHAR},
        #{fouSafeUpperAlarm,jdbcType=VARCHAR},
        #{fouSafeUpperPush,jdbcType=VARCHAR},
        #{fouSafeLowerAlarm,jdbcType=VARCHAR},
        #{fouSafeLowerPush,jdbcType=VARCHAR},
        #{thiSafeUpperAlarm,jdbcType=VARCHAR},
        #{thiSafeUpperPush,jdbcType=VARCHAR},
        #{thiSafeLowerAlarm,jdbcType=VARCHAR},
        #{thiSafeLowerPush,jdbcType=VARCHAR},
        #{halfSafeUpperAlarm,jdbcType=VARCHAR},
        #{halfSafeUpperPush,jdbcType=VARCHAR},
        #{halfSafeLowerAlarm,jdbcType=VARCHAR},
        #{halfSafeLowerPush,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mrp.paramSetting.infrastructure.po.MrpParParamSettingPO">
        insert into mrp_par_param_setting(
        id,
        version_id,
        lock_period,
        thi_lack_alarm,
        thi_lack_push,
        sev_safe_upper_alarm,
        sev_safe_upper_push,
        sev_safe_lower_alarm,
        sev_safe_lower_push,
        fou_safe_upper_alarm,
        fou_safe_upper_push,
        fou_safe_lower_alarm,
        fou_safe_lower_push,
        thi_safe_upper_alarm,
        thi_safe_upper_push,
        thi_safe_lower_alarm,
        thi_safe_lower_push,
        half_safe_upper_alarm,
        half_safe_upper_push,
        half_safe_lower_alarm,
        half_safe_lower_push,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{versionId,jdbcType=VARCHAR},
        #{lockPeriod,jdbcType=INTEGER},
        #{thiLackAlarm,jdbcType=VARCHAR},
        #{thiLackPush,jdbcType=VARCHAR},
        #{sevSafeUpperAlarm,jdbcType=VARCHAR},
        #{sevSafeUpperPush,jdbcType=VARCHAR},
        #{sevSafeLowerAlarm,jdbcType=VARCHAR},
        #{sevSafeLowerPush,jdbcType=VARCHAR},
        #{fouSafeUpperAlarm,jdbcType=VARCHAR},
        #{fouSafeUpperPush,jdbcType=VARCHAR},
        #{fouSafeLowerAlarm,jdbcType=VARCHAR},
        #{fouSafeLowerPush,jdbcType=VARCHAR},
        #{thiSafeUpperAlarm,jdbcType=VARCHAR},
        #{thiSafeUpperPush,jdbcType=VARCHAR},
        #{thiSafeLowerAlarm,jdbcType=VARCHAR},
        #{thiSafeLowerPush,jdbcType=VARCHAR},
        #{halfSafeUpperAlarm,jdbcType=VARCHAR},
        #{halfSafeUpperPush,jdbcType=VARCHAR},
        #{halfSafeLowerAlarm,jdbcType=VARCHAR},
        #{halfSafeLowerPush,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_par_param_setting(
        id,
        version_id,
        lock_period,
        thi_lack_alarm,
        thi_lack_push,
        sev_safe_upper_alarm,
        sev_safe_upper_push,
        sev_safe_lower_alarm,
        sev_safe_lower_push,
        fou_safe_upper_alarm,
        fou_safe_upper_push,
        fou_safe_lower_alarm,
        fou_safe_lower_push,
        thi_safe_upper_alarm,
        thi_safe_upper_push,
        thi_safe_lower_alarm,
        thi_safe_lower_push,
        half_safe_upper_alarm,
        half_safe_upper_push,
        half_safe_lower_alarm,
        half_safe_lower_push,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.versionId,jdbcType=VARCHAR},
        #{entity.lockPeriod,jdbcType=INTEGER},
        #{entity.thiLackAlarm,jdbcType=VARCHAR},
        #{entity.thiLackPush,jdbcType=VARCHAR},
        #{entity.sevSafeUpperAlarm,jdbcType=VARCHAR},
        #{entity.sevSafeUpperPush,jdbcType=VARCHAR},
        #{entity.sevSafeLowerAlarm,jdbcType=VARCHAR},
        #{entity.sevSafeLowerPush,jdbcType=VARCHAR},
        #{entity.fouSafeUpperAlarm,jdbcType=VARCHAR},
        #{entity.fouSafeUpperPush,jdbcType=VARCHAR},
        #{entity.fouSafeLowerAlarm,jdbcType=VARCHAR},
        #{entity.fouSafeLowerPush,jdbcType=VARCHAR},
        #{entity.thiSafeUpperAlarm,jdbcType=VARCHAR},
        #{entity.thiSafeUpperPush,jdbcType=VARCHAR},
        #{entity.thiSafeLowerAlarm,jdbcType=VARCHAR},
        #{entity.thiSafeLowerPush,jdbcType=VARCHAR},
        #{entity.halfSafeUpperAlarm,jdbcType=VARCHAR},
        #{entity.halfSafeUpperPush,jdbcType=VARCHAR},
        #{entity.halfSafeLowerAlarm,jdbcType=VARCHAR},
        #{entity.halfSafeLowerPush,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_par_param_setting(
        id,
        version_id,
        lock_period,
        thi_lack_alarm,
        thi_lack_push,
        sev_safe_upper_alarm,
        sev_safe_upper_push,
        sev_safe_lower_alarm,
        sev_safe_lower_push,
        fou_safe_upper_alarm,
        fou_safe_upper_push,
        fou_safe_lower_alarm,
        fou_safe_lower_push,
        thi_safe_upper_alarm,
        thi_safe_upper_push,
        thi_safe_lower_alarm,
        thi_safe_lower_push,
        half_safe_upper_alarm,
        half_safe_upper_push,
        half_safe_lower_alarm,
        half_safe_lower_push,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.versionId,jdbcType=VARCHAR},
        #{entity.lockPeriod,jdbcType=INTEGER},
        #{entity.thiLackAlarm,jdbcType=VARCHAR},
        #{entity.thiLackPush,jdbcType=VARCHAR},
        #{entity.sevSafeUpperAlarm,jdbcType=VARCHAR},
        #{entity.sevSafeUpperPush,jdbcType=VARCHAR},
        #{entity.sevSafeLowerAlarm,jdbcType=VARCHAR},
        #{entity.sevSafeLowerPush,jdbcType=VARCHAR},
        #{entity.fouSafeUpperAlarm,jdbcType=VARCHAR},
        #{entity.fouSafeUpperPush,jdbcType=VARCHAR},
        #{entity.fouSafeLowerAlarm,jdbcType=VARCHAR},
        #{entity.fouSafeLowerPush,jdbcType=VARCHAR},
        #{entity.thiSafeUpperAlarm,jdbcType=VARCHAR},
        #{entity.thiSafeUpperPush,jdbcType=VARCHAR},
        #{entity.thiSafeLowerAlarm,jdbcType=VARCHAR},
        #{entity.thiSafeLowerPush,jdbcType=VARCHAR},
        #{entity.halfSafeUpperAlarm,jdbcType=VARCHAR},
        #{entity.halfSafeUpperPush,jdbcType=VARCHAR},
        #{entity.halfSafeLowerAlarm,jdbcType=VARCHAR},
        #{entity.halfSafeLowerPush,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.paramSetting.infrastructure.po.MrpParParamSettingPO">
        update mrp_par_param_setting set
        version_id = #{versionId,jdbcType=VARCHAR},
        lock_period = #{lockPeriod,jdbcType=INTEGER},
        thi_lack_alarm = #{thiLackAlarm,jdbcType=VARCHAR},
        thi_lack_push = #{thiLackPush,jdbcType=VARCHAR},
        sev_safe_upper_alarm = #{sevSafeUpperAlarm,jdbcType=VARCHAR},
        sev_safe_upper_push = #{sevSafeUpperPush,jdbcType=VARCHAR},
        sev_safe_lower_alarm = #{sevSafeLowerAlarm,jdbcType=VARCHAR},
        sev_safe_lower_push = #{sevSafeLowerPush,jdbcType=VARCHAR},
        fou_safe_upper_alarm = #{fouSafeUpperAlarm,jdbcType=VARCHAR},
        fou_safe_upper_push = #{fouSafeUpperPush,jdbcType=VARCHAR},
        fou_safe_lower_alarm = #{fouSafeLowerAlarm,jdbcType=VARCHAR},
        fou_safe_lower_push = #{fouSafeLowerPush,jdbcType=VARCHAR},
        thi_safe_upper_alarm = #{thiSafeUpperAlarm,jdbcType=VARCHAR},
        thi_safe_upper_push = #{thiSafeUpperPush,jdbcType=VARCHAR},
        thi_safe_lower_alarm = #{thiSafeLowerAlarm,jdbcType=VARCHAR},
        thi_safe_lower_push = #{thiSafeLowerPush,jdbcType=VARCHAR},
        half_safe_upper_alarm = #{halfSafeUpperAlarm,jdbcType=VARCHAR},
        half_safe_upper_push = #{halfSafeUpperPush,jdbcType=VARCHAR},
        half_safe_lower_alarm = #{halfSafeLowerAlarm,jdbcType=VARCHAR},
        half_safe_lower_push = #{halfSafeLowerPush,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value      = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mrp.paramSetting.infrastructure.po.MrpParParamSettingPO">
        update mrp_par_param_setting
        <set>
            <if test="item.versionId != null and item.versionId != ''">
                version_id = #{item.versionId,jdbcType=VARCHAR},
            </if>
            <if test="item.lockPeriod != null">
                lock_period = #{item.lockPeriod,jdbcType=INTEGER},
            </if>
            <if test="item.thiLackAlarm != null and item.thiLackAlarm != ''">
                thi_lack_alarm = #{item.thiLackAlarm,jdbcType=VARCHAR},
            </if>
            <if test="item.thiLackPush != null and item.thiLackPush != ''">
                thi_lack_push = #{item.thiLackPush,jdbcType=VARCHAR},
            </if>
            <if test="item.sevSafeUpperAlarm != null and item.sevSafeUpperAlarm != ''">
                sev_safe_upper_alarm = #{item.sevSafeUpperAlarm,jdbcType=VARCHAR},
            </if>
            <if test="item.sevSafeUpperPush != null and item.sevSafeUpperPush != ''">
                sev_safe_upper_push = #{item.sevSafeUpperPush,jdbcType=VARCHAR},
            </if>
            <if test="item.sevSafeLowerAlarm != null and item.sevSafeLowerAlarm != ''">
                sev_safe_lower_alarm = #{item.sevSafeLowerAlarm,jdbcType=VARCHAR},
            </if>
            <if test="item.sevSafeLowerPush != null and item.sevSafeLowerPush != ''">
                sev_safe_lower_push = #{item.sevSafeLowerPush,jdbcType=VARCHAR},
            </if>
            <if test="item.fouSafeUpperAlarm != null and item.fouSafeUpperAlarm != ''">
                fou_safe_upper_alarm = #{item.fouSafeUpperAlarm,jdbcType=VARCHAR},
            </if>
            <if test="item.fouSafeUpperPush != null and item.fouSafeUpperPush != ''">
                fou_safe_upper_push = #{item.fouSafeUpperPush,jdbcType=VARCHAR},
            </if>
            <if test="item.fouSafeLowerAlarm != null and item.fouSafeLowerAlarm != ''">
                fou_safe_lower_alarm = #{item.fouSafeLowerAlarm,jdbcType=VARCHAR},
            </if>
            <if test="item.fouSafeLowerPush != null and item.fouSafeLowerPush != ''">
                fou_safe_lower_push = #{item.fouSafeLowerPush,jdbcType=VARCHAR},
            </if>
            <if test="item.thiSafeUpperAlarm != null and item.thiSafeUpperAlarm != ''">
                thi_safe_upper_alarm = #{item.thiSafeUpperAlarm,jdbcType=VARCHAR},
            </if>
            <if test="item.thiSafeUpperPush != null and item.thiSafeUpperPush != ''">
                thi_safe_upper_push = #{item.thiSafeUpperPush,jdbcType=VARCHAR},
            </if>
            <if test="item.thiSafeLowerAlarm != null and item.thiSafeLowerAlarm != ''">
                thi_safe_lower_alarm = #{item.thiSafeLowerAlarm,jdbcType=VARCHAR},
            </if>
            <if test="item.thiSafeLowerPush != null and item.thiSafeLowerPush != ''">
                thi_safe_lower_push = #{item.thiSafeLowerPush,jdbcType=VARCHAR},
            </if>
            <if test="item.halfSafeUpperAlarm != null and item.halfSafeUpperAlarm != ''">
                half_safe_upper_alarm = #{item.halfSafeUpperAlarm,jdbcType=VARCHAR},
            </if>
            <if test="item.halfSafeUpperPush != null and item.halfSafeUpperPush != ''">
                half_safe_upper_push = #{item.halfSafeUpperPush,jdbcType=VARCHAR},
            </if>
            <if test="item.halfSafeLowerAlarm != null and item.halfSafeLowerAlarm != ''">
                half_safe_lower_alarm = #{item.halfSafeLowerAlarm,jdbcType=VARCHAR},
            </if>
            <if test="item.halfSafeLowerPush != null and item.halfSafeLowerPush != ''">
                half_safe_lower_push = #{item.halfSafeLowerPush,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_par_param_setting
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="lock_period = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lockPeriod,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="thi_lack_alarm = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.thiLackAlarm,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="thi_lack_push = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.thiLackPush,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sev_safe_upper_alarm = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sevSafeUpperAlarm,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sev_safe_upper_push = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sevSafeUpperPush,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sev_safe_lower_alarm = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sevSafeLowerAlarm,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sev_safe_lower_push = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sevSafeLowerPush,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="fou_safe_upper_alarm = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.fouSafeUpperAlarm,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="fou_safe_upper_push = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.fouSafeUpperPush,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="fou_safe_lower_alarm = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.fouSafeLowerAlarm,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="fou_safe_lower_push = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.fouSafeLowerPush,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="thi_safe_upper_alarm = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.thiSafeUpperAlarm,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="thi_safe_upper_push = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.thiSafeUpperPush,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="thi_safe_lower_alarm = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.thiSafeLowerAlarm,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="thi_safe_lower_push = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.thiSafeLowerPush,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="half_safe_upper_alarm = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.halfSafeUpperAlarm,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="half_safe_upper_push = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.halfSafeUpperPush,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="half_safe_lower_alarm = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.halfSafeLowerAlarm,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="half_safe_lower_push = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.halfSafeLowerPush,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mrp_par_param_setting 
        <set>
            <if test="item.versionId != null and item.versionId != ''">
                version_id = #{item.versionId,jdbcType=VARCHAR},
            </if>
            <if test="item.lockPeriod != null">
                lock_period = #{item.lockPeriod,jdbcType=INTEGER},
            </if>
            <if test="item.thiLackAlarm != null and item.thiLackAlarm != ''">
                thi_lack_alarm = #{item.thiLackAlarm,jdbcType=VARCHAR},
            </if>
            <if test="item.thiLackPush != null and item.thiLackPush != ''">
                thi_lack_push = #{item.thiLackPush,jdbcType=VARCHAR},
            </if>
            <if test="item.sevSafeUpperAlarm != null and item.sevSafeUpperAlarm != ''">
                sev_safe_upper_alarm = #{item.sevSafeUpperAlarm,jdbcType=VARCHAR},
            </if>
            <if test="item.sevSafeUpperPush != null and item.sevSafeUpperPush != ''">
                sev_safe_upper_push = #{item.sevSafeUpperPush,jdbcType=VARCHAR},
            </if>
            <if test="item.sevSafeLowerAlarm != null and item.sevSafeLowerAlarm != ''">
                sev_safe_lower_alarm = #{item.sevSafeLowerAlarm,jdbcType=VARCHAR},
            </if>
            <if test="item.sevSafeLowerPush != null and item.sevSafeLowerPush != ''">
                sev_safe_lower_push = #{item.sevSafeLowerPush,jdbcType=VARCHAR},
            </if>
            <if test="item.fouSafeUpperAlarm != null and item.fouSafeUpperAlarm != ''">
                fou_safe_upper_alarm = #{item.fouSafeUpperAlarm,jdbcType=VARCHAR},
            </if>
            <if test="item.fouSafeUpperPush != null and item.fouSafeUpperPush != ''">
                fou_safe_upper_push = #{item.fouSafeUpperPush,jdbcType=VARCHAR},
            </if>
            <if test="item.fouSafeLowerAlarm != null and item.fouSafeLowerAlarm != ''">
                fou_safe_lower_alarm = #{item.fouSafeLowerAlarm,jdbcType=VARCHAR},
            </if>
            <if test="item.fouSafeLowerPush != null and item.fouSafeLowerPush != ''">
                fou_safe_lower_push = #{item.fouSafeLowerPush,jdbcType=VARCHAR},
            </if>
            <if test="item.thiSafeUpperAlarm != null and item.thiSafeUpperAlarm != ''">
                thi_safe_upper_alarm = #{item.thiSafeUpperAlarm,jdbcType=VARCHAR},
            </if>
            <if test="item.thiSafeUpperPush != null and item.thiSafeUpperPush != ''">
                thi_safe_upper_push = #{item.thiSafeUpperPush,jdbcType=VARCHAR},
            </if>
            <if test="item.thiSafeLowerAlarm != null and item.thiSafeLowerAlarm != ''">
                thi_safe_lower_alarm = #{item.thiSafeLowerAlarm,jdbcType=VARCHAR},
            </if>
            <if test="item.thiSafeLowerPush != null and item.thiSafeLowerPush != ''">
                thi_safe_lower_push = #{item.thiSafeLowerPush,jdbcType=VARCHAR},
            </if>
            <if test="item.halfSafeUpperAlarm != null and item.halfSafeUpperAlarm != ''">
                half_safe_upper_alarm = #{item.halfSafeUpperAlarm,jdbcType=VARCHAR},
            </if>
            <if test="item.halfSafeUpperPush != null and item.halfSafeUpperPush != ''">
                half_safe_upper_push = #{item.halfSafeUpperPush,jdbcType=VARCHAR},
            </if>
            <if test="item.halfSafeLowerAlarm != null and item.halfSafeLowerAlarm != ''">
                half_safe_lower_alarm = #{item.halfSafeLowerAlarm,jdbcType=VARCHAR},
            </if>
            <if test="item.halfSafeLowerPush != null and item.halfSafeLowerPush != ''">
                half_safe_lower_push = #{item.halfSafeLowerPush,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mrp_par_param_setting where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_par_param_setting where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
