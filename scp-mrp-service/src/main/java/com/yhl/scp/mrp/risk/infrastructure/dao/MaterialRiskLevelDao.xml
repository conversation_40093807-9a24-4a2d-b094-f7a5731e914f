<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.risk.infrastructure.dao.MaterialRiskLevelDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.risk.infrastructure.po.MaterialRiskLevelPO">
        <!--@Table mrp_material_risk_level-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="material_code" jdbcType="VARCHAR" property="materialCode"/>
        <result column="material_risk_level" jdbcType="VARCHAR" property="materialRiskLevel"/>
        <result column="safe_upper_alarm" jdbcType="VARCHAR" property="safeUpperAlarm"/>
        <result column="safe_lower_alarm" jdbcType="VARCHAR" property="safeLowerAlarm"/>
        <result column="material_risk_level_rule_id" jdbcType="VARCHAR" property="materialRiskLevelRuleId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.risk.vo.MaterialRiskLevelVO">
        <result column="material_risk_level_rule_name" jdbcType="VARCHAR" property="materialRiskLevelRuleName"/>
        <result column="material_name" jdbcType="VARCHAR" property="materialName"/>
        <result column="material_type" jdbcType="VARCHAR" property="materialType"/>
        <result column="material_property" jdbcType="VARCHAR" property="materialProperty"/>
        <result column="expire_date" jdbcType="INTEGER" property="expireDate"/>
        <result column="min_order_qty" jdbcType="VARCHAR" property="minOrderQty"/>
        <result column="purchase_lot" jdbcType="VARCHAR" property="purchaseLot"/>
        <result column="specific" jdbcType="VARCHAR" property="specific"/>
        <result column="order_placement_lead_time_day" jdbcType="VARCHAR" property="orderPlacementLeadTimeDay"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,stock_point_code,material_code,material_risk_level,safe_upper_alarm,safe_lower_alarm,material_risk_level_rule_id,remark,enabled,creator,create_time,modifier,modify_time,version_value

    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,material_risk_level_rule_name,material_name,material_type,material_property,expire_date,min_order_qty,purchase_lot,`specific`,order_placement_lead_time_day
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.ids != null and params.ids.size() > 0">
                and id in
                <foreach collection="params.ids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.materialCode != null and params.materialCode != ''">
                and material_code = #{params.materialCode,jdbcType=VARCHAR}
            </if>
            <if test="params.materialCodes != null and params.materialCodes.size() > 0">
                and material_code in
                <foreach collection="params.materialCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.materialRiskLevel != null and params.materialRiskLevel != ''">
                and material_risk_level = #{params.materialRiskLevel,jdbcType=VARCHAR}
            </if>
            <if test="params.safeUpperAlarm != null">
                and safe_upper_alarm = #{params.safeUpperAlarm,jdbcType=VARCHAR}
            </if>
            <if test="params.safeLowerAlarm != null">
                and safe_lower_alarm = #{params.safeLowerAlarm,jdbcType=VARCHAR}
            </if>
            <if test="params.materialRiskLevelRuleId != null">
                and material_risk_level_rule_id = #{params.materialRiskLevelRuleId,jdbcType=VARCHAR}
            </if>
            <if test="params.materialRiskLevelRuleName != null">
                and material_risk_level_rule_name = #{params.materialRiskLevelRuleName,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_risk_level
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_risk_level
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_risk_level
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_risk_level
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.risk.infrastructure.po.MaterialRiskLevelPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_material_risk_level(
        id,
        stock_point_code,
        material_code,
        material_risk_level,
        safe_upper_alarm,
        safe_lower_alarm,
        material_risk_level_rule_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{materialCode,jdbcType=VARCHAR},
        #{materialRiskLevel,jdbcType=VARCHAR},
        #{safeUpperAlarm,jdbcType=VARCHAR},
        #{safeLowerAlarm,jdbcType=VARCHAR},
        #{materialRiskLevelRuleId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mrp.risk.infrastructure.po.MaterialRiskLevelPO">
        insert into mrp_material_risk_level(id,
                                            stock_point_code,
                                            material_code,
                                            material_risk_level,
                                            safe_upper_alarm,
                                            safe_lower_alarm,
                                            material_risk_level_rule_id,
                                            remark,
                                            enabled,
                                            creator,
                                            create_time,
                                            modifier,
                                            modify_time,
                                            version_value)
        values (#{id,jdbcType=VARCHAR},
                #{stockPointCode,jdbcType=VARCHAR},
                #{materialCode,jdbcType=VARCHAR},
                #{materialRiskLevel,jdbcType=VARCHAR},
                #{safeUpperAlarm,jdbcType=VARCHAR},
                #{safeLowerAlarm,jdbcType=VARCHAR},
                #{materialRiskLevelRuleId,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_risk_level(
        id,
        stock_point_code,
        material_code,
        material_risk_level,
        safe_upper_alarm,
        safe_lower_alarm,
        material_risk_level_rule_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.materialCode,jdbcType=VARCHAR},
            #{entity.materialRiskLevel,jdbcType=VARCHAR},
            #{entity.safeUpperAlarm,jdbcType=VARCHAR},
            #{entity.safeLowerAlarm,jdbcType=VARCHAR},
            #{entity.materialRiskLevelRuleId,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_material_risk_level(
        id,
        stock_point_code,
        material_code,
        material_risk_level,
        safe_upper_alarm,
        safe_lower_alarm,
        material_risk_level_rule_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.materialCode,jdbcType=VARCHAR},
            #{entity.materialRiskLevel,jdbcType=VARCHAR},
            #{entity.safeUpperAlarm,jdbcType=VARCHAR},
            #{entity.safeLowerAlarm,jdbcType=VARCHAR},
            #{entity.materialRiskLevelRuleId,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.risk.infrastructure.po.MaterialRiskLevelPO">
        update mrp_material_risk_level
        set stock_point_code    = #{stockPointCode,jdbcType=VARCHAR},
            material_code       = #{materialCode,jdbcType=VARCHAR},
            material_risk_level = #{materialRiskLevel,jdbcType=VARCHAR},
            safe_upper_alarm    = #{safeUpperAlarm,jdbcType=VARCHAR},
            safe_lower_alarm    = #{safeLowerAlarm,jdbcType=VARCHAR},
            material_risk_level_rule_id    = #{materialRiskLevelRuleId,jdbcType=VARCHAR},
            remark              = #{remark,jdbcType=VARCHAR},
            enabled             = #{enabled,jdbcType=VARCHAR},
            modifier            = #{modifier,jdbcType=VARCHAR},
            modify_time         = #{modifyTime,jdbcType=TIMESTAMP},
            version_value      = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mrp.risk.infrastructure.po.MaterialRiskLevelPO">
        update mrp_material_risk_level
        <set>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.materialCode != null and item.materialCode != ''">
                material_code = #{item.materialCode,jdbcType=VARCHAR},
            </if>
            <if test="item.materialRiskLevel != null and item.materialRiskLevel != ''">
                material_risk_level = #{item.materialRiskLevel,jdbcType=VARCHAR},
            </if>
            <if test="item.safeUpperAlarm != null">
                safe_upper_alarm = #{item.safeUpperAlarm,jdbcType=VARCHAR},
            </if>
            <if test="item.safeLowerAlarm != null">
                safe_lower_alarm = #{item.safeLowerAlarm,jdbcType=VARCHAR},
            </if>
            <if test="item.materialRiskLevelRuleId != null">
                material_risk_level_rule_id = #{item.materialRiskLevelRuleId,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_risk_level
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_risk_level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialRiskLevel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="safe_upper_alarm = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safeUpperAlarm,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="safe_lower_alarm = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safeLowerAlarm,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_risk_level_rule_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialRiskLevelRuleId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_material_risk_level
            <set>
                <if test="item.stockPointCode != null and item.stockPointCode != ''">
                    stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
                </if>
                <if test="item.materialCode != null and item.materialCode != ''">
                    material_code = #{item.materialCode,jdbcType=VARCHAR},
                </if>
                <if test="item.materialRiskLevel != null and item.materialRiskLevel != ''">
                    material_risk_level = #{item.materialRiskLevel,jdbcType=VARCHAR},
                </if>
                <if test="item.safeUpperAlarm != null">
                    safe_upper_alarm = #{item.safeUpperAlarm,jdbcType=VARCHAR},
                </if>
                <if test="item.safeLowerAlarm != null">
                    safe_lower_alarm = #{item.safeLowerAlarm,jdbcType=VARCHAR},
                </if>
                <if test="item.materialRiskLevelRuleId != null">
                    material_risk_level_rule_id = #{item.materialRiskLevelRuleId,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_material_risk_level
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_risk_level where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteAll">
        TRUNCATE TABLE mrp_material_risk_level
    </delete>

    <select id="selectOriginalFilmList" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_risk_level
        where
    </select>

    <select id="selectByMaterialTypeList" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_risk_level
        where material_type = #{materialType,jdbcType=VARCHAR}
    </select>

    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_risk_level
        <include refid="Base_Where_Condition"/>
    </select>
</mapper>
