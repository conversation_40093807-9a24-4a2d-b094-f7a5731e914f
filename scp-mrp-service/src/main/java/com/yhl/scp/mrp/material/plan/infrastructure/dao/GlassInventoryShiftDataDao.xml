<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.material.plan.infrastructure.dao.GlassInventoryShiftDataDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.material.plan.infrastructure.po.GlassInventoryShiftDataPO">
        <!--@Table mrp_glass_inventory_shift_data-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="material_plan_version_id" jdbcType="VARCHAR" property="materialPlanVersionId"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_factory_code" jdbcType="VARCHAR" property="productFactoryCode"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="safety_stock_days_min" jdbcType="VARCHAR" property="safetyStockDaysMin"/>
        <result column="safety_stock_days_standard" jdbcType="VARCHAR" property="safetyStockDaysStandard"/>
        <result column="safety_stock_days_max" jdbcType="VARCHAR" property="safetyStockDaysMax"/>
        <result column="port_inventory_days" jdbcType="VARCHAR" property="portInventoryDays"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.material.plan.vo.GlassInventoryShiftDataVO">
        <result column="product_thickness" jdbcType="VARCHAR" property="productThickness"/>
        <result column="product_color" jdbcType="VARCHAR" property="productColor"/>
        <result column="product_length" jdbcType="VARCHAR" property="productLength"/>
        <result column="product_width" jdbcType="VARCHAR" property="productWidth"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,material_plan_version_id,product_code,product_factory_code,vehicle_model_code,safety_stock_days_min,safety_stock_days_standard,safety_stock_days_max,port_inventory_days,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,product_thickness,product_color,product_length,product_width
    </sql>

    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.materialPlanVersionId != null and params.materialPlanVersionId != ''">
                and material_plan_version_id = #{params.materialPlanVersionId,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                and product_code in
                <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productFactoryCode != null and params.productFactoryCode != ''">
                and product_factory_code = #{params.productFactoryCode,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.safetyStockDaysMin != null">
                and safety_stock_days_min = #{params.safetyStockDaysMin,jdbcType=VARCHAR}
            </if>
            <if test="params.safetyStockDaysStandard != null">
                and safety_stock_days_standard = #{params.safetyStockDaysStandard,jdbcType=VARCHAR}
            </if>
            <if test="params.safetyStockDaysMax != null">
                and safety_stock_days_max = #{params.safetyStockDaysMax,jdbcType=VARCHAR}
            </if>
            <if test="params.portInventoryDays != null">
                and port_inventory_days = #{params.portInventoryDays,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_glass_inventory_shift_data
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_glass_inventory_shift_data
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mrp_glass_inventory_shift_data
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_glass_inventory_shift_data
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        SELECT DISTINCT
        inventory_shift_data_id id,
        product_code,
        product_thickness,
        product_color,
        product_factory_code,
        vehicle_model_code
        FROM
        v_mrp_glass_inventory_shift_detail
        where 1=1
        <if test="params.productCodes != null and params.productCodes.size() > 0">
            and product_code in
            <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="params.productColor != null and params.productColor != ''">
            and product_color = #{params.productColor,jdbcType=VARCHAR}
        </if>
        <if test="params.productThickness != null and params.productThickness != ''">
            and product_thickness = #{params.productThickness,jdbcType=VARCHAR}
        </if>
        <if test="params.productCode != null and params.productCode != ''">
            and product_code = #{params.productCode,jdbcType=VARCHAR}
        </if>
        <if test="params.stockPointType != null and params.stockPointType != ''">
            and stock_point_type = #{params.stockPointType,jdbcType=VARCHAR}
        </if>
        <if test="params.warningRemindStart != null and params.warningRemindStart != ''">
            and warning_remind_start = #{params.warningRemindStart,jdbcType=VARCHAR}
        </if>
        <if test="params.warningRemindEnd != null and params.warningRemindEnd != ''">
            and warning_remind_end = #{params.warningRemindEnd,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectForDemandCalculation" resultMap="VOResultMap">
        select
        id,
        product_code,
        product_length,
        product_width,
        product_thickness,
        product_color,
        product_factory_code,
        vehicle_model_code
        from v_mrp_glass_data_consult_summary
        where 1 = 1
        <if test="params.outputQuantityToBc != null">
            and product_color = #{params.outputQuantityToBc,jdbcType=VARCHAR}
        </if>
        <if test="params.startProductThickness != null">
            and product_thickness <![CDATA[ >= ]]> #{params.startProductThickness,jdbcType=VARCHAR}
        </if>
        <if test="params.endProductThickness != null">
            and product_thickness <![CDATA[ <= ]]> #{params.endProductThickness,jdbcType=VARCHAR}
        </if>
        <if test="params.productColor != null and params.productColor != ''">
            and product_color = #{params.productColor,jdbcType=VARCHAR}
        </if>
        <if test="params.planUserProductCodeList != null and params.planUserProductCodeList.size() > 0">
            and product_code in
            <foreach collection="params.planUserProductCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="params.productColorList != null and params.productColorList.size() > 0">
            and product_color in
            <foreach collection="params.productColorList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <select id="getLastCreateTime" resultType="java.util.Date">
        select
        create_time
        from mrp_glass_inventory_shift_data
        where product_code in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        order by create_time desc
        limit 1
    </select>

    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.GlassInventoryShiftDataPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_glass_inventory_shift_data(
        id,
        material_plan_version_id,
        product_code,
        product_factory_code,
        vehicle_model_code,
        safety_stock_days_min,
        safety_stock_days_standard,
        safety_stock_days_max,
        port_inventory_days,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{materialPlanVersionId,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productFactoryCode,jdbcType=VARCHAR},
        #{vehicleModelCode,jdbcType=VARCHAR},
        #{safetyStockDaysMin,jdbcType=VARCHAR},
        #{safetyStockDaysStandard,jdbcType=VARCHAR},
        #{safetyStockDaysMax,jdbcType=VARCHAR},
        #{portInventoryDays,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.GlassInventoryShiftDataPO">
        insert into mrp_glass_inventory_shift_data(id,
                                                   material_plan_version_id,
                                                   product_code,
                                                   product_factory_code,
                                                   vehicle_model_code,
                                                   safety_stock_days_min,
                                                   safety_stock_days_standard,
                                                   safety_stock_days_max,
                                                   port_inventory_days,
                                                   remark,
                                                   enabled,
                                                   creator,
                                                   create_time,
                                                   modifier,
                                                   modify_time,
                                                   version_value)
        values (#{id,jdbcType=VARCHAR},
                #{materialPlanVersionId,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{productFactoryCode,jdbcType=VARCHAR},
                #{vehicleModelCode,jdbcType=VARCHAR},
                #{safetyStockDaysMin,jdbcType=VARCHAR},
                #{safetyStockDaysStandard,jdbcType=VARCHAR},
                #{safetyStockDaysMax,jdbcType=VARCHAR},
                #{portInventoryDays,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_glass_inventory_shift_data(
        id,
        material_plan_version_id,
        product_code,
        product_factory_code,
        vehicle_model_code,
        safety_stock_days_min,
        safety_stock_days_standard,
        safety_stock_days_max,
        port_inventory_days,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.materialPlanVersionId,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productFactoryCode,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.safetyStockDaysMin,jdbcType=VARCHAR},
            #{entity.safetyStockDaysStandard,jdbcType=VARCHAR},
            #{entity.safetyStockDaysMax,jdbcType=VARCHAR},
            #{entity.portInventoryDays,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_glass_inventory_shift_data(
        id,
        material_plan_version_id,
        product_code,
        product_factory_code,
        vehicle_model_code,
        safety_stock_days_min,
        safety_stock_days_standard,
        safety_stock_days_max,
        port_inventory_days,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.materialPlanVersionId,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productFactoryCode,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.safetyStockDaysMin,jdbcType=VARCHAR},
            #{entity.safetyStockDaysStandard,jdbcType=VARCHAR},
            #{entity.safetyStockDaysMax,jdbcType=VARCHAR},
            #{entity.portInventoryDays,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.GlassInventoryShiftDataPO">
        update mrp_glass_inventory_shift_data
        set material_plan_version_id   = #{materialPlanVersionId,jdbcType=VARCHAR},
            product_code               = #{productCode,jdbcType=VARCHAR},
            product_factory_code       = #{productFactoryCode,jdbcType=VARCHAR},
            vehicle_model_code          = #{vehicleModelCode,jdbcType=VARCHAR},
            safety_stock_days_min      = #{safetyStockDaysMin,jdbcType=VARCHAR},
            safety_stock_days_standard = #{safetyStockDaysStandard,jdbcType=VARCHAR},
            safety_stock_days_max      = #{safetyStockDaysMax,jdbcType=VARCHAR},
            port_inventory_days        = #{portInventoryDays,jdbcType=VARCHAR},
            remark                      = #{remark,jdbcType=VARCHAR},
            enabled                    = #{enabled,jdbcType=VARCHAR},
            modifier                   = #{modifier,jdbcType=VARCHAR},
            modify_time                = #{modifyTime,jdbcType=TIMESTAMP},
            version_value      = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.GlassInventoryShiftDataPO">
        update mrp_glass_inventory_shift_data
        <set>
            <if test="item.materialPlanVersionId != null and item.materialPlanVersionId != ''">
                material_plan_version_id = #{item.materialPlanVersionId,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productFactoryCode != null and item.productFactoryCode != ''">
                product_factory_code = #{item.productFactoryCode,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.safetyStockDaysMin != null">
                safety_stock_days_min = #{item.safetyStockDaysMin,jdbcType=VARCHAR},
            </if>
            <if test="item.safetyStockDaysStandard != null">
                safety_stock_days_standard = #{item.safetyStockDaysStandard,jdbcType=VARCHAR},
            </if>
            <if test="item.safetyStockDaysMax != null">
                safety_stock_days_max = #{item.safetyStockDaysMax,jdbcType=VARCHAR},
            </if>
            <if test="item.portInventoryDays != null">
                port_inventory_days = #{item.portInventoryDays,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_glass_inventory_shift_data
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="material_plan_version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialPlanVersionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_factory_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productFactoryCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_model_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.vehicleModelCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="safety_stock_days_min = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockDaysMin,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="safety_stock_days_standard = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockDaysStandard,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="safety_stock_days_max = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockDaysMax,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="port_inventory_days = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.portInventoryDays,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_glass_inventory_shift_data
            <set>
                <if test="item.materialPlanVersionId != null and item.materialPlanVersionId != ''">
                    material_plan_version_id = #{item.materialPlanVersionId,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productFactoryCode != null and item.productFactoryCode != ''">
                    product_factory_code = #{item.productFactoryCode,jdbcType=VARCHAR},
                </if>
                <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                    vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
                </if>
                <if test="item.safetyStockDaysMin != null">
                    safety_stock_days_min = #{item.safetyStockDaysMin,jdbcType=VARCHAR},
                </if>
                <if test="item.safetyStockDaysStandard != null">
                    safety_stock_days_standard = #{item.safetyStockDaysStandard,jdbcType=VARCHAR},
                </if>
                <if test="item.safetyStockDaysMax != null">
                    safety_stock_days_max = #{item.safetyStockDaysMax,jdbcType=VARCHAR},
                </if>
                <if test="item.portInventoryDays != null">
                    port_inventory_days = #{item.portInventoryDays,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_glass_inventory_shift_data
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_glass_inventory_shift_data where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <delete id="deleteAll">
        delete
        from mrp_glass_inventory_shift_data
    </delete>
</mapper>
