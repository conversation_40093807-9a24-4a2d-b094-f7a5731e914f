package com.yhl.scp.mrp.compare.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.compare.dto.MaterialChangeCompareDTO;
import com.yhl.scp.mrp.compare.service.MaterialChangeCompareService;
import com.yhl.scp.mrp.compare.vo.MaterialChangeCompareVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MaterialChangeCompareController</code>
 * <p>
 * 材料计划变动提醒-材料对比控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-17 15:59:49
 */
@Slf4j
@Api(tags = "材料计划变动提醒-材料对比控制器")
@RestController
@RequestMapping("materialChangeCompare")
public class MaterialChangeCompareController extends BaseController {

    @Resource
    private MaterialChangeCompareService materialChangeCompareService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<MaterialChangeCompareVO>> page() {
        List<MaterialChangeCompareVO> materialChangeCompareList = materialChangeCompareService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MaterialChangeCompareVO> pageInfo = new PageInfo<>(materialChangeCompareList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MaterialChangeCompareDTO materialChangeCompareDTO) {
        return materialChangeCompareService.doCreate(materialChangeCompareDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MaterialChangeCompareDTO materialChangeCompareDTO) {
        return materialChangeCompareService.doUpdate(materialChangeCompareDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        materialChangeCompareService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<MaterialChangeCompareVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialChangeCompareService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "对比计算")
    @GetMapping(value = "compareCalc")
    @SuppressWarnings("unchecked")
    public void compareCalc() {
        materialChangeCompareService.doCompareCalc();
    }

}
