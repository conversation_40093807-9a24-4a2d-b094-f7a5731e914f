<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.dao.GlassInventoryDynamicBasicReportDao">
    <resultMap id="BaseResultMap"
               type="com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.po.GlassInventoryDynamicBasicReportPO">
        <!--@Table mrp_glass_inventory_dynamic_basic_report-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="product_color" jdbcType="VARCHAR" property="productColor"/>
        <result column="product_color_desc" jdbcType="VARCHAR" property="productColorDesc"/>
        <result column="product_thickness" jdbcType="VARCHAR" property="productThickness"/>
        <result column="show_sequence" jdbcType="INTEGER" property="showSequence"/>
        <result column="group_flag" jdbcType="VARCHAR" property="groupFlag"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.report.glassInventoryDynamic.vo.GlassInventoryDynamicBasicReportVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,product_color,product_color_desc,product_thickness,show_sequence,group_flag,enabled,creator,create_time,modifier,modify_time,remark,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.productColor != null and params.productColor != ''">
                and product_color = #{params.productColor,jdbcType=VARCHAR}
            </if>
            <if test="params.productColorDesc != null and params.productColorDesc != ''">
                and product_color_desc = #{params.productColorDesc,jdbcType=VARCHAR}
            </if>
            <if test="params.productThickness != null and params.productThickness != ''">
                and product_thickness = #{params.productThickness,jdbcType=VARCHAR}
            </if>
            <if test="params.showSequence != null">
                and show_sequence = #{params.showSequence,jdbcType=INTEGER}
            </if>
            <if test="params.groupFlag != null and params.groupFlag != ''">
                and group_flag = #{params.groupFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_glass_inventory_dynamic_basic_report
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_glass_inventory_dynamic_basic_report
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mrp_glass_inventory_dynamic_basic_report
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_glass_inventory_dynamic_basic_report
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert"
            parameterType="com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.po.GlassInventoryDynamicBasicReportPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_glass_inventory_dynamic_basic_report(
        id,
        product_color,
        product_color_desc,
        product_thickness,
        show_sequence,
        group_flag,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        remark,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{productColor,jdbcType=VARCHAR},
        #{productColorDesc,jdbcType=VARCHAR},
        #{productThickness,jdbcType=VARCHAR},
        #{showSequence,jdbcType=INTEGER},
        #{groupFlag,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.po.GlassInventoryDynamicBasicReportPO">
        insert into mrp_glass_inventory_dynamic_basic_report(id,
                                                             product_color,
                                                             product_color_desc,
                                                             product_thickness,
                                                             show_sequence,
                                                             group_flag,
                                                             enabled,
                                                             creator,
                                                             create_time,
                                                             modifier,
                                                             modify_time,
                                                             remark,
                                                             version_value)
        values (#{id,jdbcType=VARCHAR},
                #{productColor,jdbcType=VARCHAR},
                #{productColorDesc,jdbcType=VARCHAR},
                #{productThickness,jdbcType=VARCHAR},
                #{showSequence,jdbcType=INTEGER},
                #{groupFlag,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{remark,jdbcType=VARCHAR},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_glass_inventory_dynamic_basic_report(
        id,
        product_color,
        product_color_desc,
        product_thickness,
        show_sequence,
        group_flag,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        remark,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.productColor,jdbcType=VARCHAR},
            #{entity.productColorDesc,jdbcType=VARCHAR},
            #{entity.productThickness,jdbcType=VARCHAR},
            #{entity.showSequence,jdbcType=INTEGER},
            #{entity.groupFlag,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_glass_inventory_dynamic_basic_report(
        id,
        product_color,
        product_color_desc,
        product_thickness,
        show_sequence,
        group_flag,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        remark,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.productColor,jdbcType=VARCHAR},
            #{entity.productColorDesc,jdbcType=VARCHAR},
            #{entity.productThickness,jdbcType=VARCHAR},
            #{entity.showSequence,jdbcType=INTEGER},
            #{entity.groupFlag,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update"
            parameterType="com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.po.GlassInventoryDynamicBasicReportPO">
        update mrp_glass_inventory_dynamic_basic_report
        set product_color      = #{productColor,jdbcType=VARCHAR},
            product_color_desc = #{productColorDesc,jdbcType=VARCHAR},
            product_thickness  = #{productThickness,jdbcType=VARCHAR},
            show_sequence      = #{showSequence,jdbcType=INTEGER},
            group_flag         = #{groupFlag,jdbcType=VARCHAR},
            enabled            = #{enabled,jdbcType=VARCHAR},
            modifier           = #{modifier,jdbcType=VARCHAR},
            modify_time        = #{modifyTime,jdbcType=TIMESTAMP},
            remark             = #{remark,jdbcType=VARCHAR},
            version_value      = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.po.GlassInventoryDynamicBasicReportPO">
        update mrp_glass_inventory_dynamic_basic_report
        <set>
            <if test="item.productColor != null and item.productColor != ''">
                product_color = #{item.productColor,jdbcType=VARCHAR},
            </if>
            <if test="item.productColorDesc != null and item.productColorDesc != ''">
                product_color_desc = #{item.productColorDesc,jdbcType=VARCHAR},
            </if>
            <if test="item.productThickness != null and item.productThickness != ''">
                product_thickness = #{item.productThickness,jdbcType=VARCHAR},
            </if>
            <if test="item.showSequence != null">
                show_sequence = #{item.showSequence,jdbcType=INTEGER},
            </if>
            <if test="item.groupFlag != null and item.groupFlag != ''">
                group_flag = #{item.groupFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_glass_inventory_dynamic_basic_report
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="product_color = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productColor,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_color_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productColorDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_thickness = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productThickness,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="show_sequence = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.showSequence,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="group_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.groupFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_glass_inventory_dynamic_basic_report
            <set>
                <if test="item.productColor != null and item.productColor != ''">
                    product_color = #{item.productColor,jdbcType=VARCHAR},
                </if>
                <if test="item.productColorDesc != null and item.productColorDesc != ''">
                    product_color_desc = #{item.productColorDesc,jdbcType=VARCHAR},
                </if>
                <if test="item.productThickness != null and item.productThickness != ''">
                    product_thickness = #{item.productThickness,jdbcType=VARCHAR},
                </if>
                <if test="item.showSequence != null">
                    show_sequence = #{item.showSequence,jdbcType=INTEGER},
                </if>
                <if test="item.groupFlag != null and item.groupFlag != ''">
                    group_flag = #{item.groupFlag,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_glass_inventory_dynamic_basic_report
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_glass_inventory_dynamic_basic_report where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
