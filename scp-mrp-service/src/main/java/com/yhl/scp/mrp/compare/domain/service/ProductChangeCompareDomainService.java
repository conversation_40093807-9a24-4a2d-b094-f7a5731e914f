package com.yhl.scp.mrp.compare.domain.service;

import com.yhl.scp.mrp.compare.domain.entity.ProductChangeCompareDO;
import com.yhl.scp.mrp.compare.infrastructure.dao.ProductChangeCompareDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>ProductChangeCompare2DomainService</code>
 * <p>
 * 材料计划变动提醒-产品对比领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-18 09:56:44
 */
@Service
public class ProductChangeCompareDomainService {

    @Resource
    private ProductChangeCompareDao productChangeCompareDao;

    /**
     * 数据校验
     *
     * @param productChangeCompareDO 领域对象
     */
    public void validation(ProductChangeCompareDO productChangeCompareDO) {
        checkNotNull(productChangeCompareDO);
        checkUniqueCode(productChangeCompareDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param productChangeCompareDO 领域对象
     */
    private void checkNotNull(ProductChangeCompareDO productChangeCompareDO) {

    }

    /**
     * 唯一性校验
     *
     * @param productChangeCompareDO 领域对象
     */
    private void checkUniqueCode(ProductChangeCompareDO productChangeCompareDO) {

    }

}
