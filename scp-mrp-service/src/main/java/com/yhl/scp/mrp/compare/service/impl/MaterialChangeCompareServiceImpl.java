package com.yhl.scp.mrp.compare.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.supplier.vo.SupplierPurchaseRatioVO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mrp.compare.convertor.MaterialChangeCompareConvertor;
import com.yhl.scp.mrp.compare.domain.entity.MaterialChangeCompareDO;
import com.yhl.scp.mrp.compare.domain.service.MaterialChangeCompareDomainService;
import com.yhl.scp.mrp.compare.dto.MaterialChangeCompareDTO;
import com.yhl.scp.mrp.compare.infrastructure.dao.MaterialChangeCompareDao;
import com.yhl.scp.mrp.compare.infrastructure.po.MaterialChangeComparePO;
import com.yhl.scp.mrp.compare.service.MaterialChangeCompareService;
import com.yhl.scp.mrp.compare.service.ProductChangeCompareService;
import com.yhl.scp.mrp.compare.vo.MaterialChangeCompareVO;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingService;
import com.yhl.scp.mrp.material.plan.service.NoGlassInventoryShiftDataService;
import com.yhl.scp.mrp.material.plan.service.NoGlassInventoryShiftDetailService;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDataVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDetailVO;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MaterialChangeCompareServiceImpl</code>
 * <p>
 * 材料计划变动提醒-材料对比应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-17 15:59:51
 */
@Slf4j
@Service
public class MaterialChangeCompareServiceImpl extends AbstractService implements MaterialChangeCompareService {

    @Resource
    private MaterialChangeCompareDao materialChangeCompareDao;

    @Resource
    private MaterialChangeCompareDomainService materialChangeCompareDomainService;

    @Resource
    private ProductChangeCompareService productChangeCompareService;

    @Resource
    private NoGlassInventoryShiftDataService noGlassInventoryShiftDataService;

    @Resource
    private NoGlassInventoryShiftDetailService noGlassInventoryShiftDetailService;

    @Resource
    private MaterialArrivalTrackingService materialArrivalTrackingService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(MaterialChangeCompareDTO materialChangeCompareDTO) {
        // 0.数据转换
        MaterialChangeCompareDO materialChangeCompareDO = MaterialChangeCompareConvertor.INSTANCE.dto2Do(materialChangeCompareDTO);
        MaterialChangeComparePO materialChangeComparePO = MaterialChangeCompareConvertor.INSTANCE.dto2Po(materialChangeCompareDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialChangeCompareDomainService.validation(materialChangeCompareDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialChangeComparePO);
        materialChangeCompareDao.insert(materialChangeComparePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(MaterialChangeCompareDTO materialChangeCompareDTO) {
        // 0.数据转换
        MaterialChangeCompareDO materialChangeCompareDO = MaterialChangeCompareConvertor.INSTANCE.dto2Do(materialChangeCompareDTO);
        MaterialChangeComparePO materialChangeComparePO = MaterialChangeCompareConvertor.INSTANCE.dto2Po(materialChangeCompareDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialChangeCompareDomainService.validation(materialChangeCompareDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialChangeComparePO);
        materialChangeCompareDao.update(materialChangeComparePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialChangeCompareDTO> list) {
        List<MaterialChangeComparePO> newList = MaterialChangeCompareConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialChangeCompareDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialChangeCompareDTO> list) {
        List<MaterialChangeComparePO> newList = MaterialChangeCompareConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialChangeCompareDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialChangeCompareDao.deleteBatch(idList);
        }
        return materialChangeCompareDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialChangeCompareVO selectByPrimaryKey(String id) {
        MaterialChangeComparePO po = materialChangeCompareDao.selectByPrimaryKey(id);
        return MaterialChangeCompareConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MATERIAL_CHANGE_COMPARE")
    public List<MaterialChangeCompareVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "MATERIAL_CHANGE_COMPARE")
    public List<MaterialChangeCompareVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialChangeCompareVO> dataList = materialChangeCompareDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialChangeCompareServiceImpl target = SpringBeanUtils.getBean(MaterialChangeCompareServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialChangeCompareVO> selectByParams(Map<String, Object> params) {
        List<MaterialChangeComparePO> list = materialChangeCompareDao.selectByParams(params);
        return MaterialChangeCompareConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialChangeCompareVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<MaterialChangeCompareVO> invocation(List<MaterialChangeCompareVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public void doCompareCalc() {
        String scenario = SystemHolder.getScenario();
        // 获取非原片Mrp运行结果
        List<NoGlassInventoryShiftDataVO> shiftDataVOList = noGlassInventoryShiftDataService.selectAll();
        List<NoGlassInventoryShiftDetailVO> shiftDetailVOList = noGlassInventoryShiftDetailService.selectAll();

        // 获取前30天数据
        Date date = new Date();
        Date moveDay = DateUtils.moveDay(date, 30);

        // 获取30天后8个月的数据
        Date moveMonth = DateUtils.moveMonth(DateUtils.moveDay(moveDay, 1), 8);
        // 获取到货跟踪数据
        List<MaterialArrivalTrackingVO> trackingVOList = materialArrivalTrackingService.selectByParams(new HashMap<>(2));
        Map<String, NoGlassInventoryShiftDataVO> shiftDataVOMap = shiftDataVOList.stream()
                .collect(Collectors.toMap(NoGlassInventoryShiftDataVO::getProductCode, Function.identity()));
        // 按照dataId进行分组
        Map<String, List<NoGlassInventoryShiftDetailVO>> shiftDetailGroup = shiftDetailVOList.stream()
                .collect(Collectors.groupingBy(NoGlassInventoryShiftDetailVO::getNoGlassInventoryShiftDataId));
        // 到货跟踪数据按照材料+时间进行分组
        Map<String, List<MaterialArrivalTrackingVO>> trackingGroup = trackingVOList.stream()
                .collect(Collectors.groupingBy(item ->
                        String.join("&", item.getMaterialCode(), DateUtils.dateToString(item.getPredictArrivalDate()))));

        // 查询关联数据（供应商信息等）
        List<String> materialCodeList = shiftDataVOList.stream().map(NoGlassInventoryShiftDataVO::getProductCode).distinct().collect(Collectors.toList());
        Map<String, MaterialSupplierPurchaseVO> supplierPurchaseVOMapOfMaterialCode = new HashMap<>();
        Map<String, List<SupplierPurchaseRatioVO>> supplierPurchaseRatioVOGroup = new HashMap<>();
        Map<String, SupplierVO> supplierVOMapOfId = new HashMap<>();
        Map<String, Object> supplierInfoMap = noGlassInventoryShiftDataService.getSupplierInfoByMaterialCodeList(materialCodeList);
        if (supplierInfoMap.containsKey("supplierPurchaseVOMapOfMaterialCode")){
            supplierPurchaseVOMapOfMaterialCode = (Map<String, MaterialSupplierPurchaseVO>) supplierInfoMap.get("supplierPurchaseVOMapOfMaterialCode");
        }
        if (supplierInfoMap.containsKey("supplierPurchaseRatioVOGroup")){
            supplierPurchaseRatioVOGroup = (Map<String, List<SupplierPurchaseRatioVO>>) supplierInfoMap.get("supplierPurchaseRatioVOGroup");
        }
        if (supplierInfoMap.containsKey("supplierVOMapOfId")){
            supplierVOMapOfId = (Map<String, SupplierVO>) supplierInfoMap.get("supplierVOMapOfId");
        }


        // 创建主数据
        List<MaterialChangeCompareDTO> list = createMaterialChangeCompareDto(shiftDataVOList, supplierPurchaseVOMapOfMaterialCode, supplierPurchaseRatioVOGroup, supplierVOMapOfId);

        // 对比周维度
        compareWeekData(shiftDataVOMap, shiftDetailGroup, date, moveDay, trackingGroup, list);

        // 对比月维度
        compareMonthData(shiftDataVOMap, shiftDetailGroup, DateUtils.moveDay(moveDay, 1), moveMonth, trackingGroup, list);

        // 计算波动率
        calculateTotalVolatility(list);

        // 持久化数据
        this.doDeleteAll();
        Lists.partition(list, 3000).forEach(this::doCreateBatch);
    }

    private void calculateTotalVolatility(List<MaterialChangeCompareDTO> list) {
        for (MaterialChangeCompareDTO materialChangeCompareDTO : list) {
            List<BigDecimal> volatilityList = materialChangeCompareDTO.getVolatilityList();
            if (CollectionUtils.isEmpty(volatilityList)) {
                materialChangeCompareDTO.setVolatility(BigDecimal.ZERO);
                continue;
            }
            BigDecimal totalVolatility = volatilityList.stream()
                    .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            materialChangeCompareDTO.setVolatility(totalVolatility.divide(new BigDecimal(volatilityList.size()), 2, RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP));
        }
    }

    private void compareMonthData(Map<String, NoGlassInventoryShiftDataVO> shiftDataVOMap,
                                  Map<String, List<NoGlassInventoryShiftDetailVO>> shiftDetailGroup,
                                  Date nowDate, Date moveMonth,
                                  Map<String, List<MaterialArrivalTrackingVO>> trackingGroup,
                                  List<MaterialChangeCompareDTO> list) {
        List<Date> dateList = DateUtils.getIntervalDates(nowDate, moveMonth);
        // 执行分组，按照30天分组
        List<List<Date>> dateListGroup = productChangeCompareService.groupCollection(dateList, 30, 8);
        for (MaterialChangeCompareDTO materialChangeCompareDTO : list) {
            NoGlassInventoryShiftDataVO shiftDataVO = shiftDataVOMap.get(materialChangeCompareDTO.getMaterialCode());
            List<BigDecimal> volatilityList = materialChangeCompareDTO.getVolatilityList();
            List<NoGlassInventoryShiftDetailVO> newDataList = shiftDetailGroup.get(shiftDataVO.getId());

            // 按照时间排序
            newDataList = newDataList.stream()
                    .sorted(Comparator.comparing(NoGlassInventoryShiftDetailVO::getInventoryDate)).collect(Collectors.toList());
            // 按照时间进行分组
            Map<String, List<NoGlassInventoryShiftDetailVO>> newDataGroup = newDataList
                    .stream().collect(Collectors.groupingBy(item -> DateUtils.dateToString(item.getInventoryDate())));

            int count = 0;
            for (List<Date> dates : dateListGroup) {
                BigDecimal currentQuantitySum = BigDecimal.ZERO;
                BigDecimal changeQuantitySum = BigDecimal.ZERO;
                for (Date date : dates) {
                    String dateStr = DateUtils.dateToString(date);
                    String joinKey = String.join("&", materialChangeCompareDTO.getMaterialCode(), dateStr);
                    List<NoGlassInventoryShiftDetailVO> newList = newDataGroup.getOrDefault(dateStr, new ArrayList<>());
                    // 求和采购数量
                    BigDecimal currentQuantity = newList.stream()
                            .map(NoGlassInventoryShiftDetailVO::getAdjustQuantity)
                            .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 获取到货跟踪数据
                    List<MaterialArrivalTrackingVO> materialArrivalTrackingVOList = trackingGroup.getOrDefault(joinKey, new ArrayList<>());
                    // 获取推荐日期不为空的数据
                    List<MaterialArrivalTrackingVO> filterList = materialArrivalTrackingVOList.stream()
                            .filter(item -> item.getRecommendRequireDate() != null && StringUtils.equals(dateStr, DateUtils.dateToString(item.getRecommendRequireDate())))
                            .collect(Collectors.toList());

                    BigDecimal changeQuantity = BigDecimal.ZERO;
                    if (CollectionUtils.isNotEmpty(filterList)) {
                        changeQuantity = filterList.stream()
                                .map(MaterialArrivalTrackingVO::getWaitDeliveryQuantity)
                                .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    currentQuantitySum = currentQuantitySum.add(currentQuantity);
                    changeQuantitySum = changeQuantitySum.add(changeQuantity);
                }

                BigDecimal variationQuantity = currentQuantitySum.subtract(changeQuantitySum);
                // 计算波动率
                if (changeQuantitySum.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal volatility = variationQuantity.divide(changeQuantitySum, 2, RoundingMode.HALF_UP);
                    volatilityList.add(volatility);
                } else {
                    volatilityList.add(BigDecimal.ZERO);
                }
                count++;
                switch (count) {
                    case 1:
                        materialChangeCompareDTO.setPurchaseDemandOneMonthCurrentQuantity(currentQuantitySum);
                        materialChangeCompareDTO.setPurchaseDemandOneMonthChangeQuantity(variationQuantity);
                        break;
                    case 2:
                        materialChangeCompareDTO.setPurchaseDemandTwoMonthCurrentQuantity(currentQuantitySum);
                        materialChangeCompareDTO.setPurchaseDemandTwoMonthChangeQuantity(variationQuantity);
                        break;
                    case 3:
                        materialChangeCompareDTO.setPurchaseDemandThreeMonthCurrentQuantity(currentQuantitySum);
                        materialChangeCompareDTO.setPurchaseDemandThreeMonthChangeQuantity(variationQuantity);
                        break;
                    case 4:
                        materialChangeCompareDTO.setPurchaseDemandFourMonthCurrentQuantity(currentQuantitySum);
                        materialChangeCompareDTO.setPurchaseDemandFourMonthChangeQuantity(variationQuantity);
                        break;
                    case 5:
                        materialChangeCompareDTO.setPurchaseDemandFiveMonthCurrentQuantity(currentQuantitySum);
                        materialChangeCompareDTO.setPurchaseDemandFiveMonthChangeQuantity(variationQuantity);
                        break;
                    case 6:
                        materialChangeCompareDTO.setPurchaseDemandSixMonthCurrentQuantity(currentQuantitySum);
                        materialChangeCompareDTO.setPurchaseDemandSixMonthChangeQuantity(variationQuantity);
                        break;
                    case 7:
                        materialChangeCompareDTO.setPurchaseDemandSenveMonthCurrentQuantity(currentQuantitySum);
                        materialChangeCompareDTO.setPurchaseDemandSenveMonthChangeQuantity(variationQuantity);
                        break;
                    case 8:
                        materialChangeCompareDTO.setPurchaseDemandEightMonthCurrentQuantity(currentQuantitySum);
                        materialChangeCompareDTO.setPurchaseDemandEightMonthChangeQuantity(variationQuantity);
                        break;
                    default:
                        break;
                }
            }
        }
    }

    private void compareWeekData(Map<String, NoGlassInventoryShiftDataVO> shiftDataVOMap,
                                 Map<String, List<NoGlassInventoryShiftDetailVO>> shiftDetailGroup,
                                 Date nowDate, Date moveDay,
                                 Map<String, List<MaterialArrivalTrackingVO>> trackingGroup,
                                 List<MaterialChangeCompareDTO> list) {
        List<Date> dateList = DateUtils.getIntervalDates(nowDate, moveDay);
        // 执行分组，按照7天分组
        List<List<Date>> dateListGroup = productChangeCompareService.groupCollection(dateList, 7, 4);
        for (MaterialChangeCompareDTO materialChangeCompareDTO : list) {
            NoGlassInventoryShiftDataVO shiftDataVO = shiftDataVOMap.get(materialChangeCompareDTO.getMaterialCode());
            List<BigDecimal> volatilityList = materialChangeCompareDTO.getVolatilityList();
            List<NoGlassInventoryShiftDetailVO> newDataList = shiftDetailGroup.get(shiftDataVO.getId());

            // 按照时间排序
            newDataList = newDataList.stream()
                    .sorted(Comparator.comparing(NoGlassInventoryShiftDetailVO::getInventoryDate)).collect(Collectors.toList());
            // 按照时间进行分组
            Map<String, List<NoGlassInventoryShiftDetailVO>> newDataGroup = newDataList
                    .stream().collect(Collectors.groupingBy(item -> DateUtils.dateToString(item.getInventoryDate())));

            int count = 0;
            for (List<Date> dates : dateListGroup) {
                BigDecimal currentQuantitySum = BigDecimal.ZERO;
                BigDecimal changeQuantitySum = BigDecimal.ZERO;
                for (Date date : dates) {
                    String dateStr = DateUtils.dateToString(date);
                    String joinKey = String.join("&", materialChangeCompareDTO.getMaterialCode(), dateStr);
                    List<NoGlassInventoryShiftDetailVO> newList = newDataGroup.get(dateStr);
                    // 求和采购数量
                    BigDecimal currentQuantity = newList.stream()
                            .map(NoGlassInventoryShiftDetailVO::getAdjustQuantity)
                            .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 获取到货跟踪数据
                    List<MaterialArrivalTrackingVO> materialArrivalTrackingVOList = trackingGroup.getOrDefault(joinKey, new ArrayList<>());
                    // 获取推荐日期不为空的数据
                    List<MaterialArrivalTrackingVO> filterList = materialArrivalTrackingVOList.stream()
                            .filter(item -> item.getRecommendRequireDate() != null && StringUtils.equals(dateStr, DateUtils.dateToString(item.getRecommendRequireDate())))
                            .collect(Collectors.toList());

                    BigDecimal changeQuantity = BigDecimal.ZERO;
                    if (CollectionUtils.isNotEmpty(filterList)) {
                        changeQuantity = filterList.stream()
                                .map(MaterialArrivalTrackingVO::getWaitDeliveryQuantity)
                                .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    currentQuantitySum = currentQuantitySum.add(currentQuantity);
                    changeQuantitySum = changeQuantitySum.add(changeQuantity);
                }

                BigDecimal variationQuantity = currentQuantitySum.subtract(changeQuantitySum);
                // 计算波动率
                if (changeQuantitySum.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal volatility = variationQuantity.divide(changeQuantitySum, 2, RoundingMode.HALF_UP);
                    volatilityList.add(volatility);
                } else {
                    volatilityList.add(BigDecimal.ZERO);
                }
                count++;
                switch (count) {
                    case 1:
                        materialChangeCompareDTO.setPurchaseDemandOneWeekCurrentQuantity(currentQuantitySum);
                        materialChangeCompareDTO.setPurchaseDemandOneWeekChangeQuantity(variationQuantity);
                        break;
                    case 2:
                        materialChangeCompareDTO.setPurchaseDemandTwoWeekCurrentQuantity(currentQuantitySum);
                        materialChangeCompareDTO.setPurchaseDemandTwoWeekChangeQuantity(variationQuantity);
                        break;
                    case 3:
                        materialChangeCompareDTO.setPurchaseDemandThreeWeekCurrentQuantity(currentQuantitySum);
                        materialChangeCompareDTO.setPurchaseDemandThreeWeekChangeQuantity(variationQuantity);
                        break;
                    case 4:
                        materialChangeCompareDTO.setPurchaseDemandFourWeekCurrentQuantity(currentQuantitySum);
                        materialChangeCompareDTO.setPurchaseDemandFourWeekChangeQuantity(variationQuantity);
                        break;
                    default:
                        break;
                }
            }
        }
    }

    private List<MaterialChangeCompareDTO> createMaterialChangeCompareDto(List<NoGlassInventoryShiftDataVO> shiftDataVOList,
                                                                          Map<String, MaterialSupplierPurchaseVO> supplierPurchaseVOMapOfMaterialCode,
                                                                          Map<String, List<SupplierPurchaseRatioVO>> supplierPurchaseRatioVOGroup,
                                                                          Map<String, SupplierVO> supplierVOMapOfId) {
        List<MaterialChangeCompareDTO> list = new ArrayList<>();
        for (NoGlassInventoryShiftDataVO shiftDataVO : shiftDataVOList) {
            MaterialChangeCompareDTO dto = new MaterialChangeCompareDTO();
            list.add(dto);
            dto.setProductCode(shiftDataVO.getProductFactoryCode());
            dto.setMaterialCode(shiftDataVO.getProductCode());
            dto.setVehicleModelCode(shiftDataVO.getVehicleModeCode());

            MaterialSupplierPurchaseVO materialSupplierPurchaseVO = supplierPurchaseVOMapOfMaterialCode.get(shiftDataVO.getProductCode());
            if (null == materialSupplierPurchaseVO){
                continue;
            }
            List<SupplierPurchaseRatioVO> supplierPurchaseRatioVOList = supplierPurchaseRatioVOGroup.get(materialSupplierPurchaseVO.getId());
            if (CollectionUtils.isEmpty(supplierPurchaseRatioVOList)){
                continue;
            }
            StringBuilder supplierCode = new StringBuilder();
            StringBuilder supplierName = new StringBuilder();
            for (SupplierPurchaseRatioVO supplierPurchaseRatioVO : supplierPurchaseRatioVOList) {
                SupplierVO supplierVO = supplierVOMapOfId.get(supplierPurchaseRatioVO.getSupplierId());
                if (null != supplierVO) {
                    supplierCode.append(supplierVO.getSupplierCode()).append("|");
                    supplierName.append(supplierVO.getSupplierName()).append("|");
                }
            }

            // 去除最后一个竖线
            if (supplierCode.length() > 0) {
                dto.setSupplierCode(supplierCode.substring(0, supplierCode.length() - 1));
            }
            if (supplierName.length() > 0) {
                dto.setSupplierName(supplierName.substring(0, supplierName.length() - 1));
            }
        }
        return list;
    }

    @Override
    public void doDeleteAll() {
        materialChangeCompareDao.deleteAll();
    }
}
