package com.yhl.scp.mrp.compare.convertor;

import com.yhl.scp.mrp.compare.domain.entity.MaterialChangeCompareDO;
import com.yhl.scp.mrp.compare.dto.MaterialChangeCompareDTO;
import com.yhl.scp.mrp.compare.infrastructure.po.MaterialChangeComparePO;
import com.yhl.scp.mrp.compare.vo.MaterialChangeCompareVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>MaterialChangeCompareConvertor</code>
 * <p>
 * 材料计划变动提醒-材料对比转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-17 15:59:59
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MaterialChangeCompareConvertor {

    MaterialChangeCompareConvertor INSTANCE = Mappers.getMapper(MaterialChangeCompareConvertor.class);

    MaterialChangeCompareDO dto2Do(MaterialChangeCompareDTO obj);

    MaterialChangeCompareDTO do2Dto(MaterialChangeCompareDO obj);

    List<MaterialChangeCompareDO> dto2Dos(List<MaterialChangeCompareDTO> list);

    List<MaterialChangeCompareDTO> do2Dtos(List<MaterialChangeCompareDO> list);

    MaterialChangeCompareVO do2Vo(MaterialChangeCompareDO obj);

    MaterialChangeCompareVO po2Vo(MaterialChangeComparePO obj);

    List<MaterialChangeCompareVO> po2Vos(List<MaterialChangeComparePO> list);

    MaterialChangeComparePO do2Po(MaterialChangeCompareDO obj);

    MaterialChangeCompareDO po2Do(MaterialChangeComparePO obj);

    MaterialChangeComparePO dto2Po(MaterialChangeCompareDTO obj);

    List<MaterialChangeComparePO> dto2Pos(List<MaterialChangeCompareDTO> obj);

}
