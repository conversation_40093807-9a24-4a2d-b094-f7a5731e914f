<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.material.plan.infrastructure.dao.MaterialPlanNeedDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanNeedPO">
        <!--@Table mrp_material_plan_need-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="material_plan_inventory_shift_id" jdbcType="VARCHAR" property="materialPlanInventoryShiftId"/>
        <result column="material_plan_need_no" jdbcType="VARCHAR" property="materialPlanNeedNo"/>
        <result column="pre_version_need_quantity" jdbcType="VARCHAR" property="preVersionNeedQuantity"/>
        <result column="need_quantity" jdbcType="VARCHAR" property="needQuantity"/>
        <result column="need_date" jdbcType="TIMESTAMP" property="needDate"/>
        <result column="requirement_release_date" jdbcType="TIMESTAMP" property="requirementReleaseDate"/>
        <result column="purchase_order_code" jdbcType="VARCHAR" property="purchaseOrderCode"/>
        <result column="purchase_order_line_code" jdbcType="VARCHAR" property="purchaseOrderLineCode"/>
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="expected_arrival_time" jdbcType="TIMESTAMP" property="expectedArrivalTime"/>
        <result column="expected_arrival_quantity" jdbcType="VARCHAR" property="expectedArrivalQuantity"/>
        <result column="unshipped_quantity" jdbcType="VARCHAR" property="unshippedQuantity"/>
        <result column="supply_quantity" jdbcType="VARCHAR" property="supplyQuantity"/>
        <result column="order_quantity" jdbcType="VARCHAR" property="orderQuantity"/>
        <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime"/>
        <result column="publish_status" jdbcType="VARCHAR" property="publishStatus"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="publish_user" jdbcType="VARCHAR" property="publishUser"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="inventory_shift_version_code" jdbcType="VARCHAR" property="inventoryShiftVersionCode"/>
        <result column="request_cargo_plan_lock_day" jdbcType="VARCHAR" property="requestCargoPlanLockDay"/>
        <result column="supplier_purchase_ratio_id" jdbcType="VARCHAR" property="supplierPurchaseRatioId"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.extension.material.vo.MaterialPlanNeedVO">
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_category" jdbcType="VARCHAR" property="productCategory"/>
        <result column="supply_type" jdbcType="VARCHAR" property="supplyType"/>
        <result column="product_color" jdbcType="VARCHAR" property="productColor"/>
        <result column="product_thickness" jdbcType="VARCHAR" property="productThickness"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="organize_id" jdbcType="VARCHAR" property="organizeId"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="data_source" jdbcType="VARCHAR" property="dataSource"/>
        <result column="agreement_type" jdbcType="VARCHAR" property="agreementType"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,material_plan_inventory_shift_id,material_plan_need_no,pre_version_need_quantity,need_quantity,need_date,requirement_release_date
        ,purchase_order_code,purchase_order_line_code,supplier_code,supplier_name,expected_arrival_time,expected_arrival_quantity,unshipped_quantity
        ,supply_quantity,order_quantity,publish_time,publish_status,product_code,publish_user,parent_id,inventory_shift_version_code,request_cargo_plan_lock_day
        ,supplier_purchase_ratio_id
        ,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List"/>,stock_point_code,stock_point_name,product_name,product_type
        ,supply_type,product_color,product_thickness,product_category,order_no,organize_id,product_id,data_source,agreement_type
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.ids != null and params.ids.size() > 0">
                and id in
                <foreach collection="params.ids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.materialPlanInventoryShiftId != null and params.materialPlanInventoryShiftId != ''">
                and material_plan_inventory_shift_id = #{params.materialPlanInventoryShiftId,jdbcType=VARCHAR}
            </if>
            <if test="params.materialPlanInventoryShiftIds != null and params.materialPlanInventoryShiftIds.size() > 0">
                and material_plan_inventory_shift_id in
                <foreach collection="params.materialPlanInventoryShiftIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.materialPlanNeedNo != null and params.materialPlanNeedNo != ''">
                and material_plan_need_no = #{params.materialPlanNeedNo,jdbcType=VARCHAR}
            </if>
            <if test="params.preVersionNeedQuantity != null">
                and pre_version_need_quantity = #{params.preVersionNeedQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.needQuantity != null">
                and need_quantity = #{params.needQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.needDate != null">
                and need_date = #{params.needDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.requirementReleaseDate != null">
                and requirement_release_date = #{params.requirementReleaseDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.purchaseOrderCode != null and params.purchaseOrderCode != ''">
                and purchase_order_code = #{params.purchaseOrderCode,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseOrderLineCode != null and params.purchaseOrderLineCode != ''">
                and purchase_order_line_code = #{params.purchaseOrderLineCode,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierCode != null and params.supplierCode != ''">
                and supplier_code = #{params.supplierCode,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierName != null and params.supplierName != ''">
                and supplier_name = #{params.supplierName,jdbcType=VARCHAR}
            </if>
            <if test="params.expectedArrivalTime != null">
                and expected_arrival_time = #{params.expectedArrivalTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.expectedArrivalQuantity != null">
                and expected_arrival_quantity = #{params.expectedArrivalQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.unshippedQuantity != null">
                and unshipped_quantity = #{params.unshippedQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyQuantity != null">
                and supply_quantity = #{params.supplyQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.orderQuantity != null">
                and order_quantity = #{params.orderQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.publishTime != null">
                and publish_time = #{params.publishTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.publishStatus != null and params.publishStatus != ''">
                and publish_status = #{params.publishStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.publishUser != null and params.publishUser != ''">
                and publish_user = #{params.publishUser,jdbcType=VARCHAR}
            </if>
            <if test="params.parentId != null and params.parentId != ''">
                and parent_id = #{params.parentId,jdbcType=VARCHAR}
            </if>
            <if test="params.requestCargoPlanLockDay != null">
                and request_cargo_plan_lock_day = #{params.requestCargoPlanLockDay,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.productCodeList != null and params.productCodeList.size() > 0">
                and product_code in
                <foreach collection="params.productCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.minNeedDate != null and params.maxNeedDate != null">
                and need_date BETWEEN #{params.minNeedDate,jdbcType=VARCHAR} AND #{params.maxNeedDate,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryShiftVersionCode != null and params.inventoryShiftVersionCode != ''">
                and inventory_shift_version_code = #{params.inventoryShiftVersionCode,jdbcType=VARCHAR}
            </if>
            <if test="params.startModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{params.startModifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.materialPlanNeedNos != null and params.materialPlanNeedNos.size() > 0">
                and material_plan_need_no in
                <foreach collection="params.materialPlanNeedNos" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.supplierPurchaseRatioId != null and params.supplierPurchaseRatioId != ''">
                and supplier_purchase_ratio_id = #{params.supplierPurchaseRatioId,jdbcType=VARCHAR}
            </if>
            <if test="params.agreementType != null and params.agreementType != ''">
                and agreement_type = #{params.agreementType,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_need
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_need
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_plan_need
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_need
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 条件查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_plan_need
        <include refid="Base_Where_Condition"/>
    </select>

    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanNeedPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_material_plan_need(
        id,
        material_plan_inventory_shift_id,
        material_plan_need_no,
        pre_version_need_quantity,
        need_quantity,
        need_date,
        requirement_release_date,
        purchase_order_code,
        purchase_order_line_code,
        supplier_code,
        supplier_name,
        expected_arrival_time,
        expected_arrival_quantity,
        unshipped_quantity,
        supply_quantity,
        order_quantity,
        publish_time,
        publish_status,
        product_code,
        publish_user,
        parent_id,
        inventory_shift_version_code,
        request_cargo_plan_lock_day,
        supplier_purchase_ratio_id,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{materialPlanInventoryShiftId,jdbcType=VARCHAR},
        #{materialPlanNeedNo,jdbcType=VARCHAR},
        #{preVersionNeedQuantity,jdbcType=VARCHAR},
        #{needQuantity,jdbcType=VARCHAR},
        #{needDate,jdbcType=TIMESTAMP},
        #{requirementReleaseDate,jdbcType=TIMESTAMP},
        #{purchaseOrderCode,jdbcType=VARCHAR},
        #{purchaseOrderLineCode,jdbcType=VARCHAR},
        #{supplierCode,jdbcType=VARCHAR},
        #{supplierName,jdbcType=VARCHAR},
        #{expectedArrivalTime,jdbcType=TIMESTAMP},
        #{expectedArrivalQuantity,jdbcType=VARCHAR},
        #{unshippedQuantity,jdbcType=VARCHAR},
        #{supplyQuantity,jdbcType=VARCHAR},
        #{orderQuantity,jdbcType=VARCHAR},
        #{publishTime,jdbcType=TIMESTAMP},
        #{publishStatus,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{publishUser,jdbcType=VARCHAR},
        #{parentId,jdbcType=VARCHAR},
        #{inventoryShiftVersionCode,jdbcType=VARCHAR},
        #{requestCargoPlanLockDay,jdbcType=VARCHAR},
        #{supplierPurchaseRatioId,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanNeedPO">
        insert into mrp_material_plan_need(id,
                                           material_plan_inventory_shift_id,
                                           material_plan_need_no,
                                           pre_version_need_quantity,
                                           need_quantity,
                                           need_date,
                                           requirement_release_date,
                                           purchase_order_code,
                                           purchase_order_line_code,
                                           supplier_code,
                                           supplier_name,
                                           expected_arrival_time,
                                           expected_arrival_quantity,
                                           unshipped_quantity,
                                           supply_quantity,
                                           order_quantity,
                                           publish_time,
                                           publish_status,
                                           product_code,
                                           publish_user,
                                           parent_id,
                                           inventory_shift_version_code,
                                           request_cargo_plan_lock_day,
                                           supplier_purchase_ratio_id,
                                           enabled,
                                           creator,
                                           create_time,
                                           modifier,
                                           modify_time,
                                           version_value)
        values (#{id,jdbcType=VARCHAR},
                #{materialPlanInventoryShiftId,jdbcType=VARCHAR},
                #{materialPlanNeedNo,jdbcType=VARCHAR},
                #{preVersionNeedQuantity,jdbcType=VARCHAR},
                #{needQuantity,jdbcType=VARCHAR},
                #{needDate,jdbcType=TIMESTAMP},
                #{requirementReleaseDate,jdbcType=TIMESTAMP},
                #{purchaseOrderCode,jdbcType=VARCHAR},
                #{purchaseOrderLineCode,jdbcType=VARCHAR},
                #{supplierCode,jdbcType=VARCHAR},
                #{supplierName,jdbcType=VARCHAR},
                #{expectedArrivalTime,jdbcType=TIMESTAMP},
                #{expectedArrivalQuantity,jdbcType=VARCHAR},
                #{unshippedQuantity,jdbcType=VARCHAR},
                #{supplyQuantity,jdbcType=VARCHAR},
                #{orderQuantity,jdbcType=VARCHAR},
                #{publishTime,jdbcType=TIMESTAMP},
                #{publishStatus,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{publishUser,jdbcType=VARCHAR},
                #{parentId,jdbcType=VARCHAR},
                #{inventoryShiftVersionCode,jdbcType=VARCHAR},
                #{requestCargoPlanLockDay,jdbcType=VARCHAR},
                #{supplierPurchaseRatioId,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_plan_need(
        id,
        material_plan_inventory_shift_id,
        material_plan_need_no,
        pre_version_need_quantity,
        need_quantity,
        need_date,
        requirement_release_date,
        purchase_order_code,
        purchase_order_line_code,
        supplier_code,
        supplier_name,
        expected_arrival_time,
        expected_arrival_quantity,
        unshipped_quantity,
        supply_quantity,
        order_quantity,
        publish_time,
        publish_status,
        product_code,
        publish_user,
        parent_id,
        inventory_shift_version_code,
        request_cargo_plan_lock_day,
        supplier_purchase_ratio_id,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.materialPlanInventoryShiftId,jdbcType=VARCHAR},
            #{entity.materialPlanNeedNo,jdbcType=VARCHAR},
            #{entity.preVersionNeedQuantity,jdbcType=VARCHAR},
            #{entity.needQuantity,jdbcType=VARCHAR},
            #{entity.needDate,jdbcType=TIMESTAMP},
            #{entity.requirementReleaseDate,jdbcType=TIMESTAMP},
            #{entity.purchaseOrderCode,jdbcType=VARCHAR},
            #{entity.purchaseOrderLineCode,jdbcType=VARCHAR},
            #{entity.supplierCode,jdbcType=VARCHAR},
            #{entity.supplierName,jdbcType=VARCHAR},
            #{entity.expectedArrivalTime,jdbcType=TIMESTAMP},
            #{entity.expectedArrivalQuantity,jdbcType=VARCHAR},
            #{entity.unshippedQuantity,jdbcType=VARCHAR},
            #{entity.supplyQuantity,jdbcType=VARCHAR},
            #{entity.orderQuantity,jdbcType=VARCHAR},
            #{entity.publishTime,jdbcType=TIMESTAMP},
            #{entity.publishStatus,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.publishUser,jdbcType=VARCHAR},
            #{entity.parentId,jdbcType=VARCHAR},
            #{entity.inventoryShiftVersionCode,jdbcType=VARCHAR},
            #{entity.requestCargoPlanLockDay,jdbcType=VARCHAR},
            #{entity.supplierPurchaseRatioId,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_material_plan_need(
        id,
        material_plan_inventory_shift_id,
        material_plan_need_no,
        pre_version_need_quantity,
        need_quantity,
        need_date,
        requirement_release_date,
        purchase_order_code,
        purchase_order_line_code,
        supplier_code,
        supplier_name,
        expected_arrival_time,
        expected_arrival_quantity,
        unshipped_quantity,
        supply_quantity,
        order_quantity,
        publish_time,
        publish_status,
        product_code,
        publish_user,
        parent_id,
        inventory_shift_version_code,
        request_cargo_plan_lock_day,
        supplier_purchase_ratio_id,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.materialPlanInventoryShiftId,jdbcType=VARCHAR},
            #{entity.materialPlanNeedNo,jdbcType=VARCHAR},
            #{entity.preVersionNeedQuantity,jdbcType=VARCHAR},
            #{entity.needQuantity,jdbcType=VARCHAR},
            #{entity.needDate,jdbcType=TIMESTAMP},
            #{entity.requirementReleaseDate,jdbcType=TIMESTAMP},
            #{entity.purchaseOrderCode,jdbcType=VARCHAR},
            #{entity.purchaseOrderLineCode,jdbcType=VARCHAR},
            #{entity.supplierCode,jdbcType=VARCHAR},
            #{entity.supplierName,jdbcType=VARCHAR},
            #{entity.expectedArrivalTime,jdbcType=TIMESTAMP},
            #{entity.expectedArrivalQuantity,jdbcType=VARCHAR},
            #{entity.unshippedQuantity,jdbcType=VARCHAR},
            #{entity.supplyQuantity,jdbcType=VARCHAR},
            #{entity.orderQuantity,jdbcType=VARCHAR},
            #{entity.publishTime,jdbcType=TIMESTAMP},
            #{entity.publishStatus,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.publishUser,jdbcType=VARCHAR},
            #{entity.parentId,jdbcType=VARCHAR},
            #{entity.inventoryShiftVersionCode,jdbcType=VARCHAR},
            #{entity.requestCargoPlanLockDay,jdbcType=VARCHAR},
            #{entity.supplierPurchaseRatioId,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanNeedPO">
        update mrp_material_plan_need
        set material_plan_inventory_shift_id = #{materialPlanInventoryShiftId,jdbcType=VARCHAR},
            material_plan_need_no            = #{materialPlanNeedNo,jdbcType=VARCHAR},
            pre_version_need_quantity        = #{preVersionNeedQuantity,jdbcType=VARCHAR},
            need_quantity                    = #{needQuantity,jdbcType=VARCHAR},
            need_date                        = #{needDate,jdbcType=TIMESTAMP},
            requirement_release_date         = #{requirementReleaseDate,jdbcType=TIMESTAMP},
            purchase_order_code              = #{purchaseOrderCode,jdbcType=VARCHAR},
            purchase_order_line_code         = #{purchaseOrderLineCode,jdbcType=VARCHAR},
            supplier_code                    = #{supplierCode,jdbcType=VARCHAR},
            supplier_name                    = #{supplierName,jdbcType=VARCHAR},
            expected_arrival_time            = #{expectedArrivalTime,jdbcType=TIMESTAMP},
            expected_arrival_quantity        = #{expectedArrivalQuantity,jdbcType=VARCHAR},
            unshipped_quantity               = #{unshippedQuantity,jdbcType=VARCHAR},
            supply_quantity                  = #{supplyQuantity,jdbcType=VARCHAR},
            order_quantity                   = #{orderQuantity,jdbcType=VARCHAR},
            publish_time                     = #{publishTime,jdbcType=TIMESTAMP},
            publish_status                   = #{publishStatus,jdbcType=VARCHAR},
            product_code                     = #{productCode,jdbcType=VARCHAR},
            publish_user                     = #{publishUser,jdbcType=VARCHAR},
            parent_id                        = #{parentId,jdbcType=VARCHAR},
            inventory_shift_version_code     = #{inventoryShiftVersionCode,jdbcType=VARCHAR},
            request_cargo_plan_lock_day      = #{requestCargoPlanLockDay,jdbcType=VARCHAR},
            supplier_purchase_ratio_id       = #{supplierPurchaseRatioId,jdbcType=VARCHAR},
            enabled                          = #{enabled,jdbcType=VARCHAR},
            modifier                         = #{modifier,jdbcType=VARCHAR},
            modify_time                      = #{modifyTime,jdbcType=TIMESTAMP},
            version_value                    = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanNeedPO">
        update mrp_material_plan_need
        <set>
            <if test="item.materialPlanInventoryShiftId != null and item.materialPlanInventoryShiftId != ''">
                material_plan_inventory_shift_id = #{item.materialPlanInventoryShiftId,jdbcType=VARCHAR},
            </if>
            <if test="item.materialPlanNeedNo != null and item.materialPlanNeedNo != ''">
                material_plan_need_no = #{item.materialPlanNeedNo,jdbcType=VARCHAR},
            </if>
            <if test="item.preVersionNeedQuantity != null">
                pre_version_need_quantity = #{item.preVersionNeedQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.needQuantity != null">
                need_quantity = #{item.needQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.needDate != null">
                need_date = #{item.needDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.requirementReleaseDate != null">
                requirement_release_date = #{item.requirementReleaseDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.purchaseOrderCode != null and item.purchaseOrderCode != ''">
                purchase_order_code = #{item.purchaseOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseOrderLineCode != null and item.purchaseOrderLineCode != ''">
                purchase_order_line_code = #{item.purchaseOrderLineCode,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierCode != null and item.supplierCode != ''">
                supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierName != null and item.supplierName != ''">
                supplier_name = #{item.supplierName,jdbcType=VARCHAR},
            </if>
            <if test="item.expectedArrivalTime != null">
                expected_arrival_time = #{item.expectedArrivalTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.expectedArrivalQuantity != null">
                expected_arrival_quantity = #{item.expectedArrivalQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.unshippedQuantity != null">
                unshipped_quantity = #{item.unshippedQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyQuantity != null">
                supply_quantity = #{item.supplyQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.orderQuantity != null">
                order_quantity = #{item.orderQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.publishTime != null">
                publish_time = #{item.publishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.publishStatus != null and item.publishStatus != ''">
                publish_status = #{item.publishStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.publishUser != null and item.publishUser != ''">
                publish_user = #{item.publishUser,jdbcType=VARCHAR},
            </if>
            <if test="item.parentId != null and item.parentId != ''">
                parent_id = #{item.parentId,jdbcType=VARCHAR},
            </if>
            <if test="item.inventoryShiftVersionCode != null and item.inventoryShiftVersionCode != ''">
                inventory_shift_version_code = #{item.inventoryShiftVersionCode,jdbcType=VARCHAR},
            </if>
            <if test="item.requestCargoPlanLockDay != null">
                request_cargo_plan_lock_day = #{item.requestCargoPlanLockDay,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierPurchaseRatioId != null and item.supplierPurchaseRatioId != ''">
                supplier_purchase_ratio_id = #{item.supplierPurchaseRatioId,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_plan_need
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="material_plan_inventory_shift_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialPlanInventoryShiftId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_plan_need_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialPlanNeedNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="pre_version_need_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.preVersionNeedQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="need_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.needQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="need_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.needDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="requirement_release_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.requirementReleaseDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="purchase_order_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.purchaseOrderCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_order_line_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.purchaseOrderLineCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="expected_arrival_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.expectedArrivalTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="expected_arrival_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.expectedArrivalQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="unshipped_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.unshippedQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.orderQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="publish_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.publishTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="publish_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.publishStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="publish_user = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.publishUser,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="parent_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.parentId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="inventory_shift_version_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inventoryShiftVersionCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="request_cargo_plan_lock_day = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.requestCargoPlanLockDay,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_purchase_ratio_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierPurchaseRatioId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_material_plan_need
            <set>
                <if test="item.materialPlanInventoryShiftId != null and item.materialPlanInventoryShiftId != ''">
                    material_plan_inventory_shift_id = #{item.materialPlanInventoryShiftId,jdbcType=VARCHAR},
                </if>
                <if test="item.materialPlanNeedNo != null and item.materialPlanNeedNo != ''">
                    material_plan_need_no = #{item.materialPlanNeedNo,jdbcType=VARCHAR},
                </if>
                <if test="item.preVersionNeedQuantity != null">
                    pre_version_need_quantity = #{item.preVersionNeedQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.needQuantity != null">
                    need_quantity = #{item.needQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.needDate != null">
                    need_date = #{item.needDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.requirementReleaseDate != null">
                    requirement_release_date = #{item.requirementReleaseDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.purchaseOrderCode != null and item.purchaseOrderCode != ''">
                    purchase_order_code = #{item.purchaseOrderCode,jdbcType=VARCHAR},
                </if>
                <if test="item.purchaseOrderLineCode != null and item.purchaseOrderLineCode != ''">
                    purchase_order_line_code = #{item.purchaseOrderLineCode,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierCode != null and item.supplierCode != ''">
                    supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierName != null and item.supplierName != ''">
                    supplier_name = #{item.supplierName,jdbcType=VARCHAR},
                </if>
                <if test="item.expectedArrivalTime != null">
                    expected_arrival_time = #{item.expectedArrivalTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.expectedArrivalQuantity != null">
                    expected_arrival_quantity = #{item.expectedArrivalQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.unshippedQuantity != null">
                    unshipped_quantity = #{item.unshippedQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.supplyQuantity != null">
                    supply_quantity = #{item.supplyQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.orderQuantity != null">
                    order_quantity = #{item.orderQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.publishTime != null">
                    publish_time = #{item.publishTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.publishStatus != null and item.publishStatus != ''">
                    publish_status = #{item.publishStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.publishUser != null and item.publishUser != ''">
                    publish_user = #{item.publishUser,jdbcType=VARCHAR},
                </if>
                <if test="item.parentId != null and item.parentId != ''">
                    parent_id = #{item.parentId,jdbcType=VARCHAR},
                </if>
                <if test="item.inventoryShiftVersionCode != null and item.inventoryShiftVersionCode != ''">
                    inventory_shift_version_code = #{item.inventoryShiftVersionCode,jdbcType=VARCHAR},
                </if>
                <if test="item.requestCargoPlanLockDay != null">
                    request_cargo_plan_lock_day = #{item.requestCargoPlanLockDay,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierPurchaseRatioId != null and item.supplierPurchaseRatioId != ''">
                    supplier_purchase_ratio_id = #{item.supplierPurchaseRatioId,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_material_plan_need
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_plan_need where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteByPublishStatus">
        delete
        from mrp_material_plan_need
        where publish_status = #{publishStatus,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteByParams">
        delete
        from mrp_material_plan_need
        <where>
            <if test="params.publishStatus != null and params.publishStatus != ''">
                and publish_status = #{params.publishStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.userId != null and params.userId != ''">
                and creator = #{params.userId,jdbcType=VARCHAR}
            </if>
                and product_code in
            <foreach collection="params.productCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </where>
    </delete>
</mapper>