<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.material.plan.infrastructure.dao.MaterialPlanReplaceDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanReplacePO">
        <!--@Table mrp_material_plan_replace-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="material_plan_version_id" jdbcType="VARCHAR" property="materialPlanVersionId"/>
        <result column="material_plan_inventory_shift_id" jdbcType="VARCHAR" property="materialPlanInventoryShiftId"/>
        <result column="inventory_alternative_relationship_id" jdbcType="VARCHAR" property="inventoryAlternativeRelationshipId"/>
        <result column="master_product_code" jdbcType="VARCHAR" property="masterProductCode"/>
        <result column="replace_product_code" jdbcType="VARCHAR" property="replaceProductCode"/>
        <result column="replace_quantity" jdbcType="VARCHAR" property="replaceQuantity"/>
        <result column="replace_date" jdbcType="TIMESTAMP" property="replaceDate"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="replace_type" jdbcType="VARCHAR" property="replaceType"/>
        <result column="replace_start_date" jdbcType="TIMESTAMP" property="replaceStartDate"/>
        <result column="replace_end_date" jdbcType="TIMESTAMP" property="replaceEndDate"/>
        <result column="inventory_id" jdbcType="VARCHAR" property="inventoryId"/>
        <result column="inventory_type" jdbcType="VARCHAR" property="inventoryType"/>
        <result column="work_order_code" jdbcType="VARCHAR" property="workOrderCode"/>
        <result column="finished_product_code" jdbcType="VARCHAR" property="finishedProductCode"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.material.plan.vo.MaterialPlanReplaceVO">
        <result column="master_product_name" jdbcType="VARCHAR" property="masterProductName"/>
        <result column="master_product_color" jdbcType="VARCHAR" property="masterProductColor"/>
        <result column="master_product_thickness" jdbcType="VARCHAR" property="masterProductThickness"/>
        <result column="master_product_length" jdbcType="VARCHAR" property="masterProductLength"/>
        <result column="master_product_width" jdbcType="VARCHAR" property="masterProductWidth"/>
        <result column="replace_product_name" jdbcType="VARCHAR" property="replaceProductName"/>
        <result column="finished_product_name" jdbcType="VARCHAR" property="finishedProductName"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,material_plan_version_id,material_plan_inventory_shift_id,inventory_alternative_relationship_id,master_product_code
        ,replace_product_code,replace_quantity,replace_date,priority,replace_type,replace_start_date,replace_end_date,inventory_id
        ,inventory_type,work_order_code,finished_product_code
        ,remark,enabled,creator,create_time,modifier,modify_time,version_value

    </sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List"/>,master_product_name,master_product_color,master_product_thickness
        ,master_product_length,master_product_width,replace_product_name,finished_product_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.materialPlanVersionId != null and params.materialPlanVersionId != ''">
                and material_plan_version_id = #{params.materialPlanVersionId,jdbcType=VARCHAR}
            </if>
            <if test="params.materialPlanInventoryShiftId != null and params.materialPlanInventoryShiftId != ''">
                and material_plan_inventory_shift_id = #{params.materialPlanInventoryShiftId,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryAlternativeRelationshipId != null and params.inventoryAlternativeRelationshipId != ''">
                and inventory_alternative_relationship_id = #{params.inventoryAlternativeRelationshipId,jdbcType=VARCHAR}
            </if>
            <if test="params.masterProductCode != null and params.masterProductCode != ''">
                and master_product_code = #{params.masterProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.replaceProductCode != null and params.replaceProductCode != ''">
                and replace_product_code = #{params.replaceProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.replaceQuantity != null">
                and replace_quantity = #{params.replaceQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.replaceDate != null">
                and replace_date = #{params.replaceDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.priority != null">
                and priority = #{params.priority,jdbcType=INTEGER}
            </if>
            <if test="params.replaceType != null and params.replaceType != ''">
                and replace_type = #{params.replaceType,jdbcType=VARCHAR}
            </if>
            <if test="params.replaceStartDate != null">
                and replace_start_date = #{params.replaceStartDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.replaceEndDate != null">
                and replace_end_date = #{params.replaceEndDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.inventoryId != null and params.inventoryId != ''">
                and inventory_id = #{params.inventoryId,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryType != null and params.inventoryType != ''">
                and inventory_type = #{params.inventoryType,jdbcType=VARCHAR}
            </if>
            <if test="params.workOrderCode != null and params.workOrderCode != ''">
                and work_order_code = #{params.workOrderCode,jdbcType=VARCHAR}
            </if>
            <if test="params.finishedProductCode != null and params.finishedProductCode != ''">
                and finished_product_code = #{params.finishedProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_replace
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_replace
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_plan_replace
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_replace
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanReplacePO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_material_plan_replace(
        id,
        material_plan_version_id,
        material_plan_inventory_shift_id,
        inventory_alternative_relationship_id,
        master_product_code,
        replace_product_code,
        replace_quantity,
        replace_date,
        priority,
        replace_type,
        replace_start_date,
        replace_end_date,
        inventory_id,
        inventory_type,
        work_order_code,
        finished_product_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{materialPlanVersionId,jdbcType=VARCHAR},
        #{materialPlanInventoryShiftId,jdbcType=VARCHAR},
        #{inventoryAlternativeRelationshipId,jdbcType=VARCHAR},
        #{masterProductCode,jdbcType=VARCHAR},
        #{replaceProductCode,jdbcType=VARCHAR},
        #{replaceQuantity,jdbcType=VARCHAR},
        #{replaceDate,jdbcType=TIMESTAMP},
        #{priority,jdbcType=INTEGER},
        #{replaceType,jdbcType=VARCHAR},
        #{replaceStartDate,jdbcType=TIMESTAMP},
        #{replaceEndDate,jdbcType=TIMESTAMP},
        #{inventoryId,jdbcType=VARCHAR},
        #{inventoryType,jdbcType=VARCHAR},
        #{workOrderCode,jdbcType=VARCHAR},
        #{finishedProductCode,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanReplacePO">
        insert into mrp_material_plan_replace(id,
                                              material_plan_version_id,
                                              material_plan_inventory_shift_id,
                                              inventory_alternative_relationship_id,
                                              master_product_code,
                                              replace_product_code,
                                              replace_quantity,
                                              replace_date,
                                              priority,
                                              replace_type,
                                              replace_start_date,
                                              replace_end_date,
                                              inventory_id,
                                              inventory_type,
                                              work_order_code,
                                              finished_product_code,
                                              remark,
                                              enabled,
                                              creator,
                                              create_time,
                                              modifier,
                                              modify_time,
                                              version_value)
        values (#{id,jdbcType=VARCHAR},
                #{materialPlanVersionId,jdbcType=VARCHAR},
                #{materialPlanInventoryShiftId,jdbcType=VARCHAR},
                #{inventoryAlternativeRelationshipId,jdbcType=VARCHAR},
                #{masterProductCode,jdbcType=VARCHAR},
                #{replaceProductCode,jdbcType=VARCHAR},
                #{replaceQuantity,jdbcType=VARCHAR},
                #{replaceDate,jdbcType=TIMESTAMP},
                #{priority,jdbcType=INTEGER},
                #{replaceType,jdbcType=VARCHAR},
                #{replaceStartDate,jdbcType=TIMESTAMP},
                #{replaceEndDate,jdbcType=TIMESTAMP},
                #{inventoryId,jdbcType=VARCHAR},
                #{inventoryType,jdbcType=VARCHAR},
                #{workOrderCode,jdbcType=VARCHAR},
                #{finishedProductCode,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_plan_replace(
        id,
        material_plan_version_id,
        material_plan_inventory_shift_id,
        inventory_alternative_relationship_id,
        master_product_code,
        replace_product_code,
        replace_quantity,
        replace_date,
        priority,
        replace_type,
        replace_start_date,
        replace_end_date,
        inventory_id,
        inventory_type,
        work_order_code,
        finished_product_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.materialPlanVersionId,jdbcType=VARCHAR},
            #{entity.materialPlanInventoryShiftId,jdbcType=VARCHAR},
            #{entity.inventoryAlternativeRelationshipId,jdbcType=VARCHAR},
            #{entity.masterProductCode,jdbcType=VARCHAR},
            #{entity.replaceProductCode,jdbcType=VARCHAR},
            #{entity.replaceQuantity,jdbcType=VARCHAR},
            #{entity.replaceDate,jdbcType=TIMESTAMP},
            #{entity.priority,jdbcType=INTEGER},
            #{entity.replaceType,jdbcType=VARCHAR},
            #{entity.replaceStartDate,jdbcType=TIMESTAMP},
            #{entity.replaceEndDate,jdbcType=TIMESTAMP},
            #{entity.inventoryId,jdbcType=VARCHAR},
            #{entity.inventoryType,jdbcType=VARCHAR},
            #{entity.workOrderCode,jdbcType=VARCHAR},
            #{entity.finishedProductCode,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_material_plan_replace(
        id,
        material_plan_version_id,
        material_plan_inventory_shift_id,
        inventory_alternative_relationship_id,
        master_product_code,
        replace_product_code,
        replace_quantity,
        replace_date,
        priority,
        replace_type,
        replace_start_date,
        replace_end_date,
        inventory_id,
        inventory_type,
        work_order_code,
        finished_product_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.materialPlanVersionId,jdbcType=VARCHAR},
            #{entity.materialPlanInventoryShiftId,jdbcType=VARCHAR},
            #{entity.inventoryAlternativeRelationshipId,jdbcType=VARCHAR},
            #{entity.masterProductCode,jdbcType=VARCHAR},
            #{entity.replaceProductCode,jdbcType=VARCHAR},
            #{entity.replaceQuantity,jdbcType=VARCHAR},
            #{entity.replaceDate,jdbcType=TIMESTAMP},
            #{entity.priority,jdbcType=INTEGER},
            #{entity.replaceType,jdbcType=VARCHAR},
            #{entity.replaceStartDate,jdbcType=TIMESTAMP},
            #{entity.replaceEndDate,jdbcType=TIMESTAMP},
            #{entity.inventoryId,jdbcType=VARCHAR},
            #{entity.inventoryType,jdbcType=VARCHAR},
            #{entity.workOrderCode,jdbcType=VARCHAR},
            #{entity.finishedProductCode,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanReplacePO">
        update mrp_material_plan_replace
        set material_plan_version_id              = #{materialPlanVersionId,jdbcType=VARCHAR},
            material_plan_inventory_shift_id      = #{materialPlanInventoryShiftId,jdbcType=VARCHAR},
            inventory_alternative_relationship_id = #{inventoryAlternativeRelationshipId,jdbcType=VARCHAR},
            master_product_code                   = #{masterProductCode,jdbcType=VARCHAR},
            replace_product_code                  = #{replaceProductCode,jdbcType=VARCHAR},
            replace_quantity                      = #{replaceQuantity,jdbcType=VARCHAR},
            replace_date                          = #{replaceDate,jdbcType=TIMESTAMP},
            priority                              = #{priority,jdbcType=INTEGER},
            replace_type                          = #{replaceType,jdbcType=VARCHAR},
            replace_start_date                    = #{replaceStartDate,jdbcType=TIMESTAMP},
            replace_end_date                      = #{replaceEndDate,jdbcType=TIMESTAMP},
            inventory_id                          = #{inventoryId,jdbcType=VARCHAR},
            inventory_type                        = #{inventoryType,jdbcType=VARCHAR},
            work_order_code                       = #{workOrderCode,jdbcType=VARCHAR},
            finished_product_code                 = #{finishedProductCode,jdbcType=VARCHAR},
            remark                                = #{remark,jdbcType=VARCHAR},
            enabled                               = #{enabled,jdbcType=VARCHAR},
            modifier                              = #{modifier,jdbcType=VARCHAR},
            modify_time                           = #{modifyTime,jdbcType=TIMESTAMP},
            version_value                         = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanReplacePO">
        update mrp_material_plan_replace
        <set>
            <if test="item.materialPlanVersionId != null and item.materialPlanVersionId != ''">
                material_plan_version_id = #{item.materialPlanVersionId,jdbcType=VARCHAR},
            </if>
            <if test="item.materialPlanInventoryShiftId != null and item.materialPlanInventoryShiftId != ''">
                material_plan_inventory_shift_id = #{item.materialPlanInventoryShiftId,jdbcType=VARCHAR},
            </if>
            <if test="item.inventoryAlternativeRelationshipId != null and item.inventoryAlternativeRelationshipId != ''">
                inventory_alternative_relationship_id = #{item.inventoryAlternativeRelationshipId,jdbcType=VARCHAR},
            </if>
            <if test="item.masterProductCode != null and item.masterProductCode != ''">
                master_product_code = #{item.masterProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.replaceProductCode != null and item.replaceProductCode != ''">
                replace_product_code = #{item.replaceProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.replaceQuantity != null">
                replace_quantity = #{item.replaceQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.replaceDate != null">
                replace_date = #{item.replaceDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.priority != null">
                priority = #{item.priority,jdbcType=INTEGER},
            </if>
            <if test="item.replaceType != null and item.replaceType != ''">
                replace_type = #{item.replaceType,jdbcType=VARCHAR},
            </if>
            <if test="item.replaceStartDate != null">
                replace_start_date = #{item.replaceStartDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.replaceEndDate != null">
                replace_end_date = #{item.replaceEndDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.inventoryId != null and item.inventoryId != ''">
                inventory_id = #{item.inventoryId,jdbcType=VARCHAR},
            </if>
            <if test="item.inventoryType != null and item.inventoryType != ''">
                inventory_type = #{item.inventoryType,jdbcType=VARCHAR},
            </if>
            <if test="item.workOrderCode != null and item.workOrderCode != ''">
                work_order_code = #{item.workOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="item.finishedProductCode != null and item.finishedProductCode != ''">
                finished_product_code = #{item.finishedProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_plan_replace
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="material_plan_version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialPlanVersionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_plan_inventory_shift_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialPlanInventoryShiftId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="inventory_alternative_relationship_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inventoryAlternativeRelationshipId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="master_product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.masterProductCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="replace_product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.replaceProductCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="replace_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.replaceQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="replace_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.replaceDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="priority = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.priority,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="replace_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.replaceType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="replace_start_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.replaceStartDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="replace_end_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.replaceEndDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="inventory_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inventoryId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="inventory_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inventoryType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="work_order_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.workOrderCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="finished_product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.finishedProductCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_material_plan_replace
            <set>
                <if test="item.materialPlanVersionId != null and item.materialPlanVersionId != ''">
                    material_plan_version_id = #{item.materialPlanVersionId,jdbcType=VARCHAR},
                </if>
                <if test="item.materialPlanInventoryShiftId != null and item.materialPlanInventoryShiftId != ''">
                    material_plan_inventory_shift_id = #{item.materialPlanInventoryShiftId,jdbcType=VARCHAR},
                </if>
                <if test="item.inventoryAlternativeRelationshipId != null and item.inventoryAlternativeRelationshipId != ''">
                    inventory_alternative_relationship_id = #{item.inventoryAlternativeRelationshipId,jdbcType=VARCHAR},
                </if>
                <if test="item.masterProductCode != null and item.masterProductCode != ''">
                    master_product_code = #{item.masterProductCode,jdbcType=VARCHAR},
                </if>
                <if test="item.replaceProductCode != null and item.replaceProductCode != ''">
                    replace_product_code = #{item.replaceProductCode,jdbcType=VARCHAR},
                </if>
                <if test="item.replaceQuantity != null">
                    replace_quantity = #{item.replaceQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.replaceDate != null">
                    replace_date = #{item.replaceDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.priority != null">
                    priority = #{item.priority,jdbcType=INTEGER},
                </if>
                <if test="item.replaceType != null and item.replaceType != ''">
                    replace_type = #{item.replaceType,jdbcType=VARCHAR},
                </if>
                <if test="item.replaceStartDate != null">
                    replace_start_date = #{item.replaceStartDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.replaceEndDate != null">
                    replace_end_date = #{item.replaceEndDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.inventoryId != null and item.inventoryId != ''">
                    inventory_id = #{item.inventoryId,jdbcType=VARCHAR},
                </if>
                <if test="item.inventoryType != null and item.inventoryType != ''">
                    inventory_type = #{item.inventoryType,jdbcType=VARCHAR},
                </if>
                <if test="item.workOrderCode != null and item.workOrderCode != ''">
                    work_order_code = #{item.workOrderCode,jdbcType=VARCHAR},
                </if>
                <if test="item.finishedProductCode != null and item.finishedProductCode != ''">
                    finished_product_code = #{item.finishedProductCode,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_material_plan_replace
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_plan_replace where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteByVersionId">
        delete
        from mrp_material_plan_replace
        where material_plan_version_id = #{materialPlanVersionId,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteByProductCodes">
        delete
        from mrp_material_plan_replace
        where master_product_code in
        <foreach collection="productCodeList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        or replace_product_code in
        <foreach collection="productCodeList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <select id="selectByProductCodes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_replace
        where master_product_code in
        <foreach collection="productCodeList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        or replace_product_code in
        <foreach collection="productCodeList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_plan_replace
        <include refid="Base_Where_Condition"/>
    </select>


    <delete id="deleteAll">
        delete
        from mrp_material_plan_replace
    </delete>

    <select id="convertProductCode"
            resultMap="VOResultMap">
        SELECT DISTINCT CONCAT(SUBSTRING(product_code, 1, 2), '*', SUBSTRING(product_code, 4)) master_product_code,
                        product_code as                                                        replace_product_code
        FROM mds_product_stock_point
        WHERE product_classify = 'RA.A'
          AND product_code NOT LIKE '%DEL%'
    </select>


</mapper>
