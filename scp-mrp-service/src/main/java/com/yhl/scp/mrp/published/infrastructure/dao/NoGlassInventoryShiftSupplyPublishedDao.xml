<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.published.infrastructure.dao.NoGlassInventoryShiftSupplyPublishedDao">
    <resultMap id="BaseResultMap"
               type="com.yhl.scp.mrp.published.infrastructure.po.NoGlassInventoryShiftSupplyPublishedPO">
        <!--@Table mrp_no_glass_inventory_shift_supply_published-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="material_plan_published_version_id" jdbcType="VARCHAR"
                property="materialPlanPublishedVersionId"/>
        <result column="no_glass_inventory_shift_published_id" jdbcType="VARCHAR" property="noGlassInventoryShiftPublishedId"/>
        <result column="materilal_enquiry_track" jdbcType="VARCHAR" property="materilalEnquiryTrack"/>
        <result column="supply_quantity" jdbcType="VARCHAR" property="supplyQuantity"/>
        <result column="supply_type" jdbcType="VARCHAR" property="supplyType"/>
        <result column="purchase_order_code" jdbcType="VARCHAR" property="purchaseOrderCode"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.published.vo.NoGlassInventoryShiftSupplyPublishedVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,product_code,material_plan_published_version_id,no_glass_inventory_shift_published_id,materilal_enquiry_track,supply_quantity,supply_type,purchase_order_code,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.materialPlanPublishedVersionId != null and params.materialPlanPublishedVersionId != ''">
                and material_plan_published_version_id = #{params.materialPlanPublishedVersionId,jdbcType=VARCHAR}
            </if>
            <if test="params.noGlassInventoryShiftPublishedId != null and params.noGlassInventoryShiftPublishedId != ''">
                and no_glass_inventory_shift_published_id = #{params.noGlassInventoryShiftPublishedId,jdbcType=VARCHAR}
            </if>
            <if test="params.materilalEnquiryTrack != null and params.materilalEnquiryTrack != ''">
                and materilal_enquiry_track = #{params.materilalEnquiryTrack,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyQuantity != null">
                and supply_quantity = #{params.supplyQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyType != null and params.supplyType != ''">
                and supply_type = #{params.supplyType,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseOrderCode != null and params.purchaseOrderCode != ''">
                and purchase_order_code = #{params.purchaseOrderCode,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_no_glass_inventory_shift_supply_published
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_no_glass_inventory_shift_supply_published
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mrp_no_glass_inventory_shift_supply_published
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_no_glass_inventory_shift_supply_published
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="noGlassSupplyPublishedBatch">
        {call SaveNoGlassSupplyPublished(#{startId,jdbcType=VARCHAR}, #{date,jdbcType=TIMESTAMP},#{userId,jdbcType=VARCHAR},#{productCodeList,jdbcType=VARCHAR})}
    </select>

    <select id="selectByInventoryShiftIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_no_glass_inventory_shift_supply_published
        where no_glass_inventory_shift_published_id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!-- 新增 -->
    <insert id="insert"
            parameterType="com.yhl.scp.mrp.published.infrastructure.po.NoGlassInventoryShiftSupplyPublishedPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_no_glass_inventory_shift_supply_published(
        id,
        product_code,
        material_plan_published_version_id,
        no_glass_inventory_shift_published_id,
        materilal_enquiry_track,
        supply_quantity,
        supply_type,
        purchase_order_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{materialPlanPublishedVersionId,jdbcType=VARCHAR},
        #{noGlassInventoryShiftPublishedId,jdbcType=VARCHAR},
        #{materilalEnquiryTrack,jdbcType=VARCHAR},
        #{supplyQuantity,jdbcType=VARCHAR},
        #{supplyType,jdbcType=VARCHAR},
        #{purchaseOrderCode,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.published.infrastructure.po.NoGlassInventoryShiftSupplyPublishedPO">
        insert into mrp_no_glass_inventory_shift_supply_published(id,
                                                                  product_code,
                                                                  material_plan_published_version_id,
                                                                  no_glass_inventory_shift_published_id,
                                                                  materilal_enquiry_track,
                                                                  supply_quantity,
                                                                  supply_type,
                                                                  purchase_order_code,
                                                                  remark,
                                                                  enabled,
                                                                  creator,
                                                                  create_time,
                                                                  modifier,
                                                                  modify_time,
                                                                  version_value)
        values (#{id,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{materialPlanPublishedVersionId,jdbcType=VARCHAR},
                #{noGlassInventoryShiftPublishedId,jdbcType=VARCHAR},
                #{materilalEnquiryTrack,jdbcType=VARCHAR},
                #{supplyQuantity,jdbcType=VARCHAR},
                #{supplyType,jdbcType=VARCHAR},
                #{purchaseOrderCode,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_no_glass_inventory_shift_supply_published(
        id,
        product_code,
        material_plan_published_version_id,
        no_glass_inventory_shift_published_id,
        materilal_enquiry_track,
        supply_quantity,
        supply_type,
        purchase_order_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.materialPlanPublishedVersionId,jdbcType=VARCHAR},
            #{entity.noGlassInventoryShiftPublishedId,jdbcType=VARCHAR},
            #{entity.materilalEnquiryTrack,jdbcType=VARCHAR},
            #{entity.supplyQuantity,jdbcType=VARCHAR},
            #{entity.supplyType,jdbcType=VARCHAR},
            #{entity.purchaseOrderCode,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_no_glass_inventory_shift_supply_published(
        id,
        product_code,
        material_plan_published_version_id,
        no_glass_inventory_shift_published_id,
        materilal_enquiry_track,
        supply_quantity,
        supply_type,
        purchase_order_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.materialPlanPublishedVersionId,jdbcType=VARCHAR},
            #{entity.noGlassInventoryShiftPublishedId,jdbcType=VARCHAR},
            #{entity.materilalEnquiryTrack,jdbcType=VARCHAR},
            #{entity.supplyQuantity,jdbcType=VARCHAR},
            #{entity.supplyType,jdbcType=VARCHAR},
            #{entity.purchaseOrderCode,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update"
            parameterType="com.yhl.scp.mrp.published.infrastructure.po.NoGlassInventoryShiftSupplyPublishedPO">
        update mrp_no_glass_inventory_shift_supply_published
        set product_code                       = #{productCode,jdbcType=VARCHAR},
            material_plan_published_version_id = #{materialPlanPublishedVersionId,jdbcType=VARCHAR},
            no_glass_inventory_shift_published_id        = #{noGlassInventoryShiftPublishedId,jdbcType=VARCHAR},
            materilal_enquiry_track            = #{materilalEnquiryTrack,jdbcType=VARCHAR},
            supply_quantity                    = #{supplyQuantity,jdbcType=VARCHAR},
            supply_type                        = #{supplyType,jdbcType=VARCHAR},
            purchase_order_code                = #{purchaseOrderCode,jdbcType=VARCHAR},
            remark                             = #{remark,jdbcType=VARCHAR},
            enabled                            = #{enabled,jdbcType=VARCHAR},
            modifier                           = #{modifier,jdbcType=VARCHAR},
            modify_time                        = #{modifyTime,jdbcType=TIMESTAMP},
            version_value      = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.published.infrastructure.po.NoGlassInventoryShiftSupplyPublishedPO">
        update mrp_no_glass_inventory_shift_supply_published
        <set>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.materialPlanPublishedVersionId != null and item.materialPlanPublishedVersionId != ''">
                material_plan_published_version_id = #{item.materialPlanPublishedVersionId,jdbcType=VARCHAR},
            </if>
            <if test="item.noGlassInventoryShiftPublishedId != null and item.noGlassInventoryShiftPublishedId != ''">
                no_glass_inventory_shift_published_id = #{item.noGlassInventoryShiftPublishedId,jdbcType=VARCHAR},
            </if>
            <if test="item.materilalEnquiryTrack != null and item.materilalEnquiryTrack != ''">
                materilal_enquiry_track = #{item.materilalEnquiryTrack,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyQuantity != null">
                supply_quantity = #{item.supplyQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyType != null and item.supplyType != ''">
                supply_type = #{item.supplyType,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseOrderCode != null and item.purchaseOrderCode != ''">
                purchase_order_code = #{item.purchaseOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_no_glass_inventory_shift_supply_published
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_plan_published_version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialPlanPublishedVersionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="no_glass_inventory_shift_published_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.noGlassInventoryShiftPublishedId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="materilal_enquiry_track = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materilalEnquiryTrack,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_order_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.purchaseOrderCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_no_glass_inventory_shift_supply_published
            <set>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.materialPlanPublishedVersionId != null and item.materialPlanPublishedVersionId != ''">
                    material_plan_published_version_id = #{item.materialPlanPublishedVersionId,jdbcType=VARCHAR},
                </if>
                <if test="item.noGlassInventoryShiftPublishedId != null and item.noGlassInventoryShiftPublishedId != ''">
                    no_glass_inventory_shift_published_id = #{item.noGlassInventoryShiftPublishedId,jdbcType=VARCHAR},
                </if>
                <if test="item.materilalEnquiryTrack != null and item.materilalEnquiryTrack != ''">
                    materilal_enquiry_track = #{item.materilalEnquiryTrack,jdbcType=VARCHAR},
                </if>
                <if test="item.supplyQuantity != null">
                    supply_quantity = #{item.supplyQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.supplyType != null and item.supplyType != ''">
                    supply_type = #{item.supplyType,jdbcType=VARCHAR},
                </if>
                <if test="item.purchaseOrderCode != null and item.purchaseOrderCode != ''">
                    purchase_order_code = #{item.purchaseOrderCode,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_no_glass_inventory_shift_supply_published
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_no_glass_inventory_shift_supply_published where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteByDetailIds">
        delete from mrp_no_glass_inventory_shift_supply_published where no_glass_inventory_shift_published_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
