<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.material.plan.infrastructure.dao.MaterialPlanVersionDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanVersionPO">
        <!--@Table mrp_material_plan_version-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="version_code" jdbcType="VARCHAR" property="versionCode"/>
        <result column="master_plan_version_code" jdbcType="VARCHAR" property="masterPlanVersionCode"/>
        <result column="publish_status" jdbcType="VARCHAR" property="publishStatus"/>
        <result column="version_type" jdbcType="VARCHAR" property="versionType"/>
        <result column="material_type" jdbcType="VARCHAR" property="materialType"/>
        <result column="publisher" jdbcType="VARCHAR" property="publisher"/>
        <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.material.plan.vo.MaterialPlanVersionVO">
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,version_code,master_plan_version_code,publish_status,version_type,material_type,publisher,publish_time
        ,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.versionCode != null and params.versionCode != ''">
                and version_code = #{params.versionCode,jdbcType=VARCHAR}
            </if>
            <if test="params.masterPlanVersionCode != null and params.masterPlanVersionCode != ''">
                and master_plan_version_code = #{params.masterPlanVersionCode,jdbcType=VARCHAR}
            </if>
            <if test="params.publishStatus != null and params.publishStatus != ''">
                and publish_status = #{params.publishStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.versionType != null and params.versionType != ''">
                and version_type = #{params.versionType,jdbcType=VARCHAR}
            </if>
            <if test="params.materialType != null and params.materialType != ''">
                and material_type = #{params.materialType,jdbcType=VARCHAR}
            </if>
            <if test="params.publisher != null and params.publisher != ''">
                and publisher = #{params.publisher,jdbcType=VARCHAR}
            </if>
            <if test="params.publishTime != null and params.publishTime != ''">
                and publish_time = #{params.publishTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_version
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_version
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_plan_version
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_version
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_plan_version
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectLatestVersion" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_version
        where material_type = #{materialType,jdbcType=VARCHAR}
        <if test="userId != null and userId != ''">
        and creator = #{userId,jdbcType=VARCHAR}
        </if>
        order by create_time desc limit 1
    </select>
    <select id="selectLatestVersionId" resultType="java.lang.String">
        select id
        from mrp_material_plan_version
        where material_type = 'GLASS'
        order by create_time desc limit 1
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanVersionPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_material_plan_version(
        id,
        version_code,
        master_plan_version_code,
        publish_status,
        version_type,
        material_type,
        publisher,
        publish_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{versionCode,jdbcType=VARCHAR},
        #{masterPlanVersionCode,jdbcType=VARCHAR},
        #{publishStatus,jdbcType=VARCHAR},
        #{versionType,jdbcType=VARCHAR},
        #{materialType,jdbcType=VARCHAR},
        #{publisher,jdbcType=VARCHAR},
        #{publishTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanVersionPO">
        insert into mrp_material_plan_version(id,
                                              version_code,
                                              master_plan_version_code,
                                              publish_status,
                                              version_type,
                                              material_type,
                                              publisher,
                                              publish_time,
                                              remark,
                                              enabled,
                                              creator,
                                              create_time,
                                              modifier,
                                              modify_time,
                                              version_value)
        values (#{id,jdbcType=VARCHAR},
                #{versionCode,jdbcType=VARCHAR},
                #{masterPlanVersionCode,jdbcType=VARCHAR},
                #{publishStatus,jdbcType=VARCHAR},
                #{versionType,jdbcType=VARCHAR},
                #{materialType,jdbcType=VARCHAR},
                #{publisher,jdbcType=VARCHAR},
                #{publishTime,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_plan_version(
        id,
        version_code,
        master_plan_version_code,
        publish_status,
        version_type,
        material_type,
        publisher,
        publish_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.versionCode,jdbcType=VARCHAR},
            #{entity.masterPlanVersionCode,jdbcType=VARCHAR},
            #{entity.publishStatus,jdbcType=VARCHAR},
            #{entity.versionType,jdbcType=VARCHAR},
            #{entity.materialType,jdbcType=VARCHAR},
            #{entity.publisher,jdbcType=VARCHAR},
            #{entity.publishTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_material_plan_version(
        id,
        version_code,
        master_plan_version_code,
        publish_status,
        version_type,
        material_type,
        publisher,
        publish_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.versionCode,jdbcType=VARCHAR},
            #{entity.masterPlanVersionCode,jdbcType=VARCHAR},
            #{entity.publishStatus,jdbcType=VARCHAR},
            #{entity.versionType,jdbcType=VARCHAR},
            #{entity.materialType,jdbcType=VARCHAR},
            #{entity.publisher,jdbcType=VARCHAR},
            #{entity.publishTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanVersionPO">
        update mrp_material_plan_version
        set version_code             = #{versionCode,jdbcType=VARCHAR},
            master_plan_version_code = #{masterPlanVersionCode,jdbcType=VARCHAR},
            publish_status           = #{publishStatus,jdbcType=VARCHAR},
            version_type             = #{versionType,jdbcType=VARCHAR},
            material_type            = #{materialType,jdbcType=VARCHAR},
            publisher                = #{publisher,jdbcType=VARCHAR},
            publish_time             = #{publishTime,jdbcType=TIMESTAMP},
            remark                   = #{remark,jdbcType=VARCHAR},
            enabled                  = #{enabled,jdbcType=VARCHAR},
            modifier                 = #{modifier,jdbcType=VARCHAR},
            modify_time              = #{modifyTime,jdbcType=TIMESTAMP},
            version_value            = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanVersionPO">
        update mrp_material_plan_version
        <set>
            <if test="item.versionCode != null and item.versionCode != ''">
                version_code = #{item.versionCode,jdbcType=VARCHAR},
            </if>
            <if test="item.masterPlanVersionCode != null and item.masterPlanVersionCode != ''">
                master_plan_version_code = #{item.masterPlanVersionCode,jdbcType=VARCHAR},
            </if>
            <if test="item.publishStatus != null and item.publishStatus != ''">
                publish_status = #{item.publishStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.versionType != null and item.versionType != ''">
                version_type = #{item.versionType,jdbcType=VARCHAR},
            </if>
            <if test="item.materialType != null and item.materialType != ''">
                material_type = #{item.materialType,jdbcType=VARCHAR},
            </if>
            <if test="item.publisher != null and item.publisher != ''">
                publisher = #{item.publisher,jdbcType=VARCHAR},
            </if>
            <if test="item.publishTime != null and item.publishTime != ''">
                publish_time = #{item.publishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_plan_version
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="version_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="master_plan_version_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.masterPlanVersionCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="publish_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.publishStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="publisher = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.publisher,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="publish_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.publishTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_material_plan_version
            <set>
                <if test="item.versionCode != null and item.versionCode != ''">
                    version_code = #{item.versionCode,jdbcType=VARCHAR},
                </if>
                <if test="item.masterPlanVersionCode != null and item.masterPlanVersionCode != ''">
                    master_plan_version_code = #{item.masterPlanVersionCode,jdbcType=VARCHAR},
                </if>
                <if test="item.publishStatus != null and item.publishStatus != ''">
                    publish_status = #{item.publishStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.versionType != null and item.versionType != ''">
                    version_type = #{item.versionType,jdbcType=VARCHAR},
                </if>
                <if test="item.materialType != null and item.materialType != ''">
                    material_type = #{item.materialType,jdbcType=VARCHAR},
                </if>
                <if test="item.publisher != null and item.publisher != ''">
                    publisher = #{item.publisher,jdbcType=VARCHAR},
                </if>
                <if test="item.publishTime != null">
                    publish_time = #{item.publishTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_material_plan_version
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_plan_version where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
