package com.yhl.scp.mrp.compare.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.compare.dto.ProductChangeCompareDTO;
import com.yhl.scp.mrp.compare.service.ProductChangeCompareService;
import com.yhl.scp.mrp.compare.vo.ProductChangeCompareVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>ProductChangeCompare2Controller</code>
 * <p>
 * 材料计划变动提醒-产品对比控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-18 09:56:28
 */
@Slf4j
@Api(tags = "材料计划变动提醒-产品对比控制器")
@RestController
@RequestMapping("productChangeCompare")
public class ProductChangeCompareController extends BaseController {

    @Resource
    private ProductChangeCompareService productChangeCompareService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<ProductChangeCompareVO>> page() {
        List<ProductChangeCompareVO> productChangeCompare2List = productChangeCompareService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<ProductChangeCompareVO> pageInfo = new PageInfo<>(productChangeCompare2List);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody ProductChangeCompareDTO productChangeCompareDTO) {
        return productChangeCompareService.doCreate(productChangeCompareDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody ProductChangeCompareDTO productChangeCompareDTO) {
        return productChangeCompareService.doUpdate(productChangeCompareDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        productChangeCompareService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<ProductChangeCompareVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, productChangeCompareService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "对比计算")
    @GetMapping(value = "compareCalc")
    public void compareCalc() {
        productChangeCompareService.doCompareCalc();
    }

    @ApiOperation(value = "mrp计算")
    @GetMapping(value = "mrpCalc")
    public void mrpCalc() {
        productChangeCompareService.doMrpCalc();
    }

}
