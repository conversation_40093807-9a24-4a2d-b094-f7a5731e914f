<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.inventory.infrastructure.dao.GlassSafetyInventoryDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.inventory.infrastructure.po.GlassSafetyInventoryPO">
        <!--@Table mrp_glass_safety_inventory-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
        <result column="color" jdbcType="VARCHAR" property="color"/>
        <result column="thickness" jdbcType="VARCHAR" property="thickness"/>
        <result column="min_safety_inventory_days" jdbcType="VARCHAR" property="minSafetyInventoryDays"/>
        <result column="standard_safety_inventory_days" jdbcType="VARCHAR" property="standardSafetyInventoryDays"/>
        <result column="max_safety_inventory_days" jdbcType="VARCHAR" property="maxSafetyInventoryDays"/>
        <result column="port_inventory_days" jdbcType="VARCHAR" property="portInventoryDays"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.inventory.vo.GlassSafetyInventoryVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
id,stock_point_code,stock_point_name,color,thickness,min_safety_inventory_days,standard_safety_inventory_days,max_safety_inventory_days,port_inventory_days,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointName != null and params.stockPointName != ''">
                and stock_point_name = #{params.stockPointName,jdbcType=VARCHAR}
            </if>
            <if test="params.color != null and params.color != ''">
                and color = #{params.color,jdbcType=VARCHAR}
            </if>
            <if test="params.thickness != null and params.thickness != ''">
                and thickness = #{params.thickness,jdbcType=VARCHAR}
            </if>
            <if test="params.minSafetyInventoryDays != null">
                and min_safety_inventory_days = #{params.minSafetyInventoryDays,jdbcType=VARCHAR}
            </if>
            <if test="params.standardSafetyInventoryDays != null">
                and standard_safety_inventory_days = #{params.standardSafetyInventoryDays,jdbcType=VARCHAR}
            </if>
            <if test="params.maxSafetyInventoryDays != null">
                and max_safety_inventory_days = #{params.maxSafetyInventoryDays,jdbcType=VARCHAR}
            </if>
            <if test="params.portInventoryDays != null">
                and port_inventory_days = #{params.portInventoryDays,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_glass_safety_inventory
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_glass_safety_inventory
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_mrp_glass_safety_inventory
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_glass_safety_inventory
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.GlassSafetyInventoryPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_glass_safety_inventory(
        id,
        stock_point_code,
        stock_point_name,
        color,
        thickness,
        min_safety_inventory_days,
        standard_safety_inventory_days,
        max_safety_inventory_days,
        port_inventory_days,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{stockPointName,jdbcType=VARCHAR},
        #{color,jdbcType=VARCHAR},
        #{thickness,jdbcType=VARCHAR},
        #{minSafetyInventoryDays,jdbcType=VARCHAR},
        #{standardSafetyInventoryDays,jdbcType=VARCHAR},
        #{maxSafetyInventoryDays,jdbcType=VARCHAR},
        #{portInventoryDays,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.GlassSafetyInventoryPO">
        insert into mrp_glass_safety_inventory(
        id,
        stock_point_code,
        stock_point_name,
        color,
        thickness,
        min_safety_inventory_days,
        standard_safety_inventory_days,
        max_safety_inventory_days,
        port_inventory_days,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{stockPointName,jdbcType=VARCHAR},
        #{color,jdbcType=VARCHAR},
        #{thickness,jdbcType=VARCHAR},
        #{minSafetyInventoryDays,jdbcType=VARCHAR},
        #{standardSafetyInventoryDays,jdbcType=VARCHAR},
        #{maxSafetyInventoryDays,jdbcType=VARCHAR},
        #{portInventoryDays,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_glass_safety_inventory(
        id,
        stock_point_code,
        stock_point_name,
        color,
        thickness,
        min_safety_inventory_days,
        standard_safety_inventory_days,
        max_safety_inventory_days,
        port_inventory_days,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.stockPointCode,jdbcType=VARCHAR},
        #{entity.stockPointName,jdbcType=VARCHAR},
        #{entity.color,jdbcType=VARCHAR},
        #{entity.thickness,jdbcType=VARCHAR},
        #{entity.minSafetyInventoryDays,jdbcType=VARCHAR},
        #{entity.standardSafetyInventoryDays,jdbcType=VARCHAR},
        #{entity.maxSafetyInventoryDays,jdbcType=VARCHAR},
        #{entity.portInventoryDays,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_glass_safety_inventory(
        id,
        stock_point_code,
        stock_point_name,
        color,
        thickness,
        min_safety_inventory_days,
        standard_safety_inventory_days,
        max_safety_inventory_days,
        port_inventory_days,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.stockPointCode,jdbcType=VARCHAR},
        #{entity.stockPointName,jdbcType=VARCHAR},
        #{entity.color,jdbcType=VARCHAR},
        #{entity.thickness,jdbcType=VARCHAR},
        #{entity.minSafetyInventoryDays,jdbcType=VARCHAR},
        #{entity.standardSafetyInventoryDays,jdbcType=VARCHAR},
        #{entity.maxSafetyInventoryDays,jdbcType=VARCHAR},
        #{entity.portInventoryDays,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.GlassSafetyInventoryPO">
        update mrp_glass_safety_inventory set
        stock_point_code = #{stockPointCode,jdbcType=VARCHAR},
        stock_point_name = #{stockPointName,jdbcType=VARCHAR},
        color = #{color,jdbcType=VARCHAR},
        thickness = #{thickness,jdbcType=VARCHAR},
        min_safety_inventory_days = #{minSafetyInventoryDays,jdbcType=VARCHAR},
        standard_safety_inventory_days = #{standardSafetyInventoryDays,jdbcType=VARCHAR},
        max_safety_inventory_days = #{maxSafetyInventoryDays,jdbcType=VARCHAR},
        port_inventory_days = #{portInventoryDays,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.GlassSafetyInventoryPO">
        update mrp_glass_safety_inventory
        <set>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointName != null and item.stockPointName != ''">
                stock_point_name = #{item.stockPointName,jdbcType=VARCHAR},
            </if>
            <if test="item.color != null and item.color != ''">
                color = #{item.color,jdbcType=VARCHAR},
            </if>
            <if test="item.thickness != null and item.thickness != ''">
                thickness = #{item.thickness,jdbcType=VARCHAR},
            </if>
            <if test="item.minSafetyInventoryDays != null">
                min_safety_inventory_days = #{item.minSafetyInventoryDays,jdbcType=VARCHAR},
            </if>
            <if test="item.standardSafetyInventoryDays != null">
                standard_safety_inventory_days = #{item.standardSafetyInventoryDays,jdbcType=VARCHAR},
            </if>
            <if test="item.maxSafetyInventoryDays != null">
                max_safety_inventory_days = #{item.maxSafetyInventoryDays,jdbcType=VARCHAR},
            </if>
            <if test="item.portInventoryDays != null">
                port_inventory_days = #{item.portInventoryDays,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_glass_safety_inventory
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="color = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.color,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="thickness = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.thickness,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="min_safety_inventory_days = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.minSafetyInventoryDays,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="standard_safety_inventory_days = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.standardSafetyInventoryDays,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="max_safety_inventory_days = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxSafetyInventoryDays,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="port_inventory_days = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.portInventoryDays,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mrp_glass_safety_inventory 
        <set>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointName != null and item.stockPointName != ''">
                stock_point_name = #{item.stockPointName,jdbcType=VARCHAR},
            </if>
            <if test="item.color != null and item.color != ''">
                color = #{item.color,jdbcType=VARCHAR},
            </if>
            <if test="item.thickness != null and item.thickness != ''">
                thickness = #{item.thickness,jdbcType=VARCHAR},
            </if>
            <if test="item.minSafetyInventoryDays != null">
                min_safety_inventory_days = #{item.minSafetyInventoryDays,jdbcType=VARCHAR},
            </if>
            <if test="item.standardSafetyInventoryDays != null">
                standard_safety_inventory_days = #{item.standardSafetyInventoryDays,jdbcType=VARCHAR},
            </if>
            <if test="item.maxSafetyInventoryDays != null">
                max_safety_inventory_days = #{item.maxSafetyInventoryDays,jdbcType=VARCHAR},
            </if>
            <if test="item.portInventoryDays != null">
                port_inventory_days = #{item.portInventoryDays,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mrp_glass_safety_inventory where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_glass_safety_inventory where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
