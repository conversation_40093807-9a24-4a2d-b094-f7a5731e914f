<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.material.distribute.infrastructure.dao.MaterialSupplyDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.material.distribute.infrastructure.po.MaterialSupplyPO">
        <!--@Table mrp_material_supply-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="supply_code" jdbcType="VARCHAR" property="supplyCode"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_type" jdbcType="VARCHAR" property="productType"/>
        <result column="stock_point_id" jdbcType="VARCHAR" property="stockPointId"/>
        <result column="supply_time" jdbcType="TIMESTAMP" property="supplyTime"/>
        <result column="quantity" jdbcType="VARCHAR" property="quantity"/>
        <result column="unfulfilled_quantity" jdbcType="VARCHAR" property="unfulfilledQuantity"/>
        <result column="supply_order_id" jdbcType="VARCHAR" property="supplyOrderId"/>
        <result column="supply_type" jdbcType="VARCHAR" property="supplyType"/>
        <result column="fulfillment_status" jdbcType="VARCHAR" property="fulfillmentStatus"/>
        <result column="counting_unit_id" jdbcType="VARCHAR" property="countingUnitId"/>
        <result column="appoint_demand_type" jdbcType="VARCHAR" property="appointDemandType"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="fixed" jdbcType="VARCHAR" property="fixed"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.material.distribute.vo.MaterialSupplyVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,supply_code,product_id,product_code,product_type,stock_point_id,supply_time,quantity,unfulfilled_quantity,supply_order_id,supply_type,fulfillment_status,counting_unit_id,appoint_demand_type,remark,enabled,creator,create_time,modifier,modify_time,fixed,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyCode != null and params.supplyCode != ''">
                and supply_code = #{params.supplyCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productId != null and params.productId != ''">
                and product_id = #{params.productId,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productType != null and params.productType != ''">
                and product_type = #{params.productType,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointId != null and params.stockPointId != ''">
                and stock_point_id = #{params.stockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyTime != null">
                and supply_time = #{params.supplyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.quantity != null">
                and quantity = #{params.quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.unfulfilledQuantity != null">
                and unfulfilled_quantity = #{params.unfulfilledQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyOrderId != null and params.supplyOrderId != ''">
                and supply_order_id = #{params.supplyOrderId,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyType != null and params.supplyType != ''">
                and supply_type = #{params.supplyType,jdbcType=VARCHAR}
            </if>
            <if test="params.fulfillmentStatus != null and params.fulfillmentStatus != ''">
                and fulfillment_status = #{params.fulfillmentStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.countingUnitId != null and params.countingUnitId != ''">
                and counting_unit_id = #{params.countingUnitId,jdbcType=VARCHAR}
            </if>
            <if test="params.appointDemandType != null and params.appointDemandType != ''">
                and appoint_demand_type = #{params.appointDemandType,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.fixed != null and params.fixed != ''">
                and fixed = #{params.fixed,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_supply
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_supply
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_supply
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_supply
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_supply
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增（带主键） -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.material.distribute.infrastructure.po.MaterialSupplyPO">
        insert into mrp_material_supply(id,
                                        supply_code,
                                        product_id,
                                        product_code,
                                        product_type,
                                        stock_point_id,
                                        supply_time,
                                        quantity,
                                        unfulfilled_quantity,
                                        supply_order_id,
                                        supply_type,
                                        fulfillment_status,
                                        counting_unit_id,
                                        appoint_demand_type,
                                        remark,
                                        enabled,
                                        creator,
                                        create_time,
                                        modifier,
                                        modify_time,
                                        fixed,
                                        version_value)
        values (#{id,jdbcType=VARCHAR},
                #{supplyCode,jdbcType=VARCHAR},
                #{productId,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{productType,jdbcType=VARCHAR},
                #{stockPointId,jdbcType=VARCHAR},
                #{supplyTime,jdbcType=TIMESTAMP},
                #{quantity,jdbcType=VARCHAR},
                #{unfulfilledQuantity,jdbcType=VARCHAR},
                #{supplyOrderId,jdbcType=VARCHAR},
                #{supplyType,jdbcType=VARCHAR},
                #{fulfillmentStatus,jdbcType=VARCHAR},
                #{countingUnitId,jdbcType=VARCHAR},
                #{appointDemandType,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{fixed,jdbcType=VARCHAR},
                #{versionValue,jdbcType=INTEGER})
    </insert>

    <!-- 批量新增（带主键） -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_supply(
        id,
        supply_code,
        product_id,
        product_code,
        product_type,
        stock_point_id,
        supply_time,
        quantity,
        unfulfilled_quantity,
        supply_order_id,
        supply_type,
        fulfillment_status,
        counting_unit_id,
        appoint_demand_type,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        fixed,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.supplyCode,jdbcType=VARCHAR},
            #{entity.productId,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productType,jdbcType=VARCHAR},
            #{entity.stockPointId,jdbcType=VARCHAR},
            #{entity.supplyTime,jdbcType=TIMESTAMP},
            #{entity.quantity,jdbcType=VARCHAR},
            #{entity.unfulfilledQuantity,jdbcType=VARCHAR},
            #{entity.supplyOrderId,jdbcType=VARCHAR},
            #{entity.supplyType,jdbcType=VARCHAR},
            #{entity.fulfillmentStatus,jdbcType=VARCHAR},
            #{entity.countingUnitId,jdbcType=VARCHAR},
            #{entity.appointDemandType,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.fixed,jdbcType=VARCHAR},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.material.distribute.infrastructure.po.MaterialSupplyPO">
        update mrp_material_supply
        set supply_code          = #{supplyCode,jdbcType=VARCHAR},
            product_id           = #{productId,jdbcType=VARCHAR},
            product_code         = #{productCode,jdbcType=VARCHAR},
            product_type         = #{productType,jdbcType=VARCHAR},
            stock_point_id       = #{stockPointId,jdbcType=VARCHAR},
            supply_time          = #{supplyTime,jdbcType=TIMESTAMP},
            quantity             = #{quantity,jdbcType=VARCHAR},
            unfulfilled_quantity = #{unfulfilledQuantity,jdbcType=VARCHAR},
            supply_order_id      = #{supplyOrderId,jdbcType=VARCHAR},
            supply_type          = #{supplyType,jdbcType=VARCHAR},
            fulfillment_status   = #{fulfillmentStatus,jdbcType=VARCHAR},
            counting_unit_id     = #{countingUnitId,jdbcType=VARCHAR},
            appoint_demand_type  = #{appointDemandType,jdbcType=VARCHAR},
            remark               = #{remark,jdbcType=VARCHAR},
            enabled              = #{enabled,jdbcType=VARCHAR},
            modifier             = #{modifier,jdbcType=VARCHAR},
            modify_time          = #{modifyTime,jdbcType=TIMESTAMP},
            version_value        = #{versionValue,jdbcType=INTEGER},
            fixed                = #{fixed,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mrp.material.distribute.infrastructure.po.MaterialSupplyPO">
        update mrp_material_supply
        <set>
            <if test="item.supplyCode != null and item.supplyCode != ''">
                supply_code = #{item.supplyCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productType != null and item.productType != ''">
                product_type = #{item.productType,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyTime != null">
                supply_time = #{item.supplyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.quantity != null">
                quantity = #{item.quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.unfulfilledQuantity != null">
                unfulfilled_quantity = #{item.unfulfilledQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyOrderId != null and item.supplyOrderId != ''">
                supply_order_id = #{item.supplyOrderId,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyType != null and item.supplyType != ''">
                supply_type = #{item.supplyType,jdbcType=VARCHAR},
            </if>
            <if test="item.fulfillmentStatus != null and item.fulfillmentStatus != ''">
                fulfillment_status = #{item.fulfillmentStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.countingUnitId != null and item.countingUnitId != ''">
                counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.appointDemandType != null and item.appointDemandType != ''">
                appoint_demand_type = #{item.appointDemandType,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.fixed != null and item.fixed != ''">
                fixed = #{item.fixed,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_supply
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="supply_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="unfulfilled_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.unfulfilledQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_order_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyOrderId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="fulfillment_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.fulfillmentStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="counting_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.countingUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="appoint_demand_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.appointDemandType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="fixed = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.fixed,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_material_supply
            <set>
                <if test="item.supplyCode != null and item.supplyCode != ''">
                    supply_code = #{item.supplyCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productId != null and item.productId != ''">
                    product_id = #{item.productId,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productType != null and item.productType != ''">
                    product_type = #{item.productType,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointId != null and item.stockPointId != ''">
                    stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
                </if>
                <if test="item.supplyTime != null">
                    supply_time = #{item.supplyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.quantity != null">
                    quantity = #{item.quantity,jdbcType=VARCHAR},
                </if>
                <if test="item.unfulfilledQuantity != null">
                    unfulfilled_quantity = #{item.unfulfilledQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.supplyOrderId != null and item.supplyOrderId != ''">
                    supply_order_id = #{item.supplyOrderId,jdbcType=VARCHAR},
                </if>
                <if test="item.supplyType != null and item.supplyType != ''">
                    supply_type = #{item.supplyType,jdbcType=VARCHAR},
                </if>
                <if test="item.fulfillmentStatus != null and item.fulfillmentStatus != ''">
                    fulfillment_status = #{item.fulfillmentStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.countingUnitId != null and item.countingUnitId != ''">
                    counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
                </if>
                <if test="item.appointDemandType != null and item.appointDemandType != ''">
                    appoint_demand_type = #{item.appointDemandType,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.fixed != null and item.fixed != ''">
                    fixed = #{item.fixed,jdbcType=VARCHAR},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_material_supply
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_supply where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
