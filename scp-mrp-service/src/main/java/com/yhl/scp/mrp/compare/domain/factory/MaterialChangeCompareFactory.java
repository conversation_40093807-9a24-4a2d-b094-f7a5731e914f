package com.yhl.scp.mrp.compare.domain.factory;

import com.yhl.scp.mrp.compare.domain.entity.MaterialChangeCompareDO;
import com.yhl.scp.mrp.compare.dto.MaterialChangeCompareDTO;
import com.yhl.scp.mrp.compare.infrastructure.dao.MaterialChangeCompareDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>MaterialChangeCompareFactory</code>
 * <p>
 * 材料计划变动提醒-材料对比领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-17 15:59:59
 */
@Component
public class MaterialChangeCompareFactory {

    @Resource
    private MaterialChangeCompareDao materialChangeCompareDao;

    MaterialChangeCompareDO create(MaterialChangeCompareDTO dto) {
        // TODO
        return null;
    }

}
