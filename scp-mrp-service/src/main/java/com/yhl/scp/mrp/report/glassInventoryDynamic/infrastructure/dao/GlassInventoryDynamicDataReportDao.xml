<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.dao.GlassInventoryDynamicDataReportDao">
    <resultMap id="BaseResultMap"
               type="com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.po.GlassInventoryDynamicDataReportPO">
        <!--@Table mrp_glass_inventory_dynamic_data_report-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="version_id" jdbcType="VARCHAR" property="versionId"/>
        <result column="product_color" jdbcType="VARCHAR" property="productColor"/>
        <result column="product_thickness" jdbcType="VARCHAR" property="productThickness"/>
        <result column="local_inventory_area" jdbcType="VARCHAR" property="localInventoryArea"/>
        <result column="local_inventory_weight" jdbcType="VARCHAR" property="localInventoryWeight"/>
        <result column="transit_inventory_area" jdbcType="VARCHAR" property="transitInventoryArea"/>
        <result column="transit_inventory_weight" jdbcType="VARCHAR" property="transitInventoryWeight"/>
        <result column="internal_float_glass_inventory_area" jdbcType="VARCHAR"
                property="internalFloatGlassInventoryArea"/>
        <result column="internal_float_glass_inventory_weight" jdbcType="VARCHAR"
                property="internalFloatGlassInventoryWeight"/>
        <result column="external_float_glass_inventory_area" jdbcType="VARCHAR"
                property="externalFloatGlassInventoryArea"/>
        <result column="external_float_glass_inventory_weight" jdbcType="VARCHAR"
                property="externalFloatGlassInventoryWeight"/>
        <result column="float_glass_inventory_area" jdbcType="VARCHAR" property="floatGlassInventoryArea"/>
        <result column="float_glass_inventory_weight" jdbcType="VARCHAR" property="floatGlassInventoryWeight"/>
        <result column="backlog_inventory_area" jdbcType="VARCHAR" property="backlogInventoryArea"/>
        <result column="backlog_inventory_weight" jdbcType="VARCHAR" property="backlogInventoryWeight"/>
        <result column="total_inventory_area" jdbcType="VARCHAR" property="totalInventoryArea"/>
        <result column="total_inventory_weight" jdbcType="VARCHAR" property="totalInventoryWeight"/>
        <result column="effective_total_inventory_area" jdbcType="VARCHAR" property="effectiveTotalInventoryArea"/>
        <result column="effective_total_inventory_weight" jdbcType="VARCHAR" property="effectiveTotalInventoryWeight"/>
        <result column="current_month_consume_inventory_area" jdbcType="VARCHAR"
                property="currentMonthConsumeInventoryArea"/>
        <result column="current_month_consume_inventory_weight" jdbcType="VARCHAR"
                property="currentMonthConsumeInventoryWeight"/>
        <result column="next_one_month_consume_inventory_area" jdbcType="VARCHAR"
                property="nextOneMonthConsumeInventoryArea"/>
        <result column="next_one_month_consume_inventory_weight" jdbcType="VARCHAR"
                property="nextOneMonthConsumeInventoryWeight"/>
        <result column="next_two_month_consume_inventory_area" jdbcType="VARCHAR"
                property="nextTwoMonthConsumeInventoryArea"/>
        <result column="next_two_month_consume_inventory_weight" jdbcType="VARCHAR"
                property="nextTwoMonthConsumeInventoryWeight"/>
        <result column="next_three_month_consume_inventory_area" jdbcType="VARCHAR"
                property="nextThreeMonthConsumeInventoryArea"/>
        <result column="next_three_month_consume_inventory_weight" jdbcType="VARCHAR"
                property="nextThreeMonthConsumeInventoryWeight"/>
        <result column="inventory_days" jdbcType="VARCHAR" property="inventoryDays"/>
        <result column="basic_report_Id" jdbcType="VARCHAR" property="basicReportId"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.report.glassInventoryDynamic.vo.GlassInventoryDynamicDataReportVO">
        <!-- TODO -->
        <result column="group_flag" jdbcType="VARCHAR" property="groupFlag"/>
        <result column="show_sequence" jdbcType="VARCHAR" property="showSequence"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,version_id,product_color,product_thickness,local_inventory_area,local_inventory_weight,transit_inventory_area,transit_inventory_weight,internal_float_glass_inventory_area,internal_float_glass_inventory_weight,external_float_glass_inventory_area,external_float_glass_inventory_weight,float_glass_inventory_area,float_glass_inventory_weight,backlog_inventory_area,backlog_inventory_weight,total_inventory_area,total_inventory_weight,effective_total_inventory_area,effective_total_inventory_weight,current_month_consume_inventory_area,current_month_consume_inventory_weight,next_one_month_consume_inventory_area,next_one_month_consume_inventory_weight,next_two_month_consume_inventory_area,next_two_month_consume_inventory_weight,next_three_month_consume_inventory_area,next_three_month_consume_inventory_weight,inventory_days,basic_report_id,enabled,creator,create_time,modifier,modify_time,remark,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,group_flag,show_sequence
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.versionId != null and params.versionId != ''">
                and version_id = #{params.versionId,jdbcType=VARCHAR}
            </if>
            <if test="params.productColor != null and params.productColor != ''">
                and product_color = #{params.productColor,jdbcType=VARCHAR}
            </if>
            <if test="params.productThickness != null and params.productThickness != ''">
                and product_thickness = #{params.productThickness,jdbcType=VARCHAR}
            </if>
            <if test="params.localInventoryArea != null">
                and local_inventory_area = #{params.localInventoryArea,jdbcType=VARCHAR}
            </if>
            <if test="params.localInventoryWeight != null">
                and local_inventory_weight = #{params.localInventoryWeight,jdbcType=VARCHAR}
            </if>
            <if test="params.transitInventoryArea != null">
                and transit_inventory_area = #{params.transitInventoryArea,jdbcType=VARCHAR}
            </if>
            <if test="params.transitInventoryWeight != null">
                and transit_inventory_weight = #{params.transitInventoryWeight,jdbcType=VARCHAR}
            </if>
            <if test="params.internalFloatGlassInventoryArea != null">
                and internal_float_glass_inventory_area = #{params.internalFloatGlassInventoryArea,jdbcType=VARCHAR}
            </if>
            <if test="params.internalFloatGlassInventoryWeight != null">
                and internal_float_glass_inventory_weight = #{params.internalFloatGlassInventoryWeight,jdbcType=VARCHAR}
            </if>
            <if test="params.externalFloatGlassInventoryArea != null">
                and external_float_glass_inventory_area = #{params.externalFloatGlassInventoryArea,jdbcType=VARCHAR}
            </if>
            <if test="params.externalFloatGlassInventoryWeight != null">
                and external_float_glass_inventory_weight = #{params.externalFloatGlassInventoryWeight,jdbcType=VARCHAR}
            </if>
            <if test="params.floatGlassInventoryArea != null">
                and float_glass_inventory_area = #{params.floatGlassInventoryArea,jdbcType=VARCHAR}
            </if>
            <if test="params.floatGlassInventoryWeight != null">
                and float_glass_inventory_weight = #{params.floatGlassInventoryWeight,jdbcType=VARCHAR}
            </if>
            <if test="params.backlogInventoryArea != null">
                and backlog_inventory_area = #{params.backlogInventoryArea,jdbcType=VARCHAR}
            </if>
            <if test="params.backlogInventoryWeight != null">
                and backlog_inventory_weight = #{params.backlogInventoryWeight,jdbcType=VARCHAR}
            </if>
            <if test="params.totalInventoryArea != null">
                and total_inventory_area = #{params.totalInventoryArea,jdbcType=VARCHAR}
            </if>
            <if test="params.totalInventoryWeight != null">
                and total_inventory_weight = #{params.totalInventoryWeight,jdbcType=VARCHAR}
            </if>
            <if test="params.effectiveTotalInventoryArea != null">
                and effective_total_inventory_area = #{params.effectiveTotalInventoryArea,jdbcType=VARCHAR}
            </if>
            <if test="params.effectiveTotalInventoryWeight != null">
                and effective_total_inventory_weight = #{params.effectiveTotalInventoryWeight,jdbcType=VARCHAR}
            </if>
            <if test="params.currentMonthConsumeInventoryArea != null">
                and current_month_consume_inventory_area = #{params.currentMonthConsumeInventoryArea,jdbcType=VARCHAR}
            </if>
            <if test="params.currentMonthConsumeInventoryWeight != null">
                and current_month_consume_inventory_weight =
                #{params.currentMonthConsumeInventoryWeight,jdbcType=VARCHAR}
            </if>
            <if test="params.nextOneMonthConsumeInventoryArea != null">
                and next_one_month_consume_inventory_area = #{params.nextOneMonthConsumeInventoryArea,jdbcType=VARCHAR}
            </if>
            <if test="params.nextOneMonthConsumeInventoryWeight != null">
                and next_one_month_consume_inventory_weight =
                #{params.nextOneMonthConsumeInventoryWeight,jdbcType=VARCHAR}
            </if>
            <if test="params.nextTwoMonthConsumeInventoryArea != null">
                and next_two_month_consume_inventory_area = #{params.nextTwoMonthConsumeInventoryArea,jdbcType=VARCHAR}
            </if>
            <if test="params.nextTwoMonthConsumeInventoryWeight != null">
                and next_two_month_consume_inventory_weight =
                #{params.nextTwoMonthConsumeInventoryWeight,jdbcType=VARCHAR}
            </if>
            <if test="params.nextThreeMonthConsumeInventoryArea != null">
                and next_three_month_consume_inventory_area =
                #{params.nextThreeMonthConsumeInventoryArea,jdbcType=VARCHAR}
            </if>
            <if test="params.nextThreeMonthConsumeInventoryWeight != null">
                and next_three_month_consume_inventory_weight =
                #{params.nextThreeMonthConsumeInventoryWeight,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryDays != null">
                and inventory_days = #{params.inventoryDays,jdbcType=VARCHAR}
            </if>
            <if test="params.groupFlag != null">
                and group_flag = #{params.groupFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.basicReportId != null">
                and basic_report_id = #{params.basicReportId,jdbcType=VARCHAR}
            </if>
            <if test="params.showSequence != null">
                and show_sequence = #{params.showSequence,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_glass_inventory_dynamic_data_report
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_glass_inventory_dynamic_data_report
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mrp_glass_inventory_dynamic_data_report
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_glass_inventory_dynamic_data_report
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- VO条件查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mrp_glass_inventory_dynamic_data_report
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert"
            parameterType="com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.po.GlassInventoryDynamicDataReportPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_glass_inventory_dynamic_data_report(
        id,
        version_id,
        product_color,
        product_thickness,
        local_inventory_area,
        local_inventory_weight,
        transit_inventory_area,
        transit_inventory_weight,
        internal_float_glass_inventory_area,
        internal_float_glass_inventory_weight,
        external_float_glass_inventory_area,
        external_float_glass_inventory_weight,
        float_glass_inventory_area,
        float_glass_inventory_weight,
        backlog_inventory_area,
        backlog_inventory_weight,
        total_inventory_area,
        total_inventory_weight,
        effective_total_inventory_area,
        effective_total_inventory_weight,
        current_month_consume_inventory_area,
        current_month_consume_inventory_weight,
        next_one_month_consume_inventory_area,
        next_one_month_consume_inventory_weight,
        next_two_month_consume_inventory_area,
        next_two_month_consume_inventory_weight,
        next_three_month_consume_inventory_area,
        next_three_month_consume_inventory_weight,
        inventory_days,
        basic_report_id,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        remark,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{versionId,jdbcType=VARCHAR},
        #{productColor,jdbcType=VARCHAR},
        #{productThickness,jdbcType=VARCHAR},
        #{localInventoryArea,jdbcType=VARCHAR},
        #{localInventoryWeight,jdbcType=VARCHAR},
        #{transitInventoryArea,jdbcType=VARCHAR},
        #{transitInventoryWeight,jdbcType=VARCHAR},
        #{internalFloatGlassInventoryArea,jdbcType=VARCHAR},
        #{internalFloatGlassInventoryWeight,jdbcType=VARCHAR},
        #{externalFloatGlassInventoryArea,jdbcType=VARCHAR},
        #{externalFloatGlassInventoryWeight,jdbcType=VARCHAR},
        #{floatGlassInventoryArea,jdbcType=VARCHAR},
        #{floatGlassInventoryWeight,jdbcType=VARCHAR},
        #{backlogInventoryArea,jdbcType=VARCHAR},
        #{backlogInventoryWeight,jdbcType=VARCHAR},
        #{totalInventoryArea,jdbcType=VARCHAR},
        #{totalInventoryWeight,jdbcType=VARCHAR},
        #{effectiveTotalInventoryArea,jdbcType=VARCHAR},
        #{effectiveTotalInventoryWeight,jdbcType=VARCHAR},
        #{currentMonthConsumeInventoryArea,jdbcType=VARCHAR},
        #{currentMonthConsumeInventoryWeight,jdbcType=VARCHAR},
        #{nextOneMonthConsumeInventoryArea,jdbcType=VARCHAR},
        #{nextOneMonthConsumeInventoryWeight,jdbcType=VARCHAR},
        #{nextTwoMonthConsumeInventoryArea,jdbcType=VARCHAR},
        #{nextTwoMonthConsumeInventoryWeight,jdbcType=VARCHAR},
        #{nextThreeMonthConsumeInventoryArea,jdbcType=VARCHAR},
        #{nextThreeMonthConsumeInventoryWeight,jdbcType=VARCHAR},
        #{inventoryDays,jdbcType=VARCHAR},
        #{basicReportId,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.po.GlassInventoryDynamicDataReportPO">
        insert into mrp_glass_inventory_dynamic_data_report(id,
                                                            version_id,
                                                            product_color,
                                                            product_thickness,
                                                            local_inventory_area,
                                                            local_inventory_weight,
                                                            transit_inventory_area,
                                                            transit_inventory_weight,
                                                            internal_float_glass_inventory_area,
                                                            internal_float_glass_inventory_weight,
                                                            external_float_glass_inventory_area,
                                                            external_float_glass_inventory_weight,
                                                            float_glass_inventory_area,
                                                            float_glass_inventory_weight,
                                                            backlog_inventory_area,
                                                            backlog_inventory_weight,
                                                            total_inventory_area,
                                                            total_inventory_weight,
                                                            effective_total_inventory_area,
                                                            effective_total_inventory_weight,
                                                            current_month_consume_inventory_area,
                                                            current_month_consume_inventory_weight,
                                                            next_one_month_consume_inventory_area,
                                                            next_one_month_consume_inventory_weight,
                                                            next_two_month_consume_inventory_area,
                                                            next_two_month_consume_inventory_weight,
                                                            next_three_month_consume_inventory_area,
                                                            next_three_month_consume_inventory_weight,
                                                            inventory_days,
                                                            basic_report_id,
                                                            enabled,
                                                            creator,
                                                            create_time,
                                                            modifier,
                                                            modify_time,
                                                            remark,
                                                            version_value)
        values (#{id,jdbcType=VARCHAR},
                #{versionId,jdbcType=VARCHAR},
                #{productColor,jdbcType=VARCHAR},
                #{productThickness,jdbcType=VARCHAR},
                #{localInventoryArea,jdbcType=VARCHAR},
                #{localInventoryWeight,jdbcType=VARCHAR},
                #{transitInventoryArea,jdbcType=VARCHAR},
                #{transitInventoryWeight,jdbcType=VARCHAR},
                #{internalFloatGlassInventoryArea,jdbcType=VARCHAR},
                #{internalFloatGlassInventoryWeight,jdbcType=VARCHAR},
                #{externalFloatGlassInventoryArea,jdbcType=VARCHAR},
                #{externalFloatGlassInventoryWeight,jdbcType=VARCHAR},
                #{floatGlassInventoryArea,jdbcType=VARCHAR},
                #{floatGlassInventoryWeight,jdbcType=VARCHAR},
                #{backlogInventoryArea,jdbcType=VARCHAR},
                #{backlogInventoryWeight,jdbcType=VARCHAR},
                #{totalInventoryArea,jdbcType=VARCHAR},
                #{totalInventoryWeight,jdbcType=VARCHAR},
                #{effectiveTotalInventoryArea,jdbcType=VARCHAR},
                #{effectiveTotalInventoryWeight,jdbcType=VARCHAR},
                #{currentMonthConsumeInventoryArea,jdbcType=VARCHAR},
                #{currentMonthConsumeInventoryWeight,jdbcType=VARCHAR},
                #{nextOneMonthConsumeInventoryArea,jdbcType=VARCHAR},
                #{nextOneMonthConsumeInventoryWeight,jdbcType=VARCHAR},
                #{nextTwoMonthConsumeInventoryArea,jdbcType=VARCHAR},
                #{nextTwoMonthConsumeInventoryWeight,jdbcType=VARCHAR},
                #{nextThreeMonthConsumeInventoryArea,jdbcType=VARCHAR},
                #{nextThreeMonthConsumeInventoryWeight,jdbcType=VARCHAR},
                #{inventoryDays,jdbcType=VARCHAR},
                #{basicReportId,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{remark,jdbcType=VARCHAR},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_glass_inventory_dynamic_data_report(
        id,
        version_id,
        product_color,
        product_thickness,
        local_inventory_area,
        local_inventory_weight,
        transit_inventory_area,
        transit_inventory_weight,
        internal_float_glass_inventory_area,
        internal_float_glass_inventory_weight,
        external_float_glass_inventory_area,
        external_float_glass_inventory_weight,
        float_glass_inventory_area,
        float_glass_inventory_weight,
        backlog_inventory_area,
        backlog_inventory_weight,
        total_inventory_area,
        total_inventory_weight,
        effective_total_inventory_area,
        effective_total_inventory_weight,
        current_month_consume_inventory_area,
        current_month_consume_inventory_weight,
        next_one_month_consume_inventory_area,
        next_one_month_consume_inventory_weight,
        next_two_month_consume_inventory_area,
        next_two_month_consume_inventory_weight,
        next_three_month_consume_inventory_area,
        next_three_month_consume_inventory_weight,
        inventory_days,
        basic_report_id,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        remark,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.versionId,jdbcType=VARCHAR},
            #{entity.productColor,jdbcType=VARCHAR},
            #{entity.productThickness,jdbcType=VARCHAR},
            #{entity.localInventoryArea,jdbcType=VARCHAR},
            #{entity.localInventoryWeight,jdbcType=VARCHAR},
            #{entity.transitInventoryArea,jdbcType=VARCHAR},
            #{entity.transitInventoryWeight,jdbcType=VARCHAR},
            #{entity.internalFloatGlassInventoryArea,jdbcType=VARCHAR},
            #{entity.internalFloatGlassInventoryWeight,jdbcType=VARCHAR},
            #{entity.externalFloatGlassInventoryArea,jdbcType=VARCHAR},
            #{entity.externalFloatGlassInventoryWeight,jdbcType=VARCHAR},
            #{entity.floatGlassInventoryArea,jdbcType=VARCHAR},
            #{entity.floatGlassInventoryWeight,jdbcType=VARCHAR},
            #{entity.backlogInventoryArea,jdbcType=VARCHAR},
            #{entity.backlogInventoryWeight,jdbcType=VARCHAR},
            #{entity.totalInventoryArea,jdbcType=VARCHAR},
            #{entity.totalInventoryWeight,jdbcType=VARCHAR},
            #{entity.effectiveTotalInventoryArea,jdbcType=VARCHAR},
            #{entity.effectiveTotalInventoryWeight,jdbcType=VARCHAR},
            #{entity.currentMonthConsumeInventoryArea,jdbcType=VARCHAR},
            #{entity.currentMonthConsumeInventoryWeight,jdbcType=VARCHAR},
            #{entity.nextOneMonthConsumeInventoryArea,jdbcType=VARCHAR},
            #{entity.nextOneMonthConsumeInventoryWeight,jdbcType=VARCHAR},
            #{entity.nextTwoMonthConsumeInventoryArea,jdbcType=VARCHAR},
            #{entity.nextTwoMonthConsumeInventoryWeight,jdbcType=VARCHAR},
            #{entity.nextThreeMonthConsumeInventoryArea,jdbcType=VARCHAR},
            #{entity.nextThreeMonthConsumeInventoryWeight,jdbcType=VARCHAR},
            #{entity.inventoryDays,jdbcType=VARCHAR},
            #{entity.basicReportId,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_glass_inventory_dynamic_data_report(
        id,
        version_id,
        product_color,
        product_thickness,
        local_inventory_area,
        local_inventory_weight,
        transit_inventory_area,
        transit_inventory_weight,
        internal_float_glass_inventory_area,
        internal_float_glass_inventory_weight,
        external_float_glass_inventory_area,
        external_float_glass_inventory_weight,
        float_glass_inventory_area,
        float_glass_inventory_weight,
        backlog_inventory_area,
        backlog_inventory_weight,
        total_inventory_area,
        total_inventory_weight,
        effective_total_inventory_area,
        effective_total_inventory_weight,
        current_month_consume_inventory_area,
        current_month_consume_inventory_weight,
        next_one_month_consume_inventory_area,
        next_one_month_consume_inventory_weight,
        next_two_month_consume_inventory_area,
        next_two_month_consume_inventory_weight,
        next_three_month_consume_inventory_area,
        next_three_month_consume_inventory_weight,
        inventory_days,
        basic_report_id,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        remark,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.versionId,jdbcType=VARCHAR},
            #{entity.productColor,jdbcType=VARCHAR},
            #{entity.productThickness,jdbcType=VARCHAR},
            #{entity.localInventoryArea,jdbcType=VARCHAR},
            #{entity.localInventoryWeight,jdbcType=VARCHAR},
            #{entity.transitInventoryArea,jdbcType=VARCHAR},
            #{entity.transitInventoryWeight,jdbcType=VARCHAR},
            #{entity.internalFloatGlassInventoryArea,jdbcType=VARCHAR},
            #{entity.internalFloatGlassInventoryWeight,jdbcType=VARCHAR},
            #{entity.externalFloatGlassInventoryArea,jdbcType=VARCHAR},
            #{entity.externalFloatGlassInventoryWeight,jdbcType=VARCHAR},
            #{entity.floatGlassInventoryArea,jdbcType=VARCHAR},
            #{entity.floatGlassInventoryWeight,jdbcType=VARCHAR},
            #{entity.backlogInventoryArea,jdbcType=VARCHAR},
            #{entity.backlogInventoryWeight,jdbcType=VARCHAR},
            #{entity.totalInventoryArea,jdbcType=VARCHAR},
            #{entity.totalInventoryWeight,jdbcType=VARCHAR},
            #{entity.effectiveTotalInventoryArea,jdbcType=VARCHAR},
            #{entity.effectiveTotalInventoryWeight,jdbcType=VARCHAR},
            #{entity.currentMonthConsumeInventoryArea,jdbcType=VARCHAR},
            #{entity.currentMonthConsumeInventoryWeight,jdbcType=VARCHAR},
            #{entity.nextOneMonthConsumeInventoryArea,jdbcType=VARCHAR},
            #{entity.nextOneMonthConsumeInventoryWeight,jdbcType=VARCHAR},
            #{entity.nextTwoMonthConsumeInventoryArea,jdbcType=VARCHAR},
            #{entity.nextTwoMonthConsumeInventoryWeight,jdbcType=VARCHAR},
            #{entity.nextThreeMonthConsumeInventoryArea,jdbcType=VARCHAR},
            #{entity.nextThreeMonthConsumeInventoryWeight,jdbcType=VARCHAR},
            #{entity.inventoryDays,jdbcType=VARCHAR},
            #{entity.basicReportId,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update"
            parameterType="com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.po.GlassInventoryDynamicDataReportPO">
        update mrp_glass_inventory_dynamic_data_report
        set version_id                                = #{versionId,jdbcType=VARCHAR},
            product_color                             = #{productColor,jdbcType=VARCHAR},
            product_thickness                         = #{productThickness,jdbcType=VARCHAR},
            local_inventory_area                      = #{localInventoryArea,jdbcType=VARCHAR},
            local_inventory_weight                    = #{localInventoryWeight,jdbcType=VARCHAR},
            transit_inventory_area                    = #{transitInventoryArea,jdbcType=VARCHAR},
            transit_inventory_weight                  = #{transitInventoryWeight,jdbcType=VARCHAR},
            internal_float_glass_inventory_area       = #{internalFloatGlassInventoryArea,jdbcType=VARCHAR},
            internal_float_glass_inventory_weight     = #{internalFloatGlassInventoryWeight,jdbcType=VARCHAR},
            external_float_glass_inventory_area       = #{externalFloatGlassInventoryArea,jdbcType=VARCHAR},
            external_float_glass_inventory_weight     = #{externalFloatGlassInventoryWeight,jdbcType=VARCHAR},
            float_glass_inventory_area                = #{floatGlassInventoryArea,jdbcType=VARCHAR},
            float_glass_inventory_weight              = #{floatGlassInventoryWeight,jdbcType=VARCHAR},
            backlog_inventory_area                    = #{backlogInventoryArea,jdbcType=VARCHAR},
            backlog_inventory_weight                  = #{backlogInventoryWeight,jdbcType=VARCHAR},
            total_inventory_area                      = #{totalInventoryArea,jdbcType=VARCHAR},
            total_inventory_weight                    = #{totalInventoryWeight,jdbcType=VARCHAR},
            effective_total_inventory_area            = #{effectiveTotalInventoryArea,jdbcType=VARCHAR},
            effective_total_inventory_weight          = #{effectiveTotalInventoryWeight,jdbcType=VARCHAR},
            current_month_consume_inventory_area      = #{currentMonthConsumeInventoryArea,jdbcType=VARCHAR},
            current_month_consume_inventory_weight    = #{currentMonthConsumeInventoryWeight,jdbcType=VARCHAR},
            next_one_month_consume_inventory_area     = #{nextOneMonthConsumeInventoryArea,jdbcType=VARCHAR},
            next_one_month_consume_inventory_weight   = #{nextOneMonthConsumeInventoryWeight,jdbcType=VARCHAR},
            next_two_month_consume_inventory_area     = #{nextTwoMonthConsumeInventoryArea,jdbcType=VARCHAR},
            next_two_month_consume_inventory_weight   = #{nextTwoMonthConsumeInventoryWeight,jdbcType=VARCHAR},
            next_three_month_consume_inventory_area   = #{nextThreeMonthConsumeInventoryArea,jdbcType=VARCHAR},
            next_three_month_consume_inventory_weight = #{nextThreeMonthConsumeInventoryWeight,jdbcType=VARCHAR},
            inventory_days                            = #{inventoryDays,jdbcType=VARCHAR},
            basic_report_id                           = #{basicReportId,jdbcType=VARCHAR},
            enabled                                   = #{enabled,jdbcType=VARCHAR},
            modifier                                  = #{modifier,jdbcType=VARCHAR},
            modify_time                               = #{modifyTime,jdbcType=TIMESTAMP},
            remark             = #{remark,jdbcType=VARCHAR},
            version_value      = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.po.GlassInventoryDynamicDataReportPO">
        update mrp_glass_inventory_dynamic_data_report
        <set>
            <if test="item.versionId != null and item.versionId != ''">
                version_id = #{item.versionId,jdbcType=VARCHAR},
            </if>
            <if test="item.productColor != null and item.productColor != ''">
                product_color = #{item.productColor,jdbcType=VARCHAR},
            </if>
            <if test="item.productThickness != null and item.productThickness != ''">
                product_thickness = #{item.productThickness,jdbcType=VARCHAR},
            </if>
            <if test="item.localInventoryArea != null">
                local_inventory_area = #{item.localInventoryArea,jdbcType=VARCHAR},
            </if>
            <if test="item.localInventoryWeight != null">
                local_inventory_weight = #{item.localInventoryWeight,jdbcType=VARCHAR},
            </if>
            <if test="item.transitInventoryArea != null">
                transit_inventory_area = #{item.transitInventoryArea,jdbcType=VARCHAR},
            </if>
            <if test="item.transitInventoryWeight != null">
                transit_inventory_weight = #{item.transitInventoryWeight,jdbcType=VARCHAR},
            </if>
            <if test="item.internalFloatGlassInventoryArea != null">
                internal_float_glass_inventory_area = #{item.internalFloatGlassInventoryArea,jdbcType=VARCHAR},
            </if>
            <if test="item.internalFloatGlassInventoryWeight != null">
                internal_float_glass_inventory_weight = #{item.internalFloatGlassInventoryWeight,jdbcType=VARCHAR},
            </if>
            <if test="item.externalFloatGlassInventoryArea != null">
                external_float_glass_inventory_area = #{item.externalFloatGlassInventoryArea,jdbcType=VARCHAR},
            </if>
            <if test="item.externalFloatGlassInventoryWeight != null">
                external_float_glass_inventory_weight = #{item.externalFloatGlassInventoryWeight,jdbcType=VARCHAR},
            </if>
            <if test="item.floatGlassInventoryArea != null">
                float_glass_inventory_area = #{item.floatGlassInventoryArea,jdbcType=VARCHAR},
            </if>
            <if test="item.floatGlassInventoryWeight != null">
                float_glass_inventory_weight = #{item.floatGlassInventoryWeight,jdbcType=VARCHAR},
            </if>
            <if test="item.backlogInventoryArea != null">
                backlog_inventory_area = #{item.backlogInventoryArea,jdbcType=VARCHAR},
            </if>
            <if test="item.backlogInventoryWeight != null">
                backlog_inventory_weight = #{item.backlogInventoryWeight,jdbcType=VARCHAR},
            </if>
            <if test="item.totalInventoryArea != null">
                total_inventory_area = #{item.totalInventoryArea,jdbcType=VARCHAR},
            </if>
            <if test="item.totalInventoryWeight != null">
                total_inventory_weight = #{item.totalInventoryWeight,jdbcType=VARCHAR},
            </if>
            <if test="item.effectiveTotalInventoryArea != null">
                effective_total_inventory_area = #{item.effectiveTotalInventoryArea,jdbcType=VARCHAR},
            </if>
            <if test="item.effectiveTotalInventoryWeight != null">
                effective_total_inventory_weight = #{item.effectiveTotalInventoryWeight,jdbcType=VARCHAR},
            </if>
            <if test="item.currentMonthConsumeInventoryArea != null">
                current_month_consume_inventory_area = #{item.currentMonthConsumeInventoryArea,jdbcType=VARCHAR},
            </if>
            <if test="item.currentMonthConsumeInventoryWeight != null">
                current_month_consume_inventory_weight = #{item.currentMonthConsumeInventoryWeight,jdbcType=VARCHAR},
            </if>
            <if test="item.nextOneMonthConsumeInventoryArea != null">
                next_one_month_consume_inventory_area = #{item.nextOneMonthConsumeInventoryArea,jdbcType=VARCHAR},
            </if>
            <if test="item.nextOneMonthConsumeInventoryWeight != null">
                next_one_month_consume_inventory_weight = #{item.nextOneMonthConsumeInventoryWeight,jdbcType=VARCHAR},
            </if>
            <if test="item.nextTwoMonthConsumeInventoryArea != null">
                next_two_month_consume_inventory_area = #{item.nextTwoMonthConsumeInventoryArea,jdbcType=VARCHAR},
            </if>
            <if test="item.nextTwoMonthConsumeInventoryWeight != null">
                next_two_month_consume_inventory_weight = #{item.nextTwoMonthConsumeInventoryWeight,jdbcType=VARCHAR},
            </if>
            <if test="item.nextThreeMonthConsumeInventoryArea != null">
                next_three_month_consume_inventory_area = #{item.nextThreeMonthConsumeInventoryArea,jdbcType=VARCHAR},
            </if>
            <if test="item.nextThreeMonthConsumeInventoryWeight != null">
                next_three_month_consume_inventory_weight =
                #{item.nextThreeMonthConsumeInventoryWeight,jdbcType=VARCHAR},
            </if>
            <if test="item.inventoryDays != null">
                inventory_days = #{item.inventoryDays,jdbcType=VARCHAR},
            </if>
            <if test="item.basicReportId != null">
                basic_report_id = #{item.basicReportId,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_glass_inventory_dynamic_data_report
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_color = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productColor,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_thickness = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productThickness,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="local_inventory_area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.localInventoryArea,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="local_inventory_weight = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.localInventoryWeight,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transit_inventory_area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transitInventoryArea,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transit_inventory_weight = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transitInventoryWeight,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="internal_float_glass_inventory_area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.internalFloatGlassInventoryArea,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="internal_float_glass_inventory_weight = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.internalFloatGlassInventoryWeight,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="external_float_glass_inventory_area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.externalFloatGlassInventoryArea,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="external_float_glass_inventory_weight = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.externalFloatGlassInventoryWeight,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="float_glass_inventory_area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.floatGlassInventoryArea,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="float_glass_inventory_weight = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.floatGlassInventoryWeight,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="backlog_inventory_area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.backlogInventoryArea,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="backlog_inventory_weight = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.backlogInventoryWeight,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="total_inventory_area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.totalInventoryArea,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="total_inventory_weight = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.totalInventoryWeight,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="effective_total_inventory_area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.effectiveTotalInventoryArea,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="effective_total_inventory_weight = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.effectiveTotalInventoryWeight,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="current_month_consume_inventory_area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.currentMonthConsumeInventoryArea,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="current_month_consume_inventory_weight = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.currentMonthConsumeInventoryWeight,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="next_one_month_consume_inventory_area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.nextOneMonthConsumeInventoryArea,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="next_one_month_consume_inventory_weight = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.nextOneMonthConsumeInventoryWeight,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="next_two_month_consume_inventory_area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.nextTwoMonthConsumeInventoryArea,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="next_two_month_consume_inventory_weight = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.nextTwoMonthConsumeInventoryWeight,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="next_three_month_consume_inventory_area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.nextThreeMonthConsumeInventoryArea,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="next_three_month_consume_inventory_weight = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.nextThreeMonthConsumeInventoryWeight,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="inventory_days = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inventoryDays,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="basic_report_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.basicReportId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_glass_inventory_dynamic_data_report
            <set>
                <if test="item.versionId != null and item.versionId != ''">
                    version_id = #{item.versionId,jdbcType=VARCHAR},
                </if>
                <if test="item.productColor != null and item.productColor != ''">
                    product_color = #{item.productColor,jdbcType=VARCHAR},
                </if>
                <if test="item.productThickness != null and item.productThickness != ''">
                    product_thickness = #{item.productThickness,jdbcType=VARCHAR},
                </if>
                <if test="item.localInventoryArea != null">
                    local_inventory_area = #{item.localInventoryArea,jdbcType=VARCHAR},
                </if>
                <if test="item.localInventoryWeight != null">
                    local_inventory_weight = #{item.localInventoryWeight,jdbcType=VARCHAR},
                </if>
                <if test="item.transitInventoryArea != null">
                    transit_inventory_area = #{item.transitInventoryArea,jdbcType=VARCHAR},
                </if>
                <if test="item.transitInventoryWeight != null">
                    transit_inventory_weight = #{item.transitInventoryWeight,jdbcType=VARCHAR},
                </if>
                <if test="item.internalFloatGlassInventoryArea != null">
                    internal_float_glass_inventory_area = #{item.internalFloatGlassInventoryArea,jdbcType=VARCHAR},
                </if>
                <if test="item.internalFloatGlassInventoryWeight != null">
                    internal_float_glass_inventory_weight = #{item.internalFloatGlassInventoryWeight,jdbcType=VARCHAR},
                </if>
                <if test="item.externalFloatGlassInventoryArea != null">
                    external_float_glass_inventory_area = #{item.externalFloatGlassInventoryArea,jdbcType=VARCHAR},
                </if>
                <if test="item.externalFloatGlassInventoryWeight != null">
                    external_float_glass_inventory_weight = #{item.externalFloatGlassInventoryWeight,jdbcType=VARCHAR},
                </if>
                <if test="item.floatGlassInventoryArea != null">
                    float_glass_inventory_area = #{item.floatGlassInventoryArea,jdbcType=VARCHAR},
                </if>
                <if test="item.floatGlassInventoryWeight != null">
                    float_glass_inventory_weight = #{item.floatGlassInventoryWeight,jdbcType=VARCHAR},
                </if>
                <if test="item.backlogInventoryArea != null">
                    backlog_inventory_area = #{item.backlogInventoryArea,jdbcType=VARCHAR},
                </if>
                <if test="item.backlogInventoryWeight != null">
                    backlog_inventory_weight = #{item.backlogInventoryWeight,jdbcType=VARCHAR},
                </if>
                <if test="item.totalInventoryArea != null">
                    total_inventory_area = #{item.totalInventoryArea,jdbcType=VARCHAR},
                </if>
                <if test="item.totalInventoryWeight != null">
                    total_inventory_weight = #{item.totalInventoryWeight,jdbcType=VARCHAR},
                </if>
                <if test="item.effectiveTotalInventoryArea != null">
                    effective_total_inventory_area = #{item.effectiveTotalInventoryArea,jdbcType=VARCHAR},
                </if>
                <if test="item.effectiveTotalInventoryWeight != null">
                    effective_total_inventory_weight = #{item.effectiveTotalInventoryWeight,jdbcType=VARCHAR},
                </if>
                <if test="item.currentMonthConsumeInventoryArea != null">
                    current_month_consume_inventory_area = #{item.currentMonthConsumeInventoryArea,jdbcType=VARCHAR},
                </if>
                <if test="item.currentMonthConsumeInventoryWeight != null">
                    current_month_consume_inventory_weight =
                    #{item.currentMonthConsumeInventoryWeight,jdbcType=VARCHAR},
                </if>
                <if test="item.nextOneMonthConsumeInventoryArea != null">
                    next_one_month_consume_inventory_area = #{item.nextOneMonthConsumeInventoryArea,jdbcType=VARCHAR},
                </if>
                <if test="item.nextOneMonthConsumeInventoryWeight != null">
                    next_one_month_consume_inventory_weight =
                    #{item.nextOneMonthConsumeInventoryWeight,jdbcType=VARCHAR},
                </if>
                <if test="item.nextTwoMonthConsumeInventoryArea != null">
                    next_two_month_consume_inventory_area = #{item.nextTwoMonthConsumeInventoryArea,jdbcType=VARCHAR},
                </if>
                <if test="item.nextTwoMonthConsumeInventoryWeight != null">
                    next_two_month_consume_inventory_weight =
                    #{item.nextTwoMonthConsumeInventoryWeight,jdbcType=VARCHAR},
                </if>
                <if test="item.nextThreeMonthConsumeInventoryArea != null">
                    next_three_month_consume_inventory_area =
                    #{item.nextThreeMonthConsumeInventoryArea,jdbcType=VARCHAR},
                </if>
                <if test="item.nextThreeMonthConsumeInventoryWeight != null">
                    next_three_month_consume_inventory_weight =
                    #{item.nextThreeMonthConsumeInventoryWeight,jdbcType=VARCHAR},
                </if>
                <if test="item.inventoryDays != null">
                    inventory_days = #{item.inventoryDays,jdbcType=VARCHAR},
                </if>
                <if test="item.basicReportId != null">
                    basic_report_id = #{item.basicReportId,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_glass_inventory_dynamic_data_report
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_glass_inventory_dynamic_data_report where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
