package com.yhl.scp.ips.conf.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.conf.dto.DataCleanupDTO;
import com.yhl.scp.ips.conf.vo.DataCleanupVO;

import java.util.List;

public interface DataCleanupService extends BaseService<DataCleanupDTO, DataCleanupVO> {

    /**
     * 查询所有
     *
     * @return list {@link DataCleanupVO}
     */
    List<DataCleanupVO> selectAll();

    BaseResponse<Void> manualDataCleanup(List<String> ids);
}
