package com.yhl.scp.ips.conf.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "数据清理配置表DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DataCleanupDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -48485648411238838L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 数据库类型: mysql, mongodb
     */
    @ApiModelProperty(value = "数据库类型: mysql, mongodb")
    private String dbType;
    /**
     * 数据库名/集合名
     */
    @ApiModelProperty(value = "数据库名/集合名")
    private String databaseName;
    /**
     * 数据表名/集合名
     */
    @ApiModelProperty(value = "数据表名/集合名")
    private String tableName;
    /**
     * n天之前的数据清除，默认30天
     */
    @ApiModelProperty(value = "n天之前的数据清除，默认30天")
    private Integer beforeDays;
    /**
     * 创建时间字段名
     */
    @ApiModelProperty(value = "创建时间字段名")
    private String beforeDaysReferColumn;
    /**
     * where子句信息
     */
    @ApiModelProperty(value = "where子句信息")
    private String whereClause;
    /**
     * 每次删除批次大小
     */
    @ApiModelProperty(value = "每次删除批次大小")
    private Integer batchSize;
    /**
     * 上次清理时间
     */
    @ApiModelProperty(value = "上次清理时间")
    private Date lastCleanupTime;
    /**
     * 下次清理时间
     */
    @ApiModelProperty(value = "下次清理时间")
    private Date nextCleanupTime;
    /**
     * 描述信息
     */
    @ApiModelProperty(value = "描述信息")
    private String description;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

}
