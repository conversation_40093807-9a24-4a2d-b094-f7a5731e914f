package com.yhl.scp.ips.conf.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "数据清理配置表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataCleanupVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 464723860836771515L;

    /**
     * 数据库类型: mysql, mongodb
     */
    @ApiModelProperty(value = "数据库类型: mysql, mongodb")
    @FieldInterpretation(value = "数据库类型: mysql, mongodb")
    private String dbType;
    /**
     * 数据库名/集合名
     */
    @ApiModelProperty(value = "数据库名/集合名")
    @FieldInterpretation(value = "数据库名/集合名")
    private String databaseName;
    /**
     * 数据表名/集合名
     */
    @ApiModelProperty(value = "数据表名/集合名")
    @FieldInterpretation(value = "数据表名/集合名")
    private String tableName;
    /**
     * n天之前的数据清除，默认30天
     */
    @ApiModelProperty(value = "n天之前的数据清除，默认30天")
    @FieldInterpretation(value = "n天之前的数据清除，默认30天")
    private Integer beforeDays;
    /**
     * 创建时间字段名
     */
    @ApiModelProperty(value = "创建时间字段名")
    @FieldInterpretation(value = "创建时间字段名")
    private String beforeDaysReferColumn;
    /**
     * where子句信息
     */
    @ApiModelProperty(value = "where子句信息")
    @FieldInterpretation(value = "where子句信息")
    private String whereClause;
    /**
     * 每次删除批次大小
     */
    @ApiModelProperty(value = "每次删除批次大小")
    @FieldInterpretation(value = "每次删除批次大小")
    private Integer batchSize;
    /**
     * 上次清理时间
     */
    @ApiModelProperty(value = "上次清理时间")
    @FieldInterpretation(value = "上次清理时间")
    private Date lastCleanupTime;
    /**
     * 下次清理时间
     */
    @ApiModelProperty(value = "下次清理时间", hidden = true)
    @FieldInterpretation(value = "下次清理时间")
    private Date nextCleanupTime;
    /**
     * 描述信息
     */
    @ApiModelProperty(value = "描述信息")
    @FieldInterpretation(value = "描述信息")
    private String description;

    @Override
    public void clean() {

    }
}
