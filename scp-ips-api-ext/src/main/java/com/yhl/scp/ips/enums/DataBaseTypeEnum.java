package com.yhl.scp.ips.enums;

import com.yhl.platform.common.enums.CommonEnum;

/**
 * 数据库类型枚举
 *
 * <AUTHOR>
 */
public enum DataBaseTypeEnum implements CommonEnum {

    /**
     * MySql
     */
    MYSQL("mysql", "MySql"),
    /**
     * MongoDB
     */
    MONGO("mongo", "MongoDB");

    /**
     * 编号
     */
    private String code;
    /**
     * 描述
     */
    private String desc;

    DataBaseTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    void setDesc(String desc) {
        this.desc = desc;
    }

}