package com.yhl.scp.dfp.stock.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.dfp.stock.domain.entity.FdpInventoryBatchDetailLogDO;
import com.yhl.scp.dfp.stock.infrastructure.dao.FdpInventoryBatchDetailLogDao;
import com.yhl.scp.dfp.stock.infrastructure.po.FdpInventoryBatchDetailLogPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>FdpInventoryBatchDetailLogDomainService</code>
 * <p>
 * 领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-18 22:55:12
 */
@Service
public class FdpInventoryBatchDetailLogDomainService {

    @Resource
    private FdpInventoryBatchDetailLogDao fdpInventoryBatchDetailLogDao;

    /**
     * 数据校验
     *
     * @param fdpInventoryBatchDetailLogDO 领域对象
     */
    public void validation(FdpInventoryBatchDetailLogDO fdpInventoryBatchDetailLogDO) {
        checkNotNull(fdpInventoryBatchDetailLogDO);
        checkUniqueCode(fdpInventoryBatchDetailLogDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param fdpInventoryBatchDetailLogDO 领域对象
     */
    private void checkNotNull(FdpInventoryBatchDetailLogDO fdpInventoryBatchDetailLogDO) {
//        if (StringUtils.isBlank(fdpInventoryBatchDetailLogDO.getFdpInventoryBatchDetailLogCode())) {
//            throw new BusinessException("代码，不能为空");
//        }
//        if (StringUtils.isBlank(fdpInventoryBatchDetailLogDO.getFdpInventoryBatchDetailLogName())) {
//            throw new BusinessException("名称，不能为空");
//        }
    }

    /**
     * 唯一性校验
     *
     * @param fdpInventoryBatchDetailLogDO 领域对象
     */
    private void checkUniqueCode(FdpInventoryBatchDetailLogDO fdpInventoryBatchDetailLogDO) {
//        Map<String, Object> params = new HashMap<>(4);
//        params.put("fdpInventoryBatchDetailLogCode", fdpInventoryBatchDetailLogDO.getFdpInventoryBatchDetailLogCode());
//        if (StringUtils.isBlank(fdpInventoryBatchDetailLogDO.getId())) {
//            List<FdpInventoryBatchDetailLogPO> list = fdpInventoryBatchDetailLogDao.selectByParams(params);
//            if (CollectionUtils.isNotEmpty(list)) {
//                throw new BusinessException("新增失败，代码已存在：" + fdpInventoryBatchDetailLogDO.getFdpInventoryBatchDetailLogCode());
//            }
//        } else {
//            FdpInventoryBatchDetailLogPO old = fdpInventoryBatchDetailLogDao.selectByPrimaryKey(fdpInventoryBatchDetailLogDO.getId());
//            if (!fdpInventoryBatchDetailLogDO.getFdpInventoryBatchDetailLogCode().equals(old.getFdpInventoryBatchDetailLogCode())) {
//                List<FdpInventoryBatchDetailLogPO> list = fdpInventoryBatchDetailLogDao.selectByParams(params);
//                if (CollectionUtils.isNotEmpty(list)) {
//                    throw new BusinessException("修改失败，代码已存在：" + fdpInventoryBatchDetailLogDO.getFdpInventoryBatchDetailLogCode());
//                }
//            }
//        }
    }

}
