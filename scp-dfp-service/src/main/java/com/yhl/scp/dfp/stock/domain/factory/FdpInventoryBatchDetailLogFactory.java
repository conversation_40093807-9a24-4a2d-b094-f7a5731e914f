package com.yhl.scp.dfp.stock.domain.factory;

import com.yhl.scp.dfp.stock.domain.entity.FdpInventoryBatchDetailLogDO;
import com.yhl.scp.dfp.stock.dto.FdpInventoryBatchDetailLogDTO;
import com.yhl.scp.dfp.stock.infrastructure.dao.FdpInventoryBatchDetailLogDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>FdpInventoryBatchDetailLogFactory</code>
 * <p>
 * 领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-18 22:55:12
 */
@Component
public class FdpInventoryBatchDetailLogFactory {

    @Resource
    private FdpInventoryBatchDetailLogDao fdpInventoryBatchDetailLogDao;

    FdpInventoryBatchDetailLogDO create(FdpInventoryBatchDetailLogDTO dto) {
        // TODO
        return null;
    }

}
