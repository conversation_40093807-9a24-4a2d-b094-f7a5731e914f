package com.yhl.scp.dfp.loading.handler.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.JacksonUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.dfp.common.enums.GranularityEnum;
import com.yhl.scp.dfp.common.enums.TemplateDayPatternEnum;
import com.yhl.scp.dfp.loading.convertor.LoadingDemandSubmissionConvertor;
import com.yhl.scp.dfp.loading.convertor.LoadingDemandSubmissionHistoryConvertor;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionDTO;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionDetailDTO;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionHistoryDTO;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionHistoryDetailDTO;
import com.yhl.scp.dfp.loading.handler.LoadingAbstractHandler;
import com.yhl.scp.dfp.loading.handler.LoadingDataHolder;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionDetailService;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionHistoryDetailService;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionHistoryService;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionService;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionHistoryVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionVO;
import com.yhl.scp.dfp.loading.vo.LoadingProductVO;
import com.yhl.scp.dfp.oem.vo.OemStockPointMapVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.origin.service.OriginDemandVersionService;
import com.yhl.scp.dfp.origin.vo.OriginDemandVersionVO;
import com.yhl.scp.ips.common.SystemHolder;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>LoadingHorizonYearMonthDayHandler</code>
 * <p>
 * LoadingHorizonYearMonthDayHandler
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-20 13:59:42
 */
@Component
@Slf4j
public class LoadingHorizonYearMonthDayHandler extends LoadingAbstractHandler {

    @Resource
    private LoadingDemandSubmissionDetailService loadingDemandSubmissionDetailService;

    @Resource
    @Lazy
    private LoadingDemandSubmissionService loadingDemandSubmissionService;

    @Resource
    private OriginDemandVersionService originDemandVersionService;
    
    @Resource
    private LoadingDemandSubmissionHistoryService loadingDemandSubmissionHistoryService;
    
    @Resource
    private LoadingDemandSubmissionHistoryDetailService loadingDemandSubmissionHistoryDetailService;

    @Override
    protected String handleFileImport(LoadingDataHolder loadingDataHolder, Map<Integer, String> headers, List<Map<Integer, String>> data, Map<String, String> extMap) {
        Map<String, String> partEnabledMap = loadingDataHolder.getPartEnabledMap();
        Map<String, List<String>> productPartMap = loadingDataHolder.getProductPartMap();
        Map<String, LoadingDemandSubmissionVO> submissionVOMap = loadingDataHolder.getSubmissionVOMap();
        Map<String, LoadingDemandSubmissionHistoryVO> submissionHistoryMap = loadingDataHolder.getSubmissionHistoryMap();
        Map<String, List<LoadingDemandSubmissionDetailVO>> submissionHisDetailVOMap = loadingDataHolder.getSubmissionHisDetailVOMap();
        
        String originVersionId = extMap.get("originVersionId");
        String projectDemandType = extMap.get("projectDemandType");
        StringBuilder errMsg = new StringBuilder();
        // 行数据循环
        AtomicInteger rowNo = new AtomicInteger(1);
        List<LoadingDemandSubmissionDetailDTO> detailDTOS = Lists.newArrayList();
        List<LoadingDemandSubmissionVO> updateSubmissionVOs = Lists.newArrayList();
        List<LoadingDemandSubmissionDTO> createSubmissionVOs = Lists.newArrayList();
        List<String> deleteSubmissionIds = Lists.newArrayList();
        //历史数据处理
        List<LoadingDemandSubmissionHistoryDTO> createSubmissionHistoryDTOs = Lists.newArrayList();
        List<LoadingDemandSubmissionHistoryVO> updateSubmissionHistoryVOs = Lists.newArrayList();
        List<LoadingDemandSubmissionHistoryDetailDTO> createSubmissionHistoryDetailDtos = Lists.newArrayList();
        List<String> historySubmissionHistoryIds = Lists.newArrayList();
        int successCount = 0, failCount = 0;
        for (Map<Integer, String> rowMap : data) {
            rowNo.incrementAndGet();
            String productCode = "";
            String partNumber = "";
            String oemCode = "";
            Map<String, Double> quantityMap = new HashMap<>();
            StringBuilder rowErrMsg = new StringBuilder();
            // 列数据循环
            for (int i = 1; i < headers.size() + 1; i++) {
                String columnValue = rowMap.get(i);
                if (1 == i) {
                    oemCode = columnValue;
                    log.info("LoadingHorizonYearMonthDayHandler：行：{}，主机厂编码：{}", rowNo.get(), oemCode);
                    continue;
                } else if (2 == i) {
                    productCode = columnValue;
                    log.info("LoadingHorizonYearMonthDayHandler：行：{}，本厂编码：{}", rowNo.get(), productCode);
                    continue;
                } else if (3 == i) {
                    partNumber = columnValue;
                    log.info("LoadingHorizonYearMonthDayHandler：行：{}，零件号：{}", rowNo.get(), partNumber);
                    continue;
                }
                // 校验数据
                if (!validData(loadingDataHolder, oemCode, productCode, partNumber, rowNo.get(), rowErrMsg)) {
                    break;
                }
                if (StringUtils.equals(partEnabledMap.get(partNumber), "NO")) {
                    rowErrMsg.append(String.format("行数：%s", rowNo.get()));
                    rowErrMsg.append(String.format("，表头：%s", headers.get(i)));
                    rowErrMsg.append("，该零件未生效，无法导入\n");
                    break;
                }
                String header = headers.get(i);
                Double quantity = null;
                try {
                    if (StringUtils.isNotBlank(columnValue)) {
                        quantity = Double.valueOf(columnValue);
                    }
                } catch (Exception ex) {
                    rowErrMsg.append(String.format("行数：%s", rowNo.get()));
                    rowErrMsg.append(String.format("，表头：%s", headers.get(i)));
                    rowErrMsg.append("，内容非数值\n");
                    break;
                }
                if (Objects.nonNull(quantity)) {
                    Double mapQuantity = quantityMap.get(header);
                    if (Objects.nonNull(mapQuantity)) {
                        quantityMap.put(header, quantity + mapQuantity);
                    } else {
                        quantityMap.put(header, quantity);
                    }
                }
            }
            if (StringUtils.isNotBlank(rowErrMsg)) {
                errMsg.append(rowErrMsg);
                errMsg.append("\n");
                failCount++;
                continue;
            }
            // 标准化零件号
            String standardizedPartNumber = partNumber.replaceAll("[ ._-]", "");
            List<String> actualPartNumbers = productPartMap.getOrDefault(productCode, Lists.newArrayList());
            // 标准化实际零件号列表
            List<String> standardizedActualPartNumbers = actualPartNumbers.stream()
                    .map(actual -> actual.replaceAll("[ ._-]", ""))
                    .collect(Collectors.toList());
            String finalPartNumber = partNumber;
            if (standardizedActualPartNumbers.contains(standardizedPartNumber)) {
                int index = standardizedActualPartNumbers.indexOf(standardizedPartNumber);
                finalPartNumber = actualPartNumbers.get(index);
            }
            // 处理数据
            Map<String, Double> dataQuantityMap = handleData(originVersionId, quantityMap);
            if (MapUtils.isEmpty(dataQuantityMap)) {
                continue;
            }
            String submissionKey = String.join("_", oemCode, productCode);
            LoadingDemandSubmissionVO oldSubmissionVO = submissionVOMap.get(submissionKey);
            String submissionId;
            String submissionHistoryId;
            if (Objects.isNull(oldSubmissionVO)) {
                submissionId = UUIDUtil.getUUID();
                LoadingDemandSubmissionDTO submissionDTO = new LoadingDemandSubmissionDTO();
                submissionDTO.setId(submissionId);
                submissionDTO.setVersionId(originVersionId);
                submissionDTO.setDemandCategory(projectDemandType);
                submissionDTO.setOemCode(oemCode);
                submissionDTO.setProductCode(productCode);
                submissionDTO.setPartNumber(finalPartNumber);
                submissionDTO.setImportTime(new Date());
                createSubmissionVOs.add(submissionDTO);
                LoadingDemandSubmissionVO loadingDemandSubmissionVO = getLoadingSubmissionVo(submissionDTO);
                submissionVOMap.put(submissionKey, loadingDemandSubmissionVO);
            } else {
                submissionId = oldSubmissionVO.getId();
                Date oldImportTime = oldSubmissionVO.getImportTime();
                Date oldLastImportTime = oldSubmissionVO.getLastImportTime();
                if(!Objects.equals(projectDemandType, oldSubmissionVO.getDemandCategory())) {
                	oldSubmissionVO.setDemandCategory(projectDemandType);
                }
                oldSubmissionVO.setLastImportTime(oldImportTime);
                updateSubmissionVOs.add(oldSubmissionVO);
                
                //进行装车需求提报数据及装车需求提报详情数据备份
                LoadingDemandSubmissionHistoryVO submissionHistoryVO = submissionHistoryMap
                		.get(String.join("&", oemCode, productCode));
                if(submissionHistoryVO == null) {
                	submissionHistoryId = UUIDUtil.getUUID();
                	LoadingDemandSubmissionHistoryDTO submissionHistoryDTO = new LoadingDemandSubmissionHistoryDTO();
                	BeanUtils.copyProperties(oldSubmissionVO, submissionHistoryDTO);
                	submissionHistoryDTO.setId(submissionHistoryId);
                	submissionHistoryDTO.setLastImportTime(oldLastImportTime);
                	submissionHistoryDTO.setImportTime(oldImportTime);
                	createSubmissionHistoryDTOs.add(submissionHistoryDTO);
                }else {
                	submissionHistoryId = submissionHistoryVO.getId();
                	if(!Objects.equals(projectDemandType, submissionHistoryVO.getDemandCategory())) {
                    	oldSubmissionVO.setDemandCategory(projectDemandType);
                    }
                	submissionHistoryVO.setLastImportTime(oldLastImportTime);
                	submissionHistoryVO.setImportTime(oldImportTime);
                	updateSubmissionHistoryVOs.add(submissionHistoryVO);
                	//删除对应的历史明细记录
                	historySubmissionHistoryIds.add(submissionHistoryId);
                }
                //维护历史明细记录
                List<LoadingDemandSubmissionDetailVO> oldSubmissionDetailVOList = submissionHisDetailVOMap.get(submissionId);
                if(CollectionUtils.isNotEmpty(oldSubmissionDetailVOList)) {
                	List<LoadingDemandSubmissionHistoryDetailDTO> addHistoryDetailList = BeanUtil
                			.copyToList(oldSubmissionDetailVOList, LoadingDemandSubmissionHistoryDetailDTO.class);
                	addHistoryDetailList.forEach( e-> e.setSubmissionId(submissionHistoryId));
                	createSubmissionHistoryDetailDtos.addAll(addHistoryDetailList);
                	//统计历史天的需求数量
                	BigDecimal historyDemandQuantity = oldSubmissionDetailVOList.stream()
                			.filter(e -> GranularityEnum.DAY.getCode().equals(e.getSubmissionType())
                					&& e.getDemandQuantity() != null)
                			.map(LoadingDemandSubmissionDetailVO::getDemandQuantity)
                			.reduce(BigDecimal.ZERO, BigDecimal::add);
                	oldSubmissionVO.setHistoryDemandQuantity(historyDemandQuantity);
                }
                deleteSubmissionIds.add(submissionId);
            }
//            //删除历史数据
//            loadingDemandSubmissionDetailService.deleteByVersionIdAndSubmissionType(submissionId, GranularityEnum.DAY.getCode());
            //插入数据
            for (Map.Entry<String, Double> entry : dataQuantityMap.entrySet()) {
                LoadingDemandSubmissionDetailDTO detailDTO = new LoadingDemandSubmissionDetailDTO();
                detailDTO.setSubmissionId(submissionId);
                detailDTO.setSubmissionType(GranularityEnum.DAY.getCode());
                detailDTO.setDemandTime(entry.getKey());
                if (Objects.nonNull(entry.getValue())) {
                    detailDTO.setDemandQuantity(BigDecimal.valueOf(entry.getValue()));
                } else {
                    detailDTO.setDemandQuantity(null);
                }
                detailDTOS.add(detailDTO);
            }
            successCount++;
        }
        //删除修改的详情数据
        if(CollectionUtils.isNotEmpty(deleteSubmissionIds)) {
        	loadingDemandSubmissionDetailService.deleteBySubmissionIdsAndSubmissionType(deleteSubmissionIds, GranularityEnum.DAY.getCode());
        }
        if (CollectionUtils.isNotEmpty(detailDTOS)) {
            List<List<LoadingDemandSubmissionDetailDTO>> partition = Lists.partition(detailDTOS, 100);
            for (List<LoadingDemandSubmissionDetailDTO> detailDTOList : partition) {
                loadingDemandSubmissionDetailService.doCreateBatch(detailDTOList);
            }
        }
        if (CollectionUtils.isNotEmpty(createSubmissionVOs)) {
            loadingDemandSubmissionService.doCreateBatch(createSubmissionVOs);
        }
        if (CollectionUtils.isNotEmpty(updateSubmissionVOs)) {
        	List<LoadingDemandSubmissionDTO> updateSubmissionDtos = LoadingDemandSubmissionConvertor.INSTANCE.vo2Dtos(updateSubmissionVOs);
        	updateSubmissionDtos.forEach( e-> e.setImportTime(new Date()));
            loadingDemandSubmissionService.doUpdateBatch(updateSubmissionDtos);
        }
        
        //维护历史数据
        if(CollectionUtils.isNotEmpty(historySubmissionHistoryIds)) {
        	loadingDemandSubmissionHistoryDetailService.deleteBySubmissionIds(historySubmissionHistoryIds);
        }
        if (CollectionUtils.isNotEmpty(createSubmissionHistoryDTOs)) {
        	loadingDemandSubmissionHistoryService.doCreateBatch(createSubmissionHistoryDTOs);
        }
        if (CollectionUtils.isNotEmpty(updateSubmissionHistoryVOs)) {
        	List<LoadingDemandSubmissionHistoryDTO> vo2Dtos = LoadingDemandSubmissionHistoryConvertor.INSTANCE.vo2Dtos(updateSubmissionHistoryVOs);
        	loadingDemandSubmissionHistoryService.doUpdateBatch(vo2Dtos);
        }
        if (CollectionUtils.isNotEmpty(createSubmissionHistoryDetailDtos)) {
        	loadingDemandSubmissionHistoryDetailService.doCreateBatch(createSubmissionHistoryDetailDtos);
        }
        
        extMap.put("successInfo", String.format("导入数量共%s条,成功导入%s条", data.size(), successCount));
        if (failCount > 0){
            extMap.put("errorInfo", String.format("失败%s条,失败信息:%s", failCount, errMsg));
        }

        return String.format("导入数量共%s条,成功导入%s条",data.size(),successCount)
                + (failCount>0?String.format(",失败%s条,失败信息:%s",failCount,errMsg):"");
        // 错误提示
        //        Assert.isTrue(StringUtils.isBlank(errMsg.toString()), errMsg.toString());
    }

    private LoadingDemandSubmissionVO getLoadingSubmissionVo(LoadingDemandSubmissionDTO submissionDTO){
        LoadingDemandSubmissionVO loadingDemandSubmissionVO = new LoadingDemandSubmissionVO();
        loadingDemandSubmissionVO.setId(submissionDTO.getId());
        loadingDemandSubmissionVO.setVersionId(submissionDTO.getVersionId());
        loadingDemandSubmissionVO.setDemandCategory(submissionDTO.getDemandCategory());
        loadingDemandSubmissionVO.setOemCode(submissionDTO.getOemCode());
        loadingDemandSubmissionVO.setProductCode(submissionDTO.getProductCode());
        loadingDemandSubmissionVO.setPartNumber(submissionDTO.getPartNumber());
        return loadingDemandSubmissionVO;
    }

    @Override
    protected Boolean validData(LoadingDataHolder loadingDataHolder, String oemCode, String productCode, String partNumber, Integer rowIndex, StringBuilder errMsg) {
        Map<String, List<String>> productPartMap = loadingDataHolder.getProductPartMap();
        Map<String, List<OemVO>> oemMap = loadingDataHolder.getOemMap();
        List<OemStockPointMapVO> oemStockPointMapVOS = loadingDataHolder.getOemStockPointMapVOS();
        Map<String, List<String>> productCodeVehicleCodeMap = loadingDataHolder.getProductCodeVehicleCodeMap();
        List<LoadingProductVO> productStockPointVOS = loadingDataHolder.getProductStockPointVOS();
        log.info("LoadingHorizonYearMonthDayHandler-本厂编码：{}，零件号：{}", productCode, partNumber);
        if (StringUtils.isBlank(productCode) || StringUtils.isBlank(partNumber)) {
            errMsg.append(String.format("行数：%s", rowIndex));
            errMsg.append("，产品编码或零件号为空\n");
            return false;
        }
        if (MapUtils.isEmpty(productPartMap)) {
            errMsg.append(String.format("行数：%s", rowIndex));
            errMsg.append("，产品编码："+ productCode +",零件映射关系不存在\n");
            return false;
        }
        if (!oemMap.containsKey(oemCode)) {
            errMsg.append(String.format("行数：%s", rowIndex));
            errMsg.append("，无导入的主机厂编码，请确认导入的数据是否正确或者检查系统的主机厂主数据\n");
            return false;
        }
        List<String> actualPartNumbers = productPartMap.getOrDefault(productCode, Lists.newArrayList());
        // 标准化Excel中零件号，先移除Excel中零件号中的可能偶尔包含的十六进制代表不间断特殊的空格字符<0xa0>
        String standardizedPartNumber = partNumber.replaceAll("\u00A0", "").replaceAll("[ ._-]", "");
        log.info("excel import partNumber :{}", standardizedPartNumber);
        // 标准化数据库实际零件号列表，先移除数据库实际零件号列表中的可能偶尔包含的十六进制代表不间断特殊的空格字符<0xa0>
        if (!actualPartNumbers.isEmpty()) {
            List<String> standardizedActualPartNumbers = actualPartNumbers.stream()
                    .map(actual -> actual.replaceAll("\u00A0", "").replaceAll("[ ._-]", ""))
                    .collect(Collectors.toList());
            log.info("database partNumber :{}", JacksonUtils.toJson(standardizedActualPartNumbers));
            boolean isValid = standardizedActualPartNumbers.stream()
                    .anyMatch(actual -> actual.equalsIgnoreCase(standardizedPartNumber));
            if (!isValid) {
                errMsg.append(String.format("行数：%s", rowIndex));
                errMsg.append("，产品编码与零件号映射关系不存在\n");
                return false;
            }
        } else {
            errMsg.append(String.format("行数：%s", rowIndex));
            errMsg.append("，产品编码与零件号映射关系为空\n");
            return false;
        }

        if (CollectionUtils.isEmpty(oemStockPointMapVOS)) {
            errMsg.append(String.format("行数：%s", rowIndex));
            errMsg.append("，主机厂与库存点关系为空\n");
            return false;
        }

        String userId = SystemHolder.getUserId();
        log.info("userId：{}，productCode：{}", userId, productCode);
        List<LoadingProductVO> authEmpty = productStockPointVOS.stream().filter(p ->
                productCode.equals(p.getProductCode()) && (StringUtils.isNotBlank(p.getOrderPlanner())
                        && p.getOrderPlanner().contains(userId))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(authEmpty)) {
            errMsg.append(String.format("行数：%s", rowIndex));
            errMsg.append("，产品编码无权限\n");
            return false;
        }
        List<String> oemCodeList = productCodeVehicleCodeMap.get(productCode);
        if (CollectionUtils.isEmpty(oemCodeList) || !oemCodeList.contains(oemCode)) {
            errMsg.append(String.format("行数：%s", rowIndex));
            errMsg.append("，产品编码对应车型与主机厂关联为空,请检查\n");
            return false;
        }
        return true;
    }

    /**
     * 处理结果数据
     * 缺失数据中间补0，最后补null
     *
     * @param originVersionId 原始版本ID
     * @param quantityMap     数量MAP
     */
    @Override
    protected Map<String, Double> handleData(String originVersionId, Map<String, Double> quantityMap) {
        Map<String, Double> result = new HashMap<>();
        OriginDemandVersionVO demandVersionVO = originDemandVersionService.selectByPrimaryKey(originVersionId);
        if (Objects.isNull(demandVersionVO)) {
            return result;
        }
        String planPeriod = demandVersionVO.getPlanPeriod();
        Date currentDate = DateUtils.stringToDate(planPeriod, "yyyyMMdd");
        int period = 30;
        Date startDate = org.apache.commons.lang3.time.DateUtils.addDays(currentDate, 0);
        Date endDate = org.apache.commons.lang3.time.DateUtils.addDays(currentDate, period);
        //获取最后截止日期
        Date lastExistingDate = null;
        if (MapUtils.isNotEmpty(quantityMap)) {
            Optional<Date> optional = quantityMap.entrySet().stream()
                    .filter(x -> Objects.nonNull(x.getValue()))
                    .map(x -> DateUtils.stringToDate(x.getKey(), DateUtils.COMMON_DATE_STR3))
                    .filter(x -> Objects.nonNull(x) && !x.after(endDate) && !x.before(startDate))
                    .max(Comparator.comparing(Date::getTime));
            if (optional.isPresent()) {
                lastExistingDate = optional.get();
            }
        }

        Set<String> dates = quantityMap.keySet();
        for (Date date = startDate; !date.after(endDate); date = org.apache.commons.lang3.time.DateUtils.addDays(date, 1)) {
            String key = DateUtils.dateToString(date, DateUtils.COMMON_DATE_STR3);
            if (quantityMap.containsKey(key)) {
                result.put(key, quantityMap.getOrDefault(key, null));
            } else {
                //判断后面日期是否有数据，如果有数据则默认补0
                boolean flag = hasLaterDate(dates, key);
                if (flag) {
                    //后面日期存在数据则补充0
                    result.put(key, 0.0);
                    continue;
                }
                if (Objects.isNull(lastExistingDate)) {
                    result.put(key, null);
                } else {
                    // 如果是最后一个存在的日期之前，补0
                    if (date.before(lastExistingDate)) {
                        result.put(key, null);
                    } else {
                        // 最后一个存在的日期之后，补null
                        result.put(key, null);
                    }
                }
            }
        }
        return result;
    }

    @Override
    protected String getCommand() {
        return TemplateDayPatternEnum.YYYY_MM_DD.getCode();
    }

    public static boolean hasLaterDate(Set<String> dates, String date) {
        LocalDate targetDate = LocalDate.parse(date);

        for (String d : dates) {
            LocalDate currentDate = LocalDate.parse(d);
            if (targetDate.getMonth() != currentDate.getMonth() || targetDate.getYear()!=currentDate.getYear()){
                continue;
            }
            if (currentDate.isAfter(targetDate)) {
                return true;
            }
        }
        return false;
    }
}