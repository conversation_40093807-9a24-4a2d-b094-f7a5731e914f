package com.yhl.scp.dfp.stock.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>FdpInventoryBatchDetailLogPO</code>
 * <p>
 * PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-18 22:55:12
 */
public class FdpInventoryBatchDetailLogPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -68354348242981280L;

    /**
     * 库存点代码
     */
    private String stockPointCode;
    /**
     * 物料编码
     */
    private String productCode;
    /**
     * 子库存
     */
    private String subinventory;
    /**
     * 子库存描述
     */
    private String subinventoryDescription;
    /**
     * 货位
     */
    private String freightSpace;
    /**
     * 货位描述
     */
    private String freightSpaceDescription;
    /**
     * 批次
     */
    private String batch;
    /**
     * 条码号
     */
    private String barCode;
    /**
     * 现有量
     */
    private String currentQuantity;
    /**
     * 客户号
     */
    private String customerNum;
    /**
     * 零件号
     */
    private String partNum;
    /**
     * 入库时间
     */
    private String assignedTime;
    /**
     * 最后更新时间
     */
    private String lastUpdateDate;
    /**
     * 库龄
     */
    private String stockAge;
    /**
     * 库龄天数
     */
    private String stockAgeDay;
    /**
     * 保质期
     */
    private String warrantyDate;
    /**
     * 距离失效时间
     */
    private String distanceEnableDate;
    /**
     * 数据来源（ERP/MES）
     */
    private String sourceType;
    /**
     * 原始报文组织ID
     */
    private String originalOrgId;
    /**
     * 分配状态
     */
    private String allocationStatus;
    /**
     * 版本号
     */
    private Integer versionValue;

    public String getStockPointCode() {
        return stockPointCode;
    }

    public void setStockPointCode(String stockPointCode) {
        this.stockPointCode = stockPointCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getSubinventory() {
        return subinventory;
    }

    public void setSubinventory(String subinventory) {
        this.subinventory = subinventory;
    }

    public String getSubinventoryDescription() {
        return subinventoryDescription;
    }

    public void setSubinventoryDescription(String subinventoryDescription) {
        this.subinventoryDescription = subinventoryDescription;
    }

    public String getFreightSpace() {
        return freightSpace;
    }

    public void setFreightSpace(String freightSpace) {
        this.freightSpace = freightSpace;
    }

    public String getFreightSpaceDescription() {
        return freightSpaceDescription;
    }

    public void setFreightSpaceDescription(String freightSpaceDescription) {
        this.freightSpaceDescription = freightSpaceDescription;
    }

    public String getBatch() {
        return batch;
    }

    public void setBatch(String batch) {
        this.batch = batch;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getCurrentQuantity() {
        return currentQuantity;
    }

    public void setCurrentQuantity(String currentQuantity) {
        this.currentQuantity = currentQuantity;
    }

    public String getCustomerNum() {
        return customerNum;
    }

    public void setCustomerNum(String customerNum) {
        this.customerNum = customerNum;
    }

    public String getPartNum() {
        return partNum;
    }

    public void setPartNum(String partNum) {
        this.partNum = partNum;
    }

    public String getAssignedTime() {
        return assignedTime;
    }

    public void setAssignedTime(String assignedTime) {
        this.assignedTime = assignedTime;
    }

    public String getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(String lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getStockAge() {
        return stockAge;
    }

    public void setStockAge(String stockAge) {
        this.stockAge = stockAge;
    }

    public String getStockAgeDay() {
        return stockAgeDay;
    }

    public void setStockAgeDay(String stockAgeDay) {
        this.stockAgeDay = stockAgeDay;
    }

    public String getWarrantyDate() {
        return warrantyDate;
    }

    public void setWarrantyDate(String warrantyDate) {
        this.warrantyDate = warrantyDate;
    }

    public String getDistanceEnableDate() {
        return distanceEnableDate;
    }

    public void setDistanceEnableDate(String distanceEnableDate) {
        this.distanceEnableDate = distanceEnableDate;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getOriginalOrgId() {
        return originalOrgId;
    }

    public void setOriginalOrgId(String originalOrgId) {
        this.originalOrgId = originalOrgId;
    }

    public String getAllocationStatus() {
        return allocationStatus;
    }

    public void setAllocationStatus(String allocationStatus) {
        this.allocationStatus = allocationStatus;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

}
