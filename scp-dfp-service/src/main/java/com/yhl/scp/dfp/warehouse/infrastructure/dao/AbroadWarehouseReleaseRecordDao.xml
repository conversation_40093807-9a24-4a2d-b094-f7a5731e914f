<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.warehouse.infrastructure.dao.AbroadWarehouseReleaseRecordDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.warehouse.infrastructure.po.AbroadWarehouseReleaseRecordPO">
        <!--@Table fdp_abroad_warehouse_release_record-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="acreage" jdbcType="VARCHAR" property="acreage"/>
        <result column="acreage_sum" jdbcType="VARCHAR" property="acreageSum"/>
        <result column="actual_arrive_port_time" jdbcType="TIMESTAMP" property="actualArrivePortTime"/>
        <result column="actual_completion_time" jdbcType="TIMESTAMP" property="actualCompletionTime"/>
        <result column="attribute1" jdbcType="VARCHAR" property="attribute1"/>
        <result column="attribute2" jdbcType="VARCHAR" property="attribute2"/>
        <result column="bar_num" jdbcType="VARCHAR" property="barNum"/>
        <result column="bill_of_lading_num" jdbcType="VARCHAR" property="billOfLadingNum"/>
        <result column="box_num" jdbcType="VARCHAR" property="boxNum"/>
        <result column="car_num" jdbcType="VARCHAR" property="carNum"/>
        <result column="consigner" jdbcType="VARCHAR" property="consigner"/>
        <result column="container_num" jdbcType="VARCHAR" property="containerNum"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="creation_date" jdbcType="TIMESTAMP" property="creationDate"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="cust_part" jdbcType="VARCHAR" property="custPart"/>
        <result column="cust_po" jdbcType="VARCHAR" property="custPo"/>
        <result column="customer_number" jdbcType="VARCHAR" property="customerNumber"/>
        <result column="descriptions" jdbcType="VARCHAR" property="descriptions"/>
        <result column="ebs_site_id" jdbcType="VARCHAR" property="ebsSiteId"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="estimated_arrive_port_time" jdbcType="TIMESTAMP" property="estimatedArrivePortTime"/>
        <result column="estimated_completion_time" jdbcType="TIMESTAMP" property="estimatedCompletionTime"/>
        <result column="in_warehouse_time" jdbcType="TIMESTAMP" property="inWarehouseTime"/>
        <result column="instock_source" jdbcType="VARCHAR" property="instockSource"/>
        <result column="is_receive" jdbcType="VARCHAR" property="isReceive"/>
        <result column="item_code" jdbcType="VARCHAR" property="itemCode"/>
        <result column="kid" jdbcType="VARCHAR" property="kid"/>
        <result column="line_num" jdbcType="VARCHAR" property="lineNum"/>
        <result column="list_num" jdbcType="VARCHAR" property="listNum"/>
        <result column="lot_number" jdbcType="VARCHAR" property="lotNumber"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="plant_code" jdbcType="VARCHAR" property="plantCode"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="req" jdbcType="VARCHAR" property="req"/>
        <result column="req_num" jdbcType="VARCHAR" property="reqNum"/>
        <result column="ship_company" jdbcType="VARCHAR" property="shipCompany"/>
        <result column="shipment_locator_code" jdbcType="VARCHAR" property="shipmentLocatorCode"/>
        <result column="shipment_warehouse_code" jdbcType="VARCHAR" property="shipmentWarehouseCode"/>
        <result column="source_type" jdbcType="VARCHAR" property="sourceType"/>
        <result column="sum_qty" jdbcType="VARCHAR" property="sumQty"/>
        <result column="type_coode" jdbcType="VARCHAR" property="typeCoode"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.warehouse.vo.AbroadWarehouseReleaseRecordVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
id,acreage,acreage_sum,actual_arrive_port_time,actual_completion_time,attribute1,attribute2,bar_num,bill_of_lading_num,box_num,car_num,consigner,container_num,create_time,creation_date,creator,cust_part,cust_po,customer_number,descriptions,ebs_site_id,enabled,estimated_arrive_port_time,estimated_completion_time,in_warehouse_time,instock_source,is_receive,item_code,kid,line_num,list_num,lot_number,modifier,modify_time,plant_code,remark,req,req_num,ship_company,shipment_locator_code,shipment_warehouse_code,source_type,sum_qty,type_coode,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.acreage != null">
                and acreage = #{params.acreage,jdbcType=VARCHAR}
            </if>
            <if test="params.acreageSum != null">
                and acreage_sum = #{params.acreageSum,jdbcType=VARCHAR}
            </if>
            <if test="params.actualArrivePortTime != null">
                and actual_arrive_port_time = #{params.actualArrivePortTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.actualCompletionTime != null">
                and actual_completion_time = #{params.actualCompletionTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.attribute1 != null and params.attribute1 != ''">
                and attribute1 = #{params.attribute1,jdbcType=VARCHAR}
            </if>
            <if test="params.attribute2 != null and params.attribute2 != ''">
                and attribute2 = #{params.attribute2,jdbcType=VARCHAR}
            </if>
            <if test="params.barNum != null and params.barNum != ''">
                and bar_num = #{params.barNum,jdbcType=VARCHAR}
            </if>
            <if test="params.billOfLadingNum != null and params.billOfLadingNum != ''">
                and bill_of_lading_num = #{params.billOfLadingNum,jdbcType=VARCHAR}
            </if>
            <if test="params.boxNum != null and params.boxNum != ''">
                and box_num = #{params.boxNum,jdbcType=VARCHAR}
            </if>
            <if test="params.carNum != null and params.carNum != ''">
                and car_num = #{params.carNum,jdbcType=VARCHAR}
            </if>
            <if test="params.consigner != null and params.consigner != ''">
                and consigner = #{params.consigner,jdbcType=VARCHAR}
            </if>
            <if test="params.containerNum != null and params.containerNum != ''">
                and container_num = #{params.containerNum,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.creationDate != null">
                and creation_date = #{params.creationDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.custPart != null and params.custPart != ''">
                and cust_part = #{params.custPart,jdbcType=VARCHAR}
            </if>
            <if test="params.custPo != null and params.custPo != ''">
                and cust_po = #{params.custPo,jdbcType=VARCHAR}
            </if>
            <if test="params.customerNumber != null and params.customerNumber != ''">
                and customer_number = #{params.customerNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.descriptions != null and params.descriptions != ''">
                and descriptions = #{params.descriptions,jdbcType=VARCHAR}
            </if>
            <if test="params.ebsSiteId != null and params.ebsSiteId != ''">
                and ebs_site_id = #{params.ebsSiteId,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.estimatedArrivePortTime != null">
                and estimated_arrive_port_time = #{params.estimatedArrivePortTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.estimatedCompletionTime != null">
                and estimated_completion_time = #{params.estimatedCompletionTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.inWarehouseTime != null">
                and in_warehouse_time = #{params.inWarehouseTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.instockSource != null and params.instockSource != ''">
                and instock_source = #{params.instockSource,jdbcType=VARCHAR}
            </if>
            <if test="params.isReceive != null and params.isReceive != ''">
                and is_receive = #{params.isReceive,jdbcType=VARCHAR}
            </if>
            <if test="params.itemCode != null and params.itemCode != ''">
                and item_code = #{params.itemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.kid != null and params.kid != ''">
                and kid = #{params.kid,jdbcType=VARCHAR}
            </if>
            <if test="params.kids != null and params.kids != ''">
                and kid in
                <foreach collection="params.kids" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.lineNum != null and params.lineNum != ''">
                and line_num = #{params.lineNum,jdbcType=VARCHAR}
            </if>
            <if test="params.listNum != null and params.listNum != ''">
                and list_num = #{params.listNum,jdbcType=VARCHAR}
            </if>
            <if test="params.lotNumber != null and params.lotNumber != ''">
                and lot_number = #{params.lotNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.plantCode != null and params.plantCode != ''">
                and plant_code = #{params.plantCode,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.req != null and params.req != ''">
                and req = #{params.req,jdbcType=VARCHAR}
            </if>
            <if test="params.reqNum != null and params.reqNum != ''">
                and req_num = #{params.reqNum,jdbcType=VARCHAR}
            </if>
            <if test="params.shipCompany != null and params.shipCompany != ''">
                and ship_company = #{params.shipCompany,jdbcType=VARCHAR}
            </if>
            <if test="params.shipmentLocatorCode != null and params.shipmentLocatorCode != ''">
                and shipment_locator_code = #{params.shipmentLocatorCode,jdbcType=VARCHAR}
            </if>
            <if test="params.shipmentWarehouseCode != null and params.shipmentWarehouseCode != ''">
                and shipment_warehouse_code = #{params.shipmentWarehouseCode,jdbcType=VARCHAR}
            </if>
            <if test="params.sourceType != null and params.sourceType != ''">
                and source_type = #{params.sourceType,jdbcType=VARCHAR}
            </if>
            <if test="params.sumQty != null">
                and sum_qty = #{params.sumQty,jdbcType=VARCHAR}
            </if>
            <if test="params.typeCoode != null and params.typeCoode != ''">
                and type_coode = #{params.typeCoode,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_abroad_warehouse_release_record
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_abroad_warehouse_release_record
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from fdp_abroad_warehouse_release_record
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_abroad_warehouse_release_record
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.warehouse.infrastructure.po.AbroadWarehouseReleaseRecordPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_abroad_warehouse_release_record(
        id,
        acreage,
        acreage_sum,
        actual_arrive_port_time,
        actual_completion_time,
        attribute1,
        attribute2,
        bar_num,
        bill_of_lading_num,
        box_num,
        car_num,
        consigner,
        container_num,
        create_time,
        creation_date,
        creator,
        cust_part,
        cust_po,
        customer_number,
        descriptions,
        ebs_site_id,
        enabled,
        estimated_arrive_port_time,
        estimated_completion_time,
        in_warehouse_time,
        instock_source,
        is_receive,
        item_code,
        kid,
        line_num,
        list_num,
        lot_number,
        modifier,
        modify_time,
        plant_code,
        remark,
        req,
        req_num,
        ship_company,
        shipment_locator_code,
        shipment_warehouse_code,
        source_type,
        sum_qty,
        type_coode,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{acreage,jdbcType=VARCHAR},
        #{acreageSum,jdbcType=VARCHAR},
        #{actualArrivePortTime,jdbcType=TIMESTAMP},
        #{actualCompletionTime,jdbcType=TIMESTAMP},
        #{attribute1,jdbcType=VARCHAR},
        #{attribute2,jdbcType=VARCHAR},
        #{barNum,jdbcType=VARCHAR},
        #{billOfLadingNum,jdbcType=VARCHAR},
        #{boxNum,jdbcType=VARCHAR},
        #{carNum,jdbcType=VARCHAR},
        #{consigner,jdbcType=VARCHAR},
        #{containerNum,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{creationDate,jdbcType=TIMESTAMP},
        #{creator,jdbcType=VARCHAR},
        #{custPart,jdbcType=VARCHAR},
        #{custPo,jdbcType=VARCHAR},
        #{customerNumber,jdbcType=VARCHAR},
        #{descriptions,jdbcType=VARCHAR},
        #{ebsSiteId,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{estimatedArrivePortTime,jdbcType=TIMESTAMP},
        #{estimatedCompletionTime,jdbcType=TIMESTAMP},
        #{inWarehouseTime,jdbcType=TIMESTAMP},
        #{instockSource,jdbcType=VARCHAR},
        #{isReceive,jdbcType=VARCHAR},
        #{itemCode,jdbcType=VARCHAR},
        #{kid,jdbcType=VARCHAR},
        #{lineNum,jdbcType=VARCHAR},
        #{listNum,jdbcType=VARCHAR},
        #{lotNumber,jdbcType=VARCHAR},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{plantCode,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{req,jdbcType=VARCHAR},
        #{reqNum,jdbcType=VARCHAR},
        #{shipCompany,jdbcType=VARCHAR},
        #{shipmentLocatorCode,jdbcType=VARCHAR},
        #{shipmentWarehouseCode,jdbcType=VARCHAR},
        #{sourceType,jdbcType=VARCHAR},
        #{sumQty,jdbcType=VARCHAR},
        #{typeCoode,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dfp.warehouse.infrastructure.po.AbroadWarehouseReleaseRecordPO">
        insert into fdp_abroad_warehouse_release_record(
        id,
        acreage,
        acreage_sum,
        actual_arrive_port_time,
        actual_completion_time,
        attribute1,
        attribute2,
        bar_num,
        bill_of_lading_num,
        box_num,
        car_num,
        consigner,
        container_num,
        create_time,
        creation_date,
        creator,
        cust_part,
        cust_po,
        customer_number,
        descriptions,
        ebs_site_id,
        enabled,
        estimated_arrive_port_time,
        estimated_completion_time,
        in_warehouse_time,
        instock_source,
        is_receive,
        item_code,
        kid,
        line_num,
        list_num,
        lot_number,
        modifier,
        modify_time,
        plant_code,
        remark,
        req,
        req_num,
        ship_company,
        shipment_locator_code,
        shipment_warehouse_code,
        source_type,
        sum_qty,
        type_coode,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{acreage,jdbcType=VARCHAR},
        #{acreageSum,jdbcType=VARCHAR},
        #{actualArrivePortTime,jdbcType=TIMESTAMP},
        #{actualCompletionTime,jdbcType=TIMESTAMP},
        #{attribute1,jdbcType=VARCHAR},
        #{attribute2,jdbcType=VARCHAR},
        #{barNum,jdbcType=VARCHAR},
        #{billOfLadingNum,jdbcType=VARCHAR},
        #{boxNum,jdbcType=VARCHAR},
        #{carNum,jdbcType=VARCHAR},
        #{consigner,jdbcType=VARCHAR},
        #{containerNum,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{creationDate,jdbcType=TIMESTAMP},
        #{creator,jdbcType=VARCHAR},
        #{custPart,jdbcType=VARCHAR},
        #{custPo,jdbcType=VARCHAR},
        #{customerNumber,jdbcType=VARCHAR},
        #{descriptions,jdbcType=VARCHAR},
        #{ebsSiteId,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{estimatedArrivePortTime,jdbcType=TIMESTAMP},
        #{estimatedCompletionTime,jdbcType=TIMESTAMP},
        #{inWarehouseTime,jdbcType=TIMESTAMP},
        #{instockSource,jdbcType=VARCHAR},
        #{isReceive,jdbcType=VARCHAR},
        #{itemCode,jdbcType=VARCHAR},
        #{kid,jdbcType=VARCHAR},
        #{lineNum,jdbcType=VARCHAR},
        #{listNum,jdbcType=VARCHAR},
        #{lotNumber,jdbcType=VARCHAR},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{plantCode,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{req,jdbcType=VARCHAR},
        #{reqNum,jdbcType=VARCHAR},
        #{shipCompany,jdbcType=VARCHAR},
        #{shipmentLocatorCode,jdbcType=VARCHAR},
        #{shipmentWarehouseCode,jdbcType=VARCHAR},
        #{sourceType,jdbcType=VARCHAR},
        #{sumQty,jdbcType=VARCHAR},
        #{typeCoode,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_abroad_warehouse_release_record(
        id,
        acreage,
        acreage_sum,
        actual_arrive_port_time,
        actual_completion_time,
        attribute1,
        attribute2,
        bar_num,
        bill_of_lading_num,
        box_num,
        car_num,
        consigner,
        container_num,
        create_time,
        creation_date,
        creator,
        cust_part,
        cust_po,
        customer_number,
        descriptions,
        ebs_site_id,
        enabled,
        estimated_arrive_port_time,
        estimated_completion_time,
        in_warehouse_time,
        instock_source,
        is_receive,
        item_code,
        kid,
        line_num,
        list_num,
        lot_number,
        modifier,
        modify_time,
        plant_code,
        remark,
        req,
        req_num,
        ship_company,
        shipment_locator_code,
        shipment_warehouse_code,
        source_type,
        sum_qty,
        type_coode,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.acreage,jdbcType=VARCHAR},
        #{entity.acreageSum,jdbcType=VARCHAR},
        #{entity.actualArrivePortTime,jdbcType=TIMESTAMP},
        #{entity.actualCompletionTime,jdbcType=TIMESTAMP},
        #{entity.attribute1,jdbcType=VARCHAR},
        #{entity.attribute2,jdbcType=VARCHAR},
        #{entity.barNum,jdbcType=VARCHAR},
        #{entity.billOfLadingNum,jdbcType=VARCHAR},
        #{entity.boxNum,jdbcType=VARCHAR},
        #{entity.carNum,jdbcType=VARCHAR},
        #{entity.consigner,jdbcType=VARCHAR},
        #{entity.containerNum,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.creationDate,jdbcType=TIMESTAMP},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.custPart,jdbcType=VARCHAR},
        #{entity.custPo,jdbcType=VARCHAR},
        #{entity.customerNumber,jdbcType=VARCHAR},
        #{entity.descriptions,jdbcType=VARCHAR},
        #{entity.ebsSiteId,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.estimatedArrivePortTime,jdbcType=TIMESTAMP},
        #{entity.estimatedCompletionTime,jdbcType=TIMESTAMP},
        #{entity.inWarehouseTime,jdbcType=TIMESTAMP},
        #{entity.instockSource,jdbcType=VARCHAR},
        #{entity.isReceive,jdbcType=VARCHAR},
        #{entity.itemCode,jdbcType=VARCHAR},
        #{entity.kid,jdbcType=VARCHAR},
        #{entity.lineNum,jdbcType=VARCHAR},
        #{entity.listNum,jdbcType=VARCHAR},
        #{entity.lotNumber,jdbcType=VARCHAR},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.plantCode,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.req,jdbcType=VARCHAR},
        #{entity.reqNum,jdbcType=VARCHAR},
        #{entity.shipCompany,jdbcType=VARCHAR},
        #{entity.shipmentLocatorCode,jdbcType=VARCHAR},
        #{entity.shipmentWarehouseCode,jdbcType=VARCHAR},
        #{entity.sourceType,jdbcType=VARCHAR},
        #{entity.sumQty,jdbcType=VARCHAR},
        #{entity.typeCoode,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_abroad_warehouse_release_record(
        id,
        acreage,
        acreage_sum,
        actual_arrive_port_time,
        actual_completion_time,
        attribute1,
        attribute2,
        bar_num,
        bill_of_lading_num,
        box_num,
        car_num,
        consigner,
        container_num,
        create_time,
        creation_date,
        creator,
        cust_part,
        cust_po,
        customer_number,
        descriptions,
        ebs_site_id,
        enabled,
        estimated_arrive_port_time,
        estimated_completion_time,
        in_warehouse_time,
        instock_source,
        is_receive,
        item_code,
        kid,
        line_num,
        list_num,
        lot_number,
        modifier,
        modify_time,
        plant_code,
        remark,
        req,
        req_num,
        ship_company,
        shipment_locator_code,
        shipment_warehouse_code,
        source_type,
        sum_qty,
        type_coode,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.acreage,jdbcType=VARCHAR},
        #{entity.acreageSum,jdbcType=VARCHAR},
        #{entity.actualArrivePortTime,jdbcType=TIMESTAMP},
        #{entity.actualCompletionTime,jdbcType=TIMESTAMP},
        #{entity.attribute1,jdbcType=VARCHAR},
        #{entity.attribute2,jdbcType=VARCHAR},
        #{entity.barNum,jdbcType=VARCHAR},
        #{entity.billOfLadingNum,jdbcType=VARCHAR},
        #{entity.boxNum,jdbcType=VARCHAR},
        #{entity.carNum,jdbcType=VARCHAR},
        #{entity.consigner,jdbcType=VARCHAR},
        #{entity.containerNum,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.creationDate,jdbcType=TIMESTAMP},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.custPart,jdbcType=VARCHAR},
        #{entity.custPo,jdbcType=VARCHAR},
        #{entity.customerNumber,jdbcType=VARCHAR},
        #{entity.descriptions,jdbcType=VARCHAR},
        #{entity.ebsSiteId,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.estimatedArrivePortTime,jdbcType=TIMESTAMP},
        #{entity.estimatedCompletionTime,jdbcType=TIMESTAMP},
        #{entity.inWarehouseTime,jdbcType=TIMESTAMP},
        #{entity.instockSource,jdbcType=VARCHAR},
        #{entity.isReceive,jdbcType=VARCHAR},
        #{entity.itemCode,jdbcType=VARCHAR},
        #{entity.kid,jdbcType=VARCHAR},
        #{entity.lineNum,jdbcType=VARCHAR},
        #{entity.listNum,jdbcType=VARCHAR},
        #{entity.lotNumber,jdbcType=VARCHAR},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.plantCode,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.req,jdbcType=VARCHAR},
        #{entity.reqNum,jdbcType=VARCHAR},
        #{entity.shipCompany,jdbcType=VARCHAR},
        #{entity.shipmentLocatorCode,jdbcType=VARCHAR},
        #{entity.shipmentWarehouseCode,jdbcType=VARCHAR},
        #{entity.sourceType,jdbcType=VARCHAR},
        #{entity.sumQty,jdbcType=VARCHAR},
        #{entity.typeCoode,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.warehouse.infrastructure.po.AbroadWarehouseReleaseRecordPO">
        update fdp_abroad_warehouse_release_record set
        acreage = #{acreage,jdbcType=VARCHAR},
        acreage_sum = #{acreageSum,jdbcType=VARCHAR},
        actual_arrive_port_time = #{actualArrivePortTime,jdbcType=TIMESTAMP},
        actual_completion_time = #{actualCompletionTime,jdbcType=TIMESTAMP},
        attribute1 = #{attribute1,jdbcType=VARCHAR},
        attribute2 = #{attribute2,jdbcType=VARCHAR},
        bar_num = #{barNum,jdbcType=VARCHAR},
        bill_of_lading_num = #{billOfLadingNum,jdbcType=VARCHAR},
        box_num = #{boxNum,jdbcType=VARCHAR},
        car_num = #{carNum,jdbcType=VARCHAR},
        consigner = #{consigner,jdbcType=VARCHAR},
        container_num = #{containerNum,jdbcType=VARCHAR},
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
        cust_part = #{custPart,jdbcType=VARCHAR},
        cust_po = #{custPo,jdbcType=VARCHAR},
        customer_number = #{customerNumber,jdbcType=VARCHAR},
        descriptions = #{descriptions,jdbcType=VARCHAR},
        ebs_site_id = #{ebsSiteId,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        estimated_arrive_port_time = #{estimatedArrivePortTime,jdbcType=TIMESTAMP},
        estimated_completion_time = #{estimatedCompletionTime,jdbcType=TIMESTAMP},
        in_warehouse_time = #{inWarehouseTime,jdbcType=TIMESTAMP},
        instock_source = #{instockSource,jdbcType=VARCHAR},
        is_receive = #{isReceive,jdbcType=VARCHAR},
        item_code = #{itemCode,jdbcType=VARCHAR},
        kid = #{kid,jdbcType=VARCHAR},
        line_num = #{lineNum,jdbcType=VARCHAR},
        list_num = #{listNum,jdbcType=VARCHAR},
        lot_number = #{lotNumber,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        plant_code = #{plantCode,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        req = #{req,jdbcType=VARCHAR},
        req_num = #{reqNum,jdbcType=VARCHAR},
        ship_company = #{shipCompany,jdbcType=VARCHAR},
        shipment_locator_code = #{shipmentLocatorCode,jdbcType=VARCHAR},
        shipment_warehouse_code = #{shipmentWarehouseCode,jdbcType=VARCHAR},
        source_type = #{sourceType,jdbcType=VARCHAR},
        sum_qty = #{sumQty,jdbcType=VARCHAR},
        type_coode = #{typeCoode,jdbcType=VARCHAR},
        version_value = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.warehouse.infrastructure.po.AbroadWarehouseReleaseRecordPO">
        update fdp_abroad_warehouse_release_record
        <set>
            <if test="item.acreage != null">
                acreage = #{item.acreage,jdbcType=VARCHAR},
            </if>
            <if test="item.acreageSum != null">
                acreage_sum = #{item.acreageSum,jdbcType=VARCHAR},
            </if>
            <if test="item.actualArrivePortTime != null">
                actual_arrive_port_time = #{item.actualArrivePortTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.actualCompletionTime != null">
                actual_completion_time = #{item.actualCompletionTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.attribute1 != null and item.attribute1 != ''">
                attribute1 = #{item.attribute1,jdbcType=VARCHAR},
            </if>
            <if test="item.attribute2 != null and item.attribute2 != ''">
                attribute2 = #{item.attribute2,jdbcType=VARCHAR},
            </if>
            <if test="item.barNum != null and item.barNum != ''">
                bar_num = #{item.barNum,jdbcType=VARCHAR},
            </if>
            <if test="item.billOfLadingNum != null and item.billOfLadingNum != ''">
                bill_of_lading_num = #{item.billOfLadingNum,jdbcType=VARCHAR},
            </if>
            <if test="item.boxNum != null and item.boxNum != ''">
                box_num = #{item.boxNum,jdbcType=VARCHAR},
            </if>
            <if test="item.carNum != null and item.carNum != ''">
                car_num = #{item.carNum,jdbcType=VARCHAR},
            </if>
            <if test="item.consigner != null and item.consigner != ''">
                consigner = #{item.consigner,jdbcType=VARCHAR},
            </if>
            <if test="item.containerNum != null and item.containerNum != ''">
                container_num = #{item.containerNum,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.creationDate != null">
                creation_date = #{item.creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.custPart != null and item.custPart != ''">
                cust_part = #{item.custPart,jdbcType=VARCHAR},
            </if>
            <if test="item.custPo != null and item.custPo != ''">
                cust_po = #{item.custPo,jdbcType=VARCHAR},
            </if>
            <if test="item.customerNumber != null and item.customerNumber != ''">
                customer_number = #{item.customerNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.descriptions != null and item.descriptions != ''">
                descriptions = #{item.descriptions,jdbcType=VARCHAR},
            </if>
            <if test="item.ebsSiteId != null and item.ebsSiteId != ''">
                ebs_site_id = #{item.ebsSiteId,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.estimatedArrivePortTime != null">
                estimated_arrive_port_time = #{item.estimatedArrivePortTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.estimatedCompletionTime != null">
                estimated_completion_time = #{item.estimatedCompletionTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.inWarehouseTime != null">
                in_warehouse_time = #{item.inWarehouseTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.instockSource != null and item.instockSource != ''">
                instock_source = #{item.instockSource,jdbcType=VARCHAR},
            </if>
            <if test="item.isReceive != null and item.isReceive != ''">
                is_receive = #{item.isReceive,jdbcType=VARCHAR},
            </if>
            <if test="item.itemCode != null and item.itemCode != ''">
                item_code = #{item.itemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.kid != null and item.kid != ''">
                kid = #{item.kid,jdbcType=VARCHAR},
            </if>
            <if test="item.lineNum != null and item.lineNum != ''">
                line_num = #{item.lineNum,jdbcType=VARCHAR},
            </if>
            <if test="item.listNum != null and item.listNum != ''">
                list_num = #{item.listNum,jdbcType=VARCHAR},
            </if>
            <if test="item.lotNumber != null and item.lotNumber != ''">
                lot_number = #{item.lotNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.plantCode != null and item.plantCode != ''">
                plant_code = #{item.plantCode,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.req != null and item.req != ''">
                req = #{item.req,jdbcType=VARCHAR},
            </if>
            <if test="item.reqNum != null and item.reqNum != ''">
                req_num = #{item.reqNum,jdbcType=VARCHAR},
            </if>
            <if test="item.shipCompany != null and item.shipCompany != ''">
                ship_company = #{item.shipCompany,jdbcType=VARCHAR},
            </if>
            <if test="item.shipmentLocatorCode != null and item.shipmentLocatorCode != ''">
                shipment_locator_code = #{item.shipmentLocatorCode,jdbcType=VARCHAR},
            </if>
            <if test="item.shipmentWarehouseCode != null and item.shipmentWarehouseCode != ''">
                shipment_warehouse_code = #{item.shipmentWarehouseCode,jdbcType=VARCHAR},
            </if>
            <if test="item.sourceType != null and item.sourceType != ''">
                source_type = #{item.sourceType,jdbcType=VARCHAR},
            </if>
            <if test="item.sumQty != null">
                sum_qty = #{item.sumQty,jdbcType=VARCHAR},
            </if>
            <if test="item.typeCoode != null and item.typeCoode != ''">
                type_coode = #{item.typeCoode,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_abroad_warehouse_release_record
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="acreage = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.acreage,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="acreage_sum = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.acreageSum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="actual_arrive_port_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.actualArrivePortTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="actual_completion_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.actualCompletionTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="attribute1 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.attribute1,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="attribute2 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.attribute2,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bar_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.barNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bill_of_lading_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.billOfLadingNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="box_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.boxNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="car_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.carNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="consigner = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.consigner,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="container_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.containerNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="creation_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creationDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cust_part = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.custPart,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cust_po = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.custPo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="customer_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.customerNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="descriptions = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.descriptions,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ebs_site_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ebsSiteId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="estimated_arrive_port_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.estimatedArrivePortTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="estimated_completion_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.estimatedCompletionTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="in_warehouse_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inWarehouseTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="instock_source = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.instockSource,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="is_receive = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.isReceive,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="item_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.itemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="kid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.kid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="line_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lineNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="list_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.listNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="lot_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lotNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="plant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.plantCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="req = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.req,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="req_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.reqNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ship_company = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.shipCompany,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="shipment_locator_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.shipmentLocatorCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="shipment_warehouse_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.shipmentWarehouseCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="source_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sourceType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sum_qty = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sumQty,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="type_coode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.typeCoode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update fdp_abroad_warehouse_release_record 
        <set>
            <if test="item.acreage != null">
                acreage = #{item.acreage,jdbcType=VARCHAR},
            </if>
            <if test="item.acreageSum != null">
                acreage_sum = #{item.acreageSum,jdbcType=VARCHAR},
            </if>
            <if test="item.actualArrivePortTime != null">
                actual_arrive_port_time = #{item.actualArrivePortTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.actualCompletionTime != null">
                actual_completion_time = #{item.actualCompletionTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.attribute1 != null and item.attribute1 != ''">
                attribute1 = #{item.attribute1,jdbcType=VARCHAR},
            </if>
            <if test="item.attribute2 != null and item.attribute2 != ''">
                attribute2 = #{item.attribute2,jdbcType=VARCHAR},
            </if>
            <if test="item.barNum != null and item.barNum != ''">
                bar_num = #{item.barNum,jdbcType=VARCHAR},
            </if>
            <if test="item.billOfLadingNum != null and item.billOfLadingNum != ''">
                bill_of_lading_num = #{item.billOfLadingNum,jdbcType=VARCHAR},
            </if>
            <if test="item.boxNum != null and item.boxNum != ''">
                box_num = #{item.boxNum,jdbcType=VARCHAR},
            </if>
            <if test="item.carNum != null and item.carNum != ''">
                car_num = #{item.carNum,jdbcType=VARCHAR},
            </if>
            <if test="item.consigner != null and item.consigner != ''">
                consigner = #{item.consigner,jdbcType=VARCHAR},
            </if>
            <if test="item.containerNum != null and item.containerNum != ''">
                container_num = #{item.containerNum,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.creationDate != null">
                creation_date = #{item.creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.custPart != null and item.custPart != ''">
                cust_part = #{item.custPart,jdbcType=VARCHAR},
            </if>
            <if test="item.custPo != null and item.custPo != ''">
                cust_po = #{item.custPo,jdbcType=VARCHAR},
            </if>
            <if test="item.customerNumber != null and item.customerNumber != ''">
                customer_number = #{item.customerNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.descriptions != null and item.descriptions != ''">
                descriptions = #{item.descriptions,jdbcType=VARCHAR},
            </if>
            <if test="item.ebsSiteId != null and item.ebsSiteId != ''">
                ebs_site_id = #{item.ebsSiteId,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.estimatedArrivePortTime != null">
                estimated_arrive_port_time = #{item.estimatedArrivePortTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.estimatedCompletionTime != null">
                estimated_completion_time = #{item.estimatedCompletionTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.inWarehouseTime != null">
                in_warehouse_time = #{item.inWarehouseTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.instockSource != null and item.instockSource != ''">
                instock_source = #{item.instockSource,jdbcType=VARCHAR},
            </if>
            <if test="item.isReceive != null and item.isReceive != ''">
                is_receive = #{item.isReceive,jdbcType=VARCHAR},
            </if>
            <if test="item.itemCode != null and item.itemCode != ''">
                item_code = #{item.itemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.kid != null and item.kid != ''">
                kid = #{item.kid,jdbcType=VARCHAR},
            </if>
            <if test="item.lineNum != null and item.lineNum != ''">
                line_num = #{item.lineNum,jdbcType=VARCHAR},
            </if>
            <if test="item.listNum != null and item.listNum != ''">
                list_num = #{item.listNum,jdbcType=VARCHAR},
            </if>
            <if test="item.lotNumber != null and item.lotNumber != ''">
                lot_number = #{item.lotNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.plantCode != null and item.plantCode != ''">
                plant_code = #{item.plantCode,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.req != null and item.req != ''">
                req = #{item.req,jdbcType=VARCHAR},
            </if>
            <if test="item.reqNum != null and item.reqNum != ''">
                req_num = #{item.reqNum,jdbcType=VARCHAR},
            </if>
            <if test="item.shipCompany != null and item.shipCompany != ''">
                ship_company = #{item.shipCompany,jdbcType=VARCHAR},
            </if>
            <if test="item.shipmentLocatorCode != null and item.shipmentLocatorCode != ''">
                shipment_locator_code = #{item.shipmentLocatorCode,jdbcType=VARCHAR},
            </if>
            <if test="item.shipmentWarehouseCode != null and item.shipmentWarehouseCode != ''">
                shipment_warehouse_code = #{item.shipmentWarehouseCode,jdbcType=VARCHAR},
            </if>
            <if test="item.sourceType != null and item.sourceType != ''">
                source_type = #{item.sourceType,jdbcType=VARCHAR},
            </if>
            <if test="item.sumQty != null">
                sum_qty = #{item.sumQty,jdbcType=VARCHAR},
            </if>
            <if test="item.typeCoode != null and item.typeCoode != ''">
                type_coode = #{item.typeCoode,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from fdp_abroad_warehouse_release_record where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_abroad_warehouse_release_record where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    
    <!-- 中转库在途确认 -->
    <select id="selectNotReceiveEixtStatistics"
            resultType="com.yhl.scp.dfp.warehouse.vo.WarehouseReceiveCheckExitVO">
        SELECT
        	m.oem_code AS oemCode,
			r.list_num listNum,
			r.item_code AS itemCode,
			r.shipment_locator_code shipmentLocatorCode,
			r.container_num containerNum,
			MAX(r.creation_date) creationDate,
			SUM(r.sum_qty) sumQty
		FROM
			fdp_abroad_warehouse_release_record r
			LEFT JOIN v_oem_transfer_target_stock_location_map m ON r.shipment_locator_code = m.location 
		WHERE
			r.sum_qty IS NOT NULL 
			AND r.is_receive = 'N'
			<if test="params.oemCodeList != null and params.oemCodeList.size() > 0">
                and m.oem_code in
                <foreach collection="params.oemCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productCodeList != null and params.productCodeList.size() > 0">
                and r.item_code in
                <foreach collection="params.productCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.listNum != null and params.listNum != ''">
                and r.list_num like concat('%', #{params.listNum}, '%')
            </if>
            <if test="params.itemCode != null and params.itemCode != ''">
                and r.item_code like concat('%', #{params.itemCode}, '%')
            </if>
            <if test="params.shipmentLocatorCode != null and params.shipmentLocatorCode != ''">
                and r.shipment_locator_code like concat('%', #{params.shipmentLocatorCode}, '%')
            </if>
            <if test="params.containerNum != null and params.containerNum != ''">
                and r.container_num like concat('%', #{params.containerNum}, '%')
            </if>
            <if test="params.containerNumList != null and params.containerNumList.size() > 0">
                and r.container_num in
                <foreach collection="params.containerNumList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
			GROUP BY
			m.oem_code, 
			r.list_num,
			r.item_code,
			r.shipment_locator_code,
			r.container_num
    </select>
    
    <select id="queryContainerNumdropdown" resultType="com.yhl.platform.common.LabelValue">
        SELECT DISTINCT
			container_num AS `value`,
			container_num AS `label` 
		FROM
			fdp_abroad_warehouse_release_record 
		WHERE
			container_num IS NOT NULL 
			AND container_num <![CDATA[ != ]]> ''
    </select>
</mapper>
