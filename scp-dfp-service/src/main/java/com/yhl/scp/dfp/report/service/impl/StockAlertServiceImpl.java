package com.yhl.scp.dfp.report.service.impl;

import java.io.BufferedOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.excel.EasyExcelFactory;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.yhl.scp.biz.common.excel.SheetStyleHandler;
import com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum;
import com.yhl.scp.dfp.utils.EasyExcelUtil;
import lombok.SneakyThrows;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.stereotype.Service;

import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.dfp.clean.infrastructure.dao.CleanDemandDataDetailDao;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataDetailVO;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanCalcReportService;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanCalcReportVO;
import com.yhl.scp.dfp.oem.service.OemInventorySubmissionExitService;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.service.OemStockPointMapService;
import com.yhl.scp.dfp.oem.service.OemTransportTimeService;
import com.yhl.scp.dfp.report.infrastructure.dao.StockAlertDao;
import com.yhl.scp.dfp.report.service.StockAlertService;
import com.yhl.scp.dfp.report.vo.StockAlertVO;
import com.yhl.scp.dfp.stock.service.InventoryBatchDetailService;
import com.yhl.scp.dfp.stock.service.InventoryRecieveConfirmationService;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseToWarehouseService;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.NewIpsFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mds.feign.NewMdsFeign;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;

/**
 * <code>StockAlertServiceImpl</code>
 * <p>
 * StockAlertServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-12 10:47:11
 */
@Service
public class StockAlertServiceImpl implements StockAlertService {

    @Resource
    private StockAlertDao stockAlertDao;

    @Resource
    private CleanDemandDataDetailDao cleanDemandDataDetailDao;

    @Resource
    private InventoryBatchDetailService inventoryBatchDetailService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private MpsFeign mpsFeign;
    @Resource
    protected NewIpsFeign newIpsFeign;

    @Resource
    protected OemService oemService;

    @Resource
    protected OemStockPointMapService oemStockPointMapService;

    @Resource
    protected WarehouseReleaseToWarehouseService warehouseReleaseToWarehouseService;

    @Resource
    protected OemTransportTimeService oemTransportTimeService;

    @Resource
    protected InventoryRecieveConfirmationService inventoryRecieveConfirmationService;

    @Resource
    protected OemInventorySubmissionExitService oemInventorySubmissionExitService;

    @Resource
    protected DeliveryPlanCalcReportService deliveryPlanCalcReportService;


    public static final String COLOUR_YELLOW = "YELLOW";

    public static final String COLOUR_RED = "RED";

    public static final String COLOUR_ORANGE = "ORANGE";

    public static final String DATE_FORMAT = "yyyy/MM/dd";

    /**
     * 颜色映射
     */
    private static final Map<String, Short> COLOR_MAPPING = ImmutableMap.of(COLOUR_RED, IndexedColors.RED.getIndex(),
            COLOUR_ORANGE, IndexedColors.ORANGE.getIndex(), COLOUR_YELLOW, IndexedColors.YELLOW.getIndex());

    @Override
    public List<StockAlertVO> selectAll() {
        return stockAlertDao.selectAll();
    }

    @Override
    public List<StockAlertVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageMethod.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    public List<StockAlertVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        String scenario = SystemHolder.getScenario();
        List<StockAlertVO> dataList = stockAlertDao.selectByCondition(sortParam, queryCriteriaParam);
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        Map<String, BigDecimal> quantityMap = stockAlertDao.selectStatistics(sortParam, queryCriteriaParam).stream()
                .collect(Collectors.toMap(x -> String.join(Constants.DELIMITER, x.getDemandCategory(),
                        x.getOemCode(), x.getProductCode(), x.getVehicleModelCode(), x.getVehicleModelCode(),
                        x.getDemandDate()), StockAlertVO::getDemandQty, (v1, v2) -> v1));
        Date date = new Date();
        String date0 = DateUtils.dateToString(date);
        String date1 = DateUtils.dateToString(DateUtils.moveCalendar(date, Calendar.DAY_OF_YEAR, 1));
        String date2 = DateUtils.dateToString(DateUtils.moveCalendar(date, Calendar.DAY_OF_YEAR, 2));
        List<String> productCodes = dataList.stream().map(StockAlertVO::getProductCode)
                .distinct().collect(Collectors.toList());
        List<String> oemCodes = dataList.stream().map(StockAlertVO::getOemCode)
                .distinct().collect(Collectors.toList());
        CompletableFuture<Map<String, BigDecimal>> fgStockMapFuture = getFgStockMapFuture(productCodes, scenario);
        CompletableFuture<Map<String, BigDecimal>> demandQtyMapFuture = getDemandQtyMapFuture(productCodes, scenario);

        //获取在途数量，中转库库存数量
        List<DeliveryPlanCalcReportVO> deliveryPlanCalcReportList = deliveryPlanCalcReportService
        		.selectByParams(ImmutableMap.of("oemCodes", oemCodes, "productCodes" , productCodes));
        Map<String, DeliveryPlanCalcReportVO> deliveryPlanCalcReportMap = deliveryPlanCalcReportList.stream()
        		.collect(Collectors.toMap(x -> String.join(Constants.DELIMITER, x.getOemCode(),
                                x.getProductCode()), e-> e,(v1, v2) -> v1));

        CompletableFuture.allOf(fgStockMapFuture, demandQtyMapFuture);
        Map<String, BigDecimal> fgStockMap = fgStockMapFuture.join();
        Map<String, BigDecimal> demandQtyMap = demandQtyMapFuture.join();

        for (StockAlertVO stockAlertVO : dataList) {
            String demandCategory = stockAlertVO.getDemandCategory();
            String oemCode = stockAlertVO.getOemCode();
            String productCode = stockAlertVO.getProductCode();
            String vehicleModelCode = stockAlertVO.getVehicleModelCode();
            // 1.设置库存供应
            String unionKey = StringUtils.join(Constants.DELIMITER, oemCode, productCode);
            stockAlertVO.setFgStock(fgStockMap.getOrDefault(productCode, BigDecimal.ZERO));
            //处理在途数量，中转库库存数量
            String oemProductKey = String.join(Constants.DELIMITER, oemCode, productCode);
            DeliveryPlanCalcReportVO deliveryPlanCalcReportVO = deliveryPlanCalcReportMap.get(oemProductKey);
            BigDecimal transportingQty = BigDecimal.ZERO;
            BigDecimal transitStock = BigDecimal.ZERO;
            if(deliveryPlanCalcReportVO != null) {
            	transportingQty = BigDecimal.valueOf(deliveryPlanCalcReportVO.getReceive());
            	transitStock = BigDecimal.valueOf(deliveryPlanCalcReportVO.getOpeningInventory());
            }
            stockAlertVO.setTransportingQty(transportingQty);
            stockAlertVO.setTransitStock(transitStock);
            // 2.设置发货量
            String key0 = String.join(Constants.DELIMITER, demandCategory, oemCode, productCode, vehicleModelCode,
                    vehicleModelCode, date0);
            stockAlertVO.setDeliveryQty0(quantityMap.get(key0));
            String key1 = String.join(Constants.DELIMITER, demandCategory, oemCode, productCode, vehicleModelCode,
                    vehicleModelCode, date1);
            stockAlertVO.setDeliveryQty1(quantityMap.get(key1));
            String key2 = String.join(Constants.DELIMITER, demandCategory, oemCode, productCode, vehicleModelCode,
                    vehicleModelCode, date2);
            stockAlertVO.setDeliveryQty2(quantityMap.get(key2));
            // 3.设置需求量
            stockAlertVO.setDemandQty0(demandQtyMap.getOrDefault(String.join(Constants.DELIMITER, demandCategory,
                    oemCode, productCode, date0), BigDecimal.ZERO));
            stockAlertVO.setDemandQty1(demandQtyMap.getOrDefault(String.join(Constants.DELIMITER, demandCategory,
                    oemCode, productCode, date1), BigDecimal.ZERO));
            stockAlertVO.setDemandQty2(demandQtyMap.getOrDefault(String.join(Constants.DELIMITER, demandCategory,
                    oemCode, productCode, date2), BigDecimal.ZERO));
            // 4.设置颜色
            colourProcess(stockAlertVO);
        }
        return dataList;
    }

    /**
     * 获取并组装成品库存
     *
     * @param productCodes 物品代码列表
     * @param scenario     场景
     * @return java.util.Map<java.lang.String,java.math.BigDecimal>
     */
    private CompletableFuture<Map<String, BigDecimal>> getFgStockMapFuture(List<String> productCodes, String scenario) {
        return CompletableFuture.supplyAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            Map<String, BigDecimal> result = new HashMap<>();
            List<InventoryBatchDetailVO> inventoryBatchDetails =
                    inventoryBatchDetailService.selectRealtimeInventoryByProductCodes(productCodes, StockPointTypeEnum.BC.getCode());
            if (CollectionUtils.isEmpty(inventoryBatchDetails)) {
                return result;
            }
            List<String> spaceList = inventoryBatchDetails.stream().map(InventoryBatchDetailVO::getFreightSpace)
                    .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(spaceList)) {
                return result;
            }
            Map<String, SubInventoryCargoLocationVO> cargoLocationMap =
                    mpsFeign.queryByFreightSpaces(scenario, spaceList, StockPointTypeEnum.BC.getCode()).stream()
                            .collect(Collectors.toMap(SubInventoryCargoLocationVO::getFreightSpaceCode,
                                    Function.identity(), (v1, v2) -> v1));
            if (MapUtils.isEmpty(cargoLocationMap)) {
                return result;
            }
            List<String> saleOrganizations = newMdsFeign.selectStockPointByParams(scenario, ImmutableMap
                    .of("stockPointType", StockPointTypeEnum.BC.getCode())).stream().filter(e ->
                    StringUtils.isNotEmpty(e.getOrganizeType())
                            && StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                    .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
            String subInventory = getSubInventory(scenario);
            Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailMap =
                    inventoryBatchDetails.stream().filter(t ->
                            StringUtils.isEmpty(t.getOperationCode())
                                    && saleOrganizations.contains(t.getStockPointCode())
                                    && subInventory.equals(t.getSubinventory())).collect(Collectors
                            .groupingBy(InventoryBatchDetailVO::getProductCode));
            for (Map.Entry<String, List<InventoryBatchDetailVO>> entry : inventoryBatchDetailMap.entrySet()) {
                String productCode = entry.getKey();
                BigDecimal fgStock = entry.getValue().stream().filter(p -> {
                    String freightSpace = p.getFreightSpace();
                    return null != cargoLocationMap.get(freightSpace);
                }).map(t -> new BigDecimal(t.getCurrentQuantity()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                result.put(productCode, fgStock);
            }
            DynamicDataSourceContextHolder.clearDataSource();
            return result;
        });
    }

    /**
     * 获取并组装需求Map
     *
     * @param productCodes 物品代码列表
     * @param scenario     场景
     * @return java.util.Map<java.lang.String, java.math.BigDecimal>
     */
    private CompletableFuture<Map<String, BigDecimal>> getDemandQtyMapFuture(List<String> productCodes, String scenario) {
        return CompletableFuture.supplyAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            Map<String, BigDecimal> result = new HashMap<>();
            if (CollectionUtils.isEmpty(productCodes)) {
                return result;
            }
            Map<String, BigDecimal> resultMap = cleanDemandDataDetailDao.selectDetailByParams(ImmutableMap
                    .of("productCodes", productCodes)).stream().filter(x ->
                    x.getDemandQuantity() != null
                            && x.getDemandTime() != null
                            && StringUtils.isNotBlank(x.getDemandCategory())
                            && StringUtils.isNotBlank(x.getOemCode())
                            && StringUtils.isNotBlank(x.getProductCode())).collect(Collectors.toMap(x ->
                    String.join(Constants.DELIMITER, x.getDemandCategory(), x.getOemCode(), x.getProductCode(),
                            DateUtils.dateToString(x.getDemandTime())), CleanDemandDataDetailVO::getDemandQuantity,
                    (v1, v2) -> v2));
            DynamicDataSourceContextHolder.clearDataSource();
            return resultMap;
        });
    }

    /**
     * 颜色处理
     *
     * @param stockAlertVO 库存预警结果对象
     */
    private void colourProcess(StockAlertVO stockAlertVO) {
        /*
         * 中转库库存不满足当日需求的为黄色，中转库+在途不满足当日需求的用深橙色，中转库+在途+成品库不满足当日需求用红色，
         * 如果第一天不满足，第二、第三天也为红色，如果第一天中转库+在途不满足，但是+成品库满足，那么用中转库+在途+成品库 - 第一日需求后，
         * 再减第二日需求看是否满足，如果满足为深橙色，不满足为红色，如果第一天中转库不满足，中转库+在途满足，则用中转库+在途 -第一日需求后，
         * 再减第二日需求看是否满足，如果满足为黄色，如果不满足，则用中转库+在途 -第一日需求+成品库库存后，
         * 再减第二日需求，如果满足为深橙色，如果不满足红色，第三天同样做此处理。
         */
        BigDecimal transitStock = stockAlertVO.getTransitStock() != null
                ? stockAlertVO.getTransitStock() : BigDecimal.ZERO;
        BigDecimal transportingQty = stockAlertVO.getTransportingQty() != null
                ? stockAlertVO.getTransportingQty() : BigDecimal.ZERO;
        BigDecimal fgStock = stockAlertVO.getFgStock() != null ? stockAlertVO.getFgStock() : BigDecimal.ZERO;

        BigDecimal demand0 = stockAlertVO.getDemandQty0() != null ? stockAlertVO.getDemandQty0() : BigDecimal.ZERO;
        BigDecimal demand1 = stockAlertVO.getDemandQty1() != null ? stockAlertVO.getDemandQty1() : BigDecimal.ZERO;
        BigDecimal demand2 = stockAlertVO.getDemandQty2() != null ? stockAlertVO.getDemandQty2() : BigDecimal.ZERO;

        // 计算不同组合的库存
        BigDecimal transitPlusTransport = transitStock.add(transportingQty);
        BigDecimal transitPlusTransportPlusFG = transitPlusTransport.add(fgStock);

        // 处理第0天
        if (transitStock.compareTo(demand0) < 0) {
            // 中转库不满足当日需求
            stockAlertVO.setDay0Colour(COLOUR_YELLOW);
        }
        if (transitPlusTransport.compareTo(demand0) < 0) {
            // 中转库+在途不满足当日需求
            stockAlertVO.setDay0Colour(COLOUR_ORANGE);
        }
        if (transitPlusTransportPlusFG.compareTo(demand0) < 0) {
            // 中转库+在途+成品库不满足当日需求
            stockAlertVO.setDay0Colour(COLOUR_RED);
        }

        // 如果第0天是红色（所有库存都不满足当日需求），则第1天和第2天也都是红色
        if (COLOUR_RED.equals(stockAlertVO.getDay0Colour())) {
            stockAlertVO.setDay1Colour(COLOUR_RED);
            stockAlertVO.setDay2Colour(COLOUR_RED);
            return;
        }

        // 计算第0天后的剩余库存
        BigDecimal transitAfterDay0 = transitStock.subtract(demand0);
        if (transitAfterDay0.compareTo(BigDecimal.ZERO) < 0) {
            transitAfterDay0 = BigDecimal.ZERO;
        }

        BigDecimal transitPlusTransportAfterDay0 = transitPlusTransport.subtract(demand0);
        if (transitPlusTransportAfterDay0.compareTo(BigDecimal.ZERO) < 0) {
            transitPlusTransportAfterDay0 = BigDecimal.ZERO;
        }

        BigDecimal remainingAfterDay0 = transitPlusTransportPlusFG.subtract(demand0);
        if (remainingAfterDay0.compareTo(BigDecimal.ZERO) < 0) {
            remainingAfterDay0 = BigDecimal.ZERO;
        }

        // 处理第1天
        if (COLOUR_ORANGE.equals(stockAlertVO.getDay0Colour())) {
            // 第0天是深橙色情况（中转库+在途不满足，但加上成品库满足）
            if (remainingAfterDay0.compareTo(demand1) >= 0) {
                // 剩余库存满足第1天需求
                stockAlertVO.setDay1Colour(COLOUR_ORANGE);
            } else {
                // 剩余库存不满足第1天需求
                stockAlertVO.setDay1Colour(COLOUR_RED);
            }
        } else if (COLOUR_YELLOW.equals(stockAlertVO.getDay0Colour())) {
            // 第0天是黄色情况（中转库不满足，但中转库+在途满足）
            if (transitPlusTransportAfterDay0.compareTo(demand1) >= 0) {
                // 中转库+在途的剩余满足第1天需求
                stockAlertVO.setDay1Colour(COLOUR_YELLOW);
            } else if (remainingAfterDay0.compareTo(demand1) >= 0) {
                // 中转库+在途的剩余不满足第1天需求，但加上成品库满足
                stockAlertVO.setDay1Colour(COLOUR_ORANGE);
            } else {
                // 所有库存都不足以满足第1天需求
                stockAlertVO.setDay1Colour(COLOUR_RED);
            }
        } else {
            // 第0天没有颜色（所有库存都足够）
            if (transitAfterDay0.compareTo(demand1) >= 0) {
                // 中转库剩余满足第1天需求
                stockAlertVO.setDay1Colour(null);
            } else if (transitPlusTransportAfterDay0.compareTo(demand1) >= 0) {
                // 中转库剩余不满足，但中转库+在途剩余满足第1天需求
                stockAlertVO.setDay1Colour(COLOUR_YELLOW);
            } else if (remainingAfterDay0.compareTo(demand1) >= 0) {
                // 中转库+在途剩余不满足，但加上成品库满足第1天需求
                stockAlertVO.setDay1Colour(COLOUR_ORANGE);
            } else {
                // 所有库存都不足以满足第1天需求
                stockAlertVO.setDay1Colour(COLOUR_RED);
            }
        }

        // 如果第1天是红色，第2天也是红色
        if (COLOUR_RED.equals(stockAlertVO.getDay1Colour())) {
            stockAlertVO.setDay2Colour(COLOUR_RED);
            return;
        }

        // 计算第1天后的剩余库存
        BigDecimal remainingAfterDay1 = remainingAfterDay0.subtract(demand1);
        if (remainingAfterDay1.compareTo(BigDecimal.ZERO) < 0) {
            remainingAfterDay1 = BigDecimal.ZERO;
        }

        // 处理第2天，使用与第1天相同的逻辑
        if (remainingAfterDay1.compareTo(demand2) >= 0) {
            // 剩余库存满足第2天需求
            stockAlertVO.setDay2Colour(stockAlertVO.getDay1Colour());
        } else {
            // 剩余库存不满足第2天需求
            stockAlertVO.setDay2Colour(COLOUR_RED);
        }
    }

    /**
     * 获取子库存
     *
     * @param scenario 场景
     * @return java.lang.String
     */
    private String getSubInventory(String scenario) {
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange =
                newIpsFeign.getScenarioBusinessRange(scenario, "SUB_INVENTORY", "INTERNAL", null);
        return scenarioBusinessRange.getData().getRangeData();
    }

    @SneakyThrows
    @Override
    public void exportData(String sortParam, String queryCriteriaParam, HttpServletResponse response) {
        Date date = new Date();
        String date1 = DateUtils.dateToString(date, DATE_FORMAT);
        String date2 = DateUtils.dateToString(DateUtils.moveCalendar(date, Calendar.DAY_OF_YEAR, 1), DATE_FORMAT);
        String date3 = DateUtils.dateToString(DateUtils.moveCalendar(date, Calendar.DAY_OF_YEAR, 2), DATE_FORMAT);
        List<String> dateSequence = Lists.newArrayList(date1, date2, date3);
        List<List<String>> headers = initHeaders(dateSequence);
        List<List<Object>> dataList = new ArrayList<>();
        Map<String, Short> cellColorMap = new HashMap<>();
        // 查询库存预警数据
        List<StockAlertVO> stockAlerts = this.selectByCondition(sortParam, queryCriteriaParam);
        // 组装dataList和cellColorMap
        int rowIndex = 1;
        for (StockAlertVO item : stockAlerts) {
            List<Object> objects = getRowData(item);
            dataList.add(objects);
            putCellColorMap(cellColorMap, item, rowIndex);
            rowIndex++;
        }

        // 设置响应头
        EasyExcelUtil.initResponse(response, "库存预警报表");
        try (SheetStyleHandler handler = new SheetStyleHandler(null, null,
                cellColorMap, null);
             BufferedOutputStream outputStream = new BufferedOutputStream(response.getOutputStream())) {
            Map<Integer, Short> numberFormatMap = ImmutableMap.of(6, (short) 1,
                    7, (short) 1, 8, (short) 1,
                    9, (short) 1, 10, (short) 1,
                    11, (short) 1, 12, (short) 1,
                    13, (short) 1, 14, (short) 1);
            handler.setNumberFormatMap(numberFormatMap);
            // 添加条件样式
            handler.addCellColorCondition((cell, rowIdx, colIdx) ->
                    cellColorMap.containsKey(rowIdx + Constants.DELIMITER + colIdx));
            EasyExcelFactory.write(outputStream).head(headers).registerWriteHandler(handler)
                    .sheet("Sheet1").doWrite(dataList);
        }
    }

    /**
     * 获取表头
     *
     * @param dateSequence 日期序列
     * @return java.util.List<java.util.List<java.lang.String>>
     */
    private List<List<String>> initHeaders(List<String> dateSequence) {
        List<List<String>> headers = new ArrayList<>();
        headers.add(Lists.newArrayList("需求类型"));
        headers.add(Lists.newArrayList("主机厂编码"));
        headers.add(Lists.newArrayList("主机厂名称"));
        headers.add(Lists.newArrayList("内部车型代码"));
        headers.add(Lists.newArrayList("产品编码"));
        headers.add(Lists.newArrayList("产品名称"));
        headers.add(Lists.newArrayList("中转库库存"));
        headers.add(Lists.newArrayList("在途数量"));
        headers.add(Lists.newArrayList("成品库库存"));
        for (String date : dateSequence) {
            headers.add(Lists.newArrayList(date + "发货"));
        }
        for (String date : dateSequence) {
            headers.add(Lists.newArrayList(date + "需求"));
        }
        return headers;
    }

    /**
     * 获取数据行
     *
     * @param item 库存预警数据对象
     * @return java.util.List<java.lang.Object>
     */
    private static List<Object> getRowData(StockAlertVO item) {
        List<Object> rowData = new ArrayList<>();
        ProductionDemandTypeEnum demandCategoryEnum = ProductionDemandTypeEnum.valueOf(item.getDemandCategory());
        rowData.add(demandCategoryEnum.getDesc());
        rowData.add(item.getOemCode());
        rowData.add(item.getOemName());
        rowData.add(item.getVehicleModelCode());
        rowData.add(item.getProductCode());
        rowData.add(item.getProductName());
        if (Objects.nonNull(item.getTransitStock())) {
            rowData.add(item.getTransitStock().intValue());
        } else {
            rowData.add(null);
        }
        if (Objects.nonNull(item.getTransportingQty())) {
            rowData.add(item.getTransportingQty().intValue());
        } else {
            rowData.add(null);
        }
        if (Objects.nonNull(item.getFgStock())) {
            rowData.add(item.getFgStock().intValue());
        } else {
            rowData.add(null);
        }

        if (Objects.nonNull(item.getDeliveryQty0())) {
            rowData.add(item.getDeliveryQty0().intValue());
        } else {
            rowData.add(null);
        }
        if (Objects.nonNull(item.getDeliveryQty1())) {
            rowData.add(item.getDeliveryQty1().intValue());
        } else {
            rowData.add(null);
        }
        if (Objects.nonNull(item.getDeliveryQty2())) {
            rowData.add(item.getDeliveryQty2().intValue());
        } else {
            rowData.add(null);
        }

        if (Objects.nonNull(item.getDemandQty0())) {
            rowData.add(item.getDemandQty0().intValue());
        } else {
            rowData.add(null);
        }
        if (Objects.nonNull(item.getDemandQty1())) {
            rowData.add(item.getDemandQty1().intValue());
        } else {
            rowData.add(null);
        }
        if (Objects.nonNull(item.getDemandQty2())) {
            rowData.add(item.getDemandQty2().intValue());
        } else {
            rowData.add(null);
        }
        return rowData;
    }


    private void putCellColorMap(Map<String, Short> cellColorMap, StockAlertVO item, int rowIndex) {
        String day0Colour = item.getDay0Colour();
        if (StringUtils.isNotBlank(day0Colour) && COLOR_MAPPING.containsKey(day0Colour)) {
            cellColorMap.put(rowIndex + Constants.DELIMITER + 12, COLOR_MAPPING.get(day0Colour));
        }

        String day1Colour = item.getDay1Colour();
        if (StringUtils.isNotBlank(day1Colour) && COLOR_MAPPING.containsKey(day1Colour)) {
            cellColorMap.put(rowIndex + Constants.DELIMITER + 13, COLOR_MAPPING.get(day1Colour));
        }

        String day2Colour = item.getDay2Colour();
        if (StringUtils.isNotBlank(day2Colour) && COLOR_MAPPING.containsKey(day2Colour)) {
            cellColorMap.put(rowIndex + Constants.DELIMITER + 14, COLOR_MAPPING.get(day2Colour));
        }
    }

}