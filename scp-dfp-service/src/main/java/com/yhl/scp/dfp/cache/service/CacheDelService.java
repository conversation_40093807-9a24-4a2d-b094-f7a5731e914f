package com.yhl.scp.dfp.cache.service;

/**
 * DFP缓存删除服务接口
 */
public interface CacheDelService {
    
    /**
     * 清除库存相关缓存
     */
    void clearInventoryCache(String scenario);
    
    /**
     * 清除库存点缓存
     */
    void clearStockPointCache(String scenario);
    
    /**
     * 清除标准步骤缓存
     */
    void clearStandardStepCache(String scenario);
    
    /**
     * 清除需求发货生产报表缓存
     */
    void clearDemandDeliveryProductionReportCache();
    
    /**
     * 清除所有缓存
     */
    void clearAllCache();
}