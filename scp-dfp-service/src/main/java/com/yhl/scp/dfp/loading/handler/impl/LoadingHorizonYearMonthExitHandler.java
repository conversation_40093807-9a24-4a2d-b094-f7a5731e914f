package com.yhl.scp.dfp.loading.handler.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.JacksonUtils;
import com.yhl.scp.dfp.common.enums.GranularityEnum;
import com.yhl.scp.dfp.common.enums.TemplateMonthPatternEnum;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionDTO;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionDetailDTO;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionDetailExitDTO;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionExitDTO;
import com.yhl.scp.dfp.loading.handler.LoadingAbstractExitHandler;
import com.yhl.scp.dfp.loading.handler.LoadingDataExitHolder;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionDetailExitService;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionExitService;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailExitVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionExitVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionVO;
import com.yhl.scp.dfp.loading.vo.LoadingProductVO;
import com.yhl.scp.dfp.oem.vo.OemStockPointMapVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.origin.service.OriginDemandVersionService;
import com.yhl.scp.dfp.origin.vo.OriginDemandVersionVO;
import com.yhl.scp.ips.common.SystemHolder;

import lombok.extern.slf4j.Slf4j;

/**
 * <code>LoadingHorizonYearMonthHandler</code>
 * <p>
 * LoadingHorizonYearMonthHandler
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-20 13:59:42
 */
@Slf4j
@Component
public class LoadingHorizonYearMonthExitHandler extends LoadingAbstractExitHandler {

    @Resource
    private LoadingDemandSubmissionDetailExitService loadingDemandSubmissionDetailExitService;

    @Resource
    @Lazy
    private LoadingDemandSubmissionExitService loadingDemandSubmissionExitService;

    @Resource
    private OriginDemandVersionService originDemandVersionService;

    @Override
    protected String handleFileImport(LoadingDataExitHolder loadingDataExitHolder, Map<Integer, String> headers, List<Map<Integer, String>> data, Map<String, String> extMap) {
        Map<String, String> partEnabledMap = loadingDataExitHolder.getPartEnabledMap();
        Map<String, List<String>> productPartMap = loadingDataExitHolder.getProductPartMap();
        Map<String, LoadingDemandSubmissionExitVO> submissionExitVOMap = loadingDataExitHolder.getSubmissionExitVOMap();
        Map<String, List<LoadingDemandSubmissionDetailExitVO>> submissionDetailExitVOMap = loadingDataExitHolder.getSubmissionDetailExitVOMap();
        String originVersionId = extMap.get("originVersionId");
        String projectDemandType = extMap.get("projectDemandType");
        StringBuilder errMsg = new StringBuilder();
        // 行数据循环
        AtomicInteger rowNo = new AtomicInteger(1);
        List<LoadingDemandSubmissionDetailExitDTO> detailDTOS = Lists.newArrayList();
        List<LoadingDemandSubmissionExitVO> updateSubmissionVOs = Lists.newArrayList();
        List<LoadingDemandSubmissionExitDTO> createSubmissionVOs = Lists.newArrayList();
        List<String> deleteSubmissionIds = Lists.newArrayList();
        int successCount = 0, failCount = 0;
        for (Map<Integer, String> rowMap : data) {
            rowNo.incrementAndGet();
            String oemCode = "";
            String productCode = "";
            String partNumber = "";
            Map<String, Double> quantityMap = new HashMap<>();
            StringBuilder rowErrMsg = new StringBuilder();
            // 列数据循环
            for (int i = 1; i < headers.size() + 1; i++) {
                String columnValue = rowMap.get(i);
                if (1 == i) {
                    oemCode = columnValue;
                    continue;
                } else if (2 == i) {
                    productCode = columnValue;
                    continue;
                } else if (3 == i) {
                    partNumber = columnValue;
                    continue;
                }
                // 校验数据
                if (!validData(loadingDataExitHolder, oemCode, productCode, partNumber, rowNo.get(), rowErrMsg)) {
                    break;
                }
                if (StringUtils.equals(partEnabledMap.get(partNumber), "NO")) {
                    rowErrMsg.append(String.format("行数：%s", rowNo.get()));
                    rowErrMsg.append(String.format("，表头：%s", headers.get(i)));
                    rowErrMsg.append("，该零件未生效，无法导入\n");
                    break;
                }
                String header = headers.get(i);
                Double quantity = null;
                try {
                    if (StringUtils.isNotBlank(columnValue)) {
                        quantity = Double.valueOf(columnValue);
                    }
                } catch (Exception ex) {
                    rowErrMsg.append(String.format("行数：%s", rowNo.get()));
                    rowErrMsg.append(String.format("，表头：%s", headers.get(i)));
                    rowErrMsg.append("，内容非数值\n");
                    break;
                }
                if (Objects.nonNull(quantity)) {
                    Double mapQuantity = quantityMap.get(header);
                    if (Objects.nonNull(mapQuantity)) {
                        quantityMap.put(header, quantity + mapQuantity);
                    } else {
                        quantityMap.put(header, quantity);
                    }
                }
            }
            if (StringUtils.isNotBlank(rowErrMsg)) {
                failCount++;
                errMsg.append(rowErrMsg);
                errMsg.append("\n");
                continue;
            }
            // 标准化零件号
            String standardizedPartNumber = partNumber.replaceAll("[ ._-]", "");
            List<String> actualPartNumbers = productPartMap.getOrDefault(productCode, Lists.newArrayList());
            // 标准化实际零件号列表
            List<String> standardizedActualPartNumbers = actualPartNumbers.stream()
                    .map(actual -> actual.replaceAll("[ ._-]", ""))
                    .collect(Collectors.toList());
            String finalPartNumber = partNumber;
            if (standardizedActualPartNumbers.contains(standardizedPartNumber)) {
                int index = standardizedActualPartNumbers.indexOf(standardizedPartNumber);
                finalPartNumber = actualPartNumbers.get(index);
            }
            // 处理数据
            Map<String, Double> dataQuantityMap = handleData(originVersionId, quantityMap);
            if (MapUtils.isEmpty(dataQuantityMap)) {
                continue;
            }
            String submissionKey = String.join("_", oemCode, productCode);
            LoadingDemandSubmissionExitVO oldSubmissionExitVO = submissionExitVOMap.get(submissionKey);
            if(oldSubmissionExitVO == null) {
            	throw new BusinessException("主机厂【"+oemCode+"】,本产编码【"+productCode+"】装车需求提报数据维护异常!");
            }
            String submissionExitId = oldSubmissionExitVO.getId();
//            if (Objects.isNull(oldSubmissionVO)) {
//                submissionId = UUIDUtil.getUUID();
//                LoadingDemandSubmissionDTO submissionDTO = new LoadingDemandSubmissionDTO();
//                submissionDTO.setId(submissionId);
//                submissionDTO.setVersionId(originVersionId);
//                submissionDTO.setDemandCategory(projectDemandType);
//                submissionDTO.setOemCode(oemCode);
//                submissionDTO.setProductCode(productCode);
//                submissionDTO.setPartNumber(finalPartNumber);
//                submissionDTO.setImportTime(new Date());
//                createSubmissionVOs.add(submissionDTO);
//                submissionVOMap.put(submissionKey, getLoadingSubmissionVo(submissionDTO));
//            } else {
//                submissionId = oldSubmissionVO.getId();
//                if(!Objects.equals(projectDemandType, oldSubmissionVO.getDemandCategory())) {
//                	oldSubmissionVO.setDemandCategory(projectDemandType);
//                }
//                oldSubmissionVO.setLastImportTime(oldSubmissionVO.getImportTime());
//                oldSubmissionVO.setImportTime(new Date());
//                updateSubmissionVOs.add(oldSubmissionVO);
//            }
            //删除历史数据
//            loadingDemandSubmissionDetailService.deleteByVersionIdAndSubmissionType(submissionId, GranularityEnum.MONTH.getCode());
            deleteSubmissionIds.add(submissionExitId);
            List<LoadingDemandSubmissionDetailExitVO> oldSubmissionDetailExitList = submissionDetailExitVOMap
            		.getOrDefault(submissionExitId, new ArrayList<>());
            Map<String, LoadingDemandSubmissionDetailExitVO> oldSubmissionDetailExitMap = oldSubmissionDetailExitList.stream()
            		.collect(Collectors.toMap(LoadingDemandSubmissionDetailExitVO::getDemandTime, e->e,(v1, v2) -> v2));
            //插入数据
            for (Map.Entry<String, Double> entry : dataQuantityMap.entrySet()) {
                LoadingDemandSubmissionDetailExitDTO detailDTO = new LoadingDemandSubmissionDetailExitDTO();
                detailDTO.setSubmissionExitId(submissionExitId);
                detailDTO.setSubmissionType(GranularityEnum.MONTH.getCode());
                detailDTO.setDemandTime(entry.getKey());
                if (Objects.nonNull(entry.getValue())) {
                    detailDTO.setDemandQuantity(BigDecimal.valueOf(entry.getValue()));
                } else {
                	//如果导入数据的需求数量未维护，则原始需求时间：日期，需求数量，备注取原数据
                	LoadingDemandSubmissionDetailExitVO oldSubmissionDetailVO = oldSubmissionDetailExitMap.get(detailDTO.getDemandTime());
                	if(oldSubmissionDetailVO != null) {
                		detailDTO.setOriginDemandTime(oldSubmissionDetailVO.getOriginDemandTime());
                		detailDTO.setDemandQuantity(oldSubmissionDetailVO.getDemandQuantity());
                		detailDTO.setRemark(oldSubmissionDetailVO.getRemark());
                	}else {
                		detailDTO.setDemandQuantity(null);
                	}
                }
                detailDTOS.add(detailDTO);
            }
            successCount++;
        }
        //删除修改的详情数据
        if(CollectionUtils.isNotEmpty(deleteSubmissionIds)) {
        	loadingDemandSubmissionDetailExitService.deleteBySubmissionExitIdsAndSubmissionType(deleteSubmissionIds, GranularityEnum.MONTH.getCode());
        }
        if (CollectionUtils.isNotEmpty(detailDTOS)) {
            List<List<LoadingDemandSubmissionDetailExitDTO>> partition = Lists.partition(detailDTOS, 100);
            for (List<LoadingDemandSubmissionDetailExitDTO> detailDTOList : partition) {
                loadingDemandSubmissionDetailExitService.doCreateBatch(detailDTOList);
            }
        }
//        if (CollectionUtils.isNotEmpty(createSubmissionVOs)) {
//            loadingDemandSubmissionService.doCreateBatch(createSubmissionVOs);
//        }
//        if (CollectionUtils.isNotEmpty(updateSubmissionVOs)) {
//        	List<LoadingDemandSubmissionDTO> updateSubmissionDtos = LoadingDemandSubmissionConvertor.INSTANCE.vo2Dtos(updateSubmissionVOs);
//            loadingDemandSubmissionService.doUpdateBatch(updateSubmissionDtos);
//        }

        //目前yearMonth会覆盖day的错误原因，yearMonth和day的校验逻辑一样，所以错误原因应该也是一样的，后续可能需要修改
        extMap.put("successInfo", String.format("导入数量共%s条,成功导入%s条", data.size(), successCount));
        if (failCount > 0){
            extMap.put("errorInfo", String.format("失败%s条,失败信息:%s", failCount, errMsg));
        }

        return String.format("导入数量共%s条,成功导入%s条",data.size(),successCount)
                + (failCount>0?String.format(",失败%s条,失败信息:%s",failCount,errMsg):"");
        // 错误提示
        //Assert.isTrue(StringUtils.isBlank(errMsg.toString()), errMsg.toString());

    }

    private LoadingDemandSubmissionVO getLoadingSubmissionVo(LoadingDemandSubmissionDTO submissionDTO){
        LoadingDemandSubmissionVO loadingDemandSubmissionVO = new LoadingDemandSubmissionVO();
        loadingDemandSubmissionVO.setId(submissionDTO.getId());
        loadingDemandSubmissionVO.setVersionId(submissionDTO.getVersionId());
        loadingDemandSubmissionVO.setDemandCategory(submissionDTO.getDemandCategory());
        loadingDemandSubmissionVO.setOemCode(submissionDTO.getOemCode());
        loadingDemandSubmissionVO.setProductCode(submissionDTO.getProductCode());
        loadingDemandSubmissionVO.setPartNumber(submissionDTO.getPartNumber());
        return loadingDemandSubmissionVO;
    }

    @Override
    protected Boolean validData(LoadingDataExitHolder loadingDataExitHolder, String oemCode, String productCode, String partNumber, Integer rowIndex, StringBuilder errMsg) {
        Map<String, List<String>> productPartMap = loadingDataExitHolder.getProductPartMap();
        Map<String, List<OemVO>> oemMap = loadingDataExitHolder.getOemMap();
        List<OemStockPointMapVO> oemStockPointMapVOS = loadingDataExitHolder.getOemStockPointMapVOS();
        Map<String, List<String>> productCodeVehicleCodeMap = loadingDataExitHolder.getProductCodeVehicleCodeMap();
        List<LoadingProductVO> productStockPointVOS = loadingDataExitHolder.getProductStockPointVOS();
        log.info("LoadingHorizonYearMonthHandler-本厂编码：{}，零件号：{}", productCode, partNumber);
        if (StringUtils.isBlank(productCode) || StringUtils.isBlank(partNumber)) {
            errMsg.append(String.format("行数：%s", rowIndex));
            errMsg.append("，产品编码或零件号为空\n");
            return false;
        }
        if (MapUtils.isEmpty(productPartMap)) {
            errMsg.append(String.format("行数：%s", rowIndex));
            errMsg.append("，产品编码："+ productCode +",零件映射关系不存在\n");
            return false;
        }
        if (!oemMap.containsKey(oemCode)) {
            errMsg.append(String.format("行数：%s", rowIndex));
            errMsg.append("，无导入的主机厂编码，请确认导入的数据是否正确或者检查系统的主机厂主数据\n");
            return false;
        }
        List<String> actualPartNumbers = productPartMap.getOrDefault(productCode, Lists.newArrayList());
        // 标准化Excel中零件号，先移除Excel中零件号中的可能偶尔包含的十六进制代表不间断特殊的空格字符<0xa0>
        String standardizedPartNumber = partNumber.replaceAll("\u00A0", "").replaceAll("[ ._-]", "");
        log.info("excel 文件中的 partNumber :{}", standardizedPartNumber);
        // 标准化数据库实际零件号列表，先移除数据库实际零件号列表中的可能偶尔包含的十六进制代表不间断特殊的空格字符<0xa0>
        if (!actualPartNumbers.isEmpty()) {
            List<String> standardizedActualPartNumbers = actualPartNumbers.stream()
                    .map(actual -> actual.replaceAll("\u00A0", "").replaceAll("[ ._-]", ""))
                    .collect(Collectors.toList());
            log.info("database 数据库中的 partNumber :{}", JacksonUtils.toJson(standardizedActualPartNumbers));
            boolean isValid = standardizedActualPartNumbers.stream()
                    .anyMatch(actual -> actual.equalsIgnoreCase(standardizedPartNumber));
            if (!isValid) {
                errMsg.append(String.format("行数：%s", rowIndex));
                errMsg.append("，产品编码与零件号映射关系不存在\n");
                return false;
            }
        } else {
            errMsg.append(String.format("行数：%s", rowIndex));
            errMsg.append("，产品编码与零件号映射关系为空\n");
            return false;
        }

        if (CollectionUtils.isEmpty(oemStockPointMapVOS)) {
            errMsg.append(String.format("行数：%s", rowIndex));
            errMsg.append("，主机厂与库存点关系为空\n");
            return false;
        }

        String userId = SystemHolder.getUserId();
        log.info("userId：{}，productCode：{}", userId, productCode);

        List<LoadingProductVO> authEmpty = productStockPointVOS.stream().filter(p ->
                productCode.equals(p.getProductCode()) && (StringUtils.isNotBlank(p.getOrderPlanner())
                        && p.getOrderPlanner().contains(userId))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(authEmpty)) {
            errMsg.append(String.format("行数：%s", rowIndex));
            errMsg.append("，产品编码无权限\n");
            return false;
        }
        List<String> oemCodeList = productCodeVehicleCodeMap.get(productCode);
        if (CollectionUtils.isEmpty(oemCodeList) || !oemCodeList.contains(oemCode)) {
            errMsg.append(String.format("行数：%s", rowIndex));
            errMsg.append("，产品编码对应车型与主机厂关联为空,请检查\n");
            return false;
        }
        return true;
    }

    /**
     * 处理结果数据
     * 缺失数据中间补0，最后补null
     *
     * @param originVersionId 原始版本ID
     * @param quantityMap     数量MAP
     */
    @Override
    protected Map<String, Double> handleData(String originVersionId, Map<String, Double> quantityMap) {
        Map<String, Double> result = new HashMap<>();
        OriginDemandVersionVO demandVersionVO = originDemandVersionService.selectByPrimaryKey(originVersionId);
        if (Objects.isNull(demandVersionVO)) {
            return result;
        }
        String planPeriod = demandVersionVO.getPlanPeriod();
        Date currentDate = DateUtils.stringToDate(planPeriod, "yyyyMMdd");
        // 需求变更，需要导入12个月数据，从当月开始
        int period = 12;
        Date startDate = org.apache.commons.lang3.time.DateUtils.addMonths(currentDate, 0);
        Date endDate = org.apache.commons.lang3.time.DateUtils.addMonths(currentDate, period);
        //获取最后截止日期
        Date lastExistingDate = null;
        if (MapUtils.isNotEmpty(quantityMap)) {
            Optional<Date> optional = quantityMap.entrySet().stream()
                    .filter(x -> Objects.nonNull(x.getValue()))
                    .map(x -> DateUtils.stringToDate(x.getKey(), DateUtils.YEAR_MONTH))
                    .filter(x -> Objects.nonNull(x) && !x.after(endDate) && !x.before(startDate))
                    .max(Comparator.comparing(Date::getTime));
            if (optional.isPresent()) {
                lastExistingDate = optional.get();
            }
        }
        Set<String> dates = quantityMap.keySet();
        for (Date date = startDate; !date.after(endDate); date = org.apache.commons.lang3.time.DateUtils.addMonths(date, 1)) {
            String key = DateUtils.dateToString(date, DateUtils.YEAR_MONTH);
            if (quantityMap.containsKey(key)) {
                result.put(key, quantityMap.getOrDefault(key, null));
            } else {
                if (Objects.isNull(lastExistingDate)) {
                    result.put(key, null);
                } else {
                    //判断后面日期是否有数据，如果有数据则默认补0
                    boolean flag = hasLaterDate(dates, key);
                    if (flag) {
                        //后面日期存在数据则补充0
                        //result.put(key, 0.0);
                    	result.put(key, null);
                        continue;
                    }

                    // 如果是最后一个存在的日期之前，补0
                    if (date.before(lastExistingDate)) {
                        result.put(key, null);
                    } else {
                        // 最后一个存在的日期之后，补null
                        result.put(key, null);
                    }
                }
            }
        }
        return result;
    }

    @Override
    protected String getCommand() {
        return TemplateMonthPatternEnum.YYYY_MM.getCode();
    }

    public static boolean hasLaterDate(Set<String> dates, String date) {
        // 使用自定义的 DateTimeFormatter 来解析日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 将 date 转换为 LocalDate
        LocalDate targetDate = LocalDate.parse(date + "-01", formatter);

        for (String d : dates) {
            LocalDate currentDate = LocalDate.parse(d + "-01", formatter);
            if (currentDate.isAfter(targetDate)) {
                return true;
            }
        }
        return false;
    }

}