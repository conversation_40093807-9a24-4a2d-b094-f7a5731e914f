package com.yhl.scp.dfp.mq.receiver;

import com.yhl.scp.dfp.stock.service.RealtimeInventoryBatchDetailService;
import com.yhl.scp.ips.constant.MqConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class MqMessageReceiver {

    @Resource
    private RealtimeInventoryBatchDetailService realtimeInventoryBatchDetailService;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "${spring.profiles.active}."+ MqConstants.REALTIME_INVENTORY_SYNC_QUEUE, ignoreDeclarationExceptions = "true"),
            exchange = @Exchange(value = "${spring.profiles.active}."+MqConstants.BPIM_SYNC_EXCHANGE, ignoreDeclarationExceptions = "true", type = ExchangeTypes.TOPIC),
            key = "${spring.profiles.active}."+MqConstants.REALTIME_INVENTORY_SYNC_KEY
    ))
    public void receiveRealtimeInventorySyncMessage(String scenario) {
        log.info("接收到实时库存同步消息，场景: {}", scenario);
        try {
            realtimeInventoryBatchDetailService.doSync(scenario);
        } catch (Exception e) {
            log.error("处理实时库存同步消息失败", e);
        }
    }
}