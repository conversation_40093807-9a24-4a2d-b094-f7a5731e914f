package com.yhl.scp.dfp.cache.service.impl;

import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dfp.cache.constants.DfpCacheConstants;
import com.yhl.scp.dfp.cache.service.CacheGetService;
import com.yhl.scp.dfp.clean.infrastructure.dao.CleanDemandDataDetailDao;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataDetailVO;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanCalcReportDao;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanPublishedDao;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanCalcReportVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.dfp.report.dto.DemandDeliveryProductionDetailDTO;
import com.yhl.scp.dfp.report.vo.DemandDeliveryProductionVO;
import com.yhl.scp.dfp.demand.infrastructure.dao.DemandVersionDao;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseRecordService;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseToWarehouseService;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseVO;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mds.basic.routing.vo.StandardStepBasicVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.dfp.stock.infrastructure.dao.InventoryBatchDetailDao;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.yhl.scp.dfp.report.service.DemandDeliveryProductionService; // 导入 DemandDeliveryProductionService

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * DFP缓存获取服务实现
 */
@Slf4j
@Service
public class CacheGetServiceImpl implements CacheGetService {

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private InventoryBatchDetailDao inventoryBatchDetailDao;

    @Resource
    private MpsFeign mpsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private DemandVersionDao demandVersionDao;

    @Resource
    private DeliveryPlanCalcReportDao deliveryPlanCalcReportDao;

    @Resource
    private WarehouseReleaseRecordService warehouseReleaseRecordService;

    @Resource
    private WarehouseReleaseToWarehouseService warehouseReleaseToWarehouseService;

    @Resource
    private CleanDemandDataDetailDao cleanDemandDataDetailDao;

    @Resource
    private DeliveryPlanPublishedDao deliveryPlanPublishedDao;

    @Resource
    private DemandDeliveryProductionService demandDeliveryProductionService;

    @Resource
    private CacheGetService cacheGetService;

    @Override
    @Cacheable(value = DfpCacheConstants.INVENTORY_BATCH_DETAIL_LIST, key = "#scenario +  '_' + #productCodes.stream().sorted().collect(T(java.util.stream.Collectors).joining(','))")
    public List<InventoryBatchDetailVO> getInventoryBatchDetails(String scenario, List<String> productCodes) {
        if (CollectionUtils.isEmpty(productCodes)) {
            return new ArrayList<>();
        }

        log.info("查询库存数据，场景: {}, 产品数量: {}", scenario, productCodes.size());
        return inventoryBatchDetailDao.selectByProductCodes(productCodes, StockPointTypeEnum.BC.getCode());
    }

    @Override
    @Cacheable(value = DfpCacheConstants.NEW_STOCK_POINT_LIST, key = "#scenario")
    public List<NewStockPointVO> getNewStockPoints(String scenario) {
        log.info("查询库存点数据，场景: {}", scenario);
        return newMdsFeign.selectStockPointByParams(scenario,
                ImmutableMap.of("stockPointType", StockPointTypeEnum.BC.getCode()));
    }

    @Override
    @Cacheable(value = DfpCacheConstants.STANDARD_STEP_MAP, key = "#scenario")
    public Map<String, String> getStandardStepMap(String scenario) {
        log.info("查询标准步骤映射，场景: {}", scenario);
        List<StandardStepVO> standardStepVOS = newMdsFeign.selectStandardStepAll(scenario);
        return standardStepVOS.stream()
                .filter(vo -> vo.getStockPointCode() != null && vo.getStandardStepName() != null && vo.getStandardStepCode() != null)
                .collect(Collectors.toMap(
                        p -> p.getStockPointCode() + p.getStandardStepName(),
                        StandardStepBasicVO::getStandardStepCode,
                        (v1, v2) -> v1));
    }

    @Override
    @Cacheable(value = DfpCacheConstants.PLANNING_HORIZON, key = "#scenario")
    public PlanningHorizonVO getPlanningHorizon(String scenario) {
        log.info("查询计划展望期，场景: {}", scenario);
        return newMdsFeign.selectPlanningHorizon(scenario);
    }

    @Override
    @Cacheable(value = DfpCacheConstants.ROUTING_LIST, key = "#scenario + '_' + #productCodes.stream().sorted().collect(T(java.util.stream.Collectors).joining(','))")
    public List<RoutingVO> getRoutings(String scenario, List<String> productCodes) {
        log.info("查询工艺路线，场景: {}, 产品数量: {}", scenario, productCodes.size());
        return newMdsFeign.selectRoutingByParams(scenario, ImmutableMap.of("routingCodes", productCodes));
    }

    @Override
    @Cacheable(value = DfpCacheConstants.CARGO_LOCATION_LIST, key = "#scenario + '_' + #operations.stream().sorted().collect(T(java.util.stream.Collectors).joining(','))")
    public List<SubInventoryCargoLocationVO> getCargoLocations(String scenario, List<String> operations) {
        log.info("查询货位信息，场景: {}, 工序数量: {}", scenario, operations.size());
        return mpsFeign.queryByParams1(scenario, ImmutableMap.of("operations", operations));
    }

    @Override
    @Cacheable(value = DfpCacheConstants.BUSINESS_RANGE, key = "#scenario + '_' + #rangeType + '_' + #rangeSource + '_' + #stockPointId")
    public ScenarioBusinessRangeVO getBusinessRange(String scenario, String rangeType, String rangeSource, String stockPointId) {
        log.info("查询业务范围，场景: {}, 类型: {}, 来源: {}, 库存点: {}", scenario, rangeType, rangeSource, stockPointId);
        return ipsNewFeign.getScenarioBusinessRange(scenario, rangeType, rangeSource, stockPointId).getData();
    }

    @Override
    @Cacheable(value = DfpCacheConstants.LATEST_CLEAN_DEMAND_VERSION, key = "#businessType")
    public String getLatestCleanDemandVersionId(String businessType) {
        log.info("查询最新清洗后需求版本ID，业务类型: {}", businessType);
        return demandVersionDao.selectLatestVersionId(businessType);
    }

    @Override
    @Cacheable(value = DfpCacheConstants.DELIVERY_PLAN_CALC_REPORT_LIST, key = "#oemCodes.stream().sorted().collect(T(java.util.stream.Collectors).joining(',')) + '_' + #productCodes.stream().sorted().collect(T(java.util.stream.Collectors).joining(','))")
    public List<DeliveryPlanCalcReportVO> getDeliveryPlanCalcReports(List<String> oemCodes, List<String> productCodes) {
        log.info("查询发货计划计算报告，主机厂数量: {}, 产品数量: {}", oemCodes.size(), productCodes.size());
        return deliveryPlanCalcReportDao.selectVOByParams(ImmutableMap.of("oemCodes", oemCodes, "productCodes", productCodes));
    }

    @Override
    @Cacheable(value = DfpCacheConstants.WAREHOUSE_RELEASE_RECORD_LIST, key = "#saleOrganization + '_' + #subInventory + '_' + #startTime.getTime()")
    public List<WarehouseReleaseRecordVO> getWarehouseReleaseRecords(String saleOrganization, String subInventory, Date startTime) {
        log.info("查询发货记录，销售组织: {}, 子库存: {}, 开始时间: {}", saleOrganization, subInventory, startTime);
        return warehouseReleaseRecordService.selectByParams(ImmutableMap.of(
                "plantCode", saleOrganization,
                "attribute1", subInventory,
                "creationDateAfter", startTime
        ));
    }

    @Override
    @Cacheable(value = DfpCacheConstants.WAREHOUSE_RELEASE_TO_WAREHOUSE_LIST, key = "#saleOrganization + '_' + #subInventory + '_' + #startTime.getTime()")
    public List<WarehouseReleaseToWarehouseVO> getWarehouseReleaseToWarehouses(String saleOrganization, String subInventory, Date startTime) {
        log.info("查询仓库发货，销售组织: {}, 子库存: {}, 开始时间: {}", saleOrganization, subInventory, startTime);
        return warehouseReleaseToWarehouseService.selectByParams(ImmutableMap.of(
                "plantCode", saleOrganization,
                "attribute1", subInventory,
                "creationDateAfter", startTime
        ));
    }

    @Override
    @Cacheable(value = DfpCacheConstants.CLEAN_DEMAND_DATA_DETAIL_LIST, key = "#versionId + '_' + #startTime.getTime() + '_' + #endTime.getTime()")
    public List<CleanDemandDataDetailVO> getCleanDemandDataDetails(String versionId, Date startTime, Date endTime) {
        log.info("查询清洗后需求明细，版本ID: {}, 开始时间: {}, 结束时间: {}", versionId, startTime, endTime);
        return cleanDemandDataDetailDao.selectVOByParams(ImmutableMap.of(
                "versionId", versionId,
                "demandTimeStart", DateUtils.dateToString(startTime, DateUtils.COMMON_DATE_STR1),
                "demandTimeEnd", DateUtils.dateToString(endTime, DateUtils.COMMON_DATE_STR1)
        ));
    }

    @Override
    @Cacheable(value = DfpCacheConstants.DELIVERY_PLAN_PUBLISHED_LIST, key = "#productCodes.stream().sorted().collect(T(java.util.stream.Collectors).joining(',')) + '_' + #startTime.getTime() + '_' + #endTime.getTime()")
    public List<DeliveryPlanPublishedVO> getDeliveryPlanPublishedData(List<String> productCodes, Date startTime, Date endTime) {
        log.info("查询发货计划发布数据，产品数量: {}, 开始时间: {}, 结束时间: {}", productCodes.size(), startTime, endTime);
        return deliveryPlanPublishedDao.selectVOByParams(ImmutableMap.of(
                "productCodes", productCodes,
                "startTimeStrYMD", DateUtils.dateToString(startTime, DateUtils.COMMON_DATE_STR3),
                "endTimeStrYMD", DateUtils.dateToString(endTime, DateUtils.COMMON_DATE_STR3)
        ));
    }
    @Override
    public List<DemandDeliveryProductionVO> getDemandDeliveryProductionReport(DemandDeliveryProductionDetailDTO dto) {
        log.info("获取报表数据，DTO: {}", dto);
        List<DemandDeliveryProductionVO> allData = getAllDemandDeliveryProductionReportData();

        if (CollectionUtils.isEmpty(allData)) {
            return new ArrayList<>();
        }

        return allData.stream()
                .filter(item -> {
                    boolean matchProductCode = (dto.getProductCode() == null || dto.getProductCode().isEmpty()) || dto.getProductCode().equals(item.getProductCode());
                    boolean matchVehicleModelCode = (dto.getVehicleModelCode() == null || dto.getVehicleModelCode().isEmpty()) || dto.getVehicleModelCode().equals(item.getVehicleModelCode());
                    boolean matchInWarehouseWarning = (dto.getInWarehouseWarning() == null) || (item.getInWarehouseWarning() != null && dto.getInWarehouseWarning().equals(item.getInWarehouseWarning()));
                    return matchProductCode && matchVehicleModelCode && matchInWarehouseWarning;
                })
                .collect(Collectors.toList());
    }

    @Cacheable(value = DfpCacheConstants.DEMAND_DELIVERY_PRODUCTION_REPORT, key = "'all_report_data'")
    public List<DemandDeliveryProductionVO> getAllDemandDeliveryProductionReportData() {
        log.info("从数据源获取所有报表数据（缓存未命中，重新计算）");
        DemandDeliveryProductionDetailDTO allDataDto = new DemandDeliveryProductionDetailDTO();
        return demandDeliveryProductionService.fetchAndProcessAllReportData(allDataDto);
    }

    @Override
    @CachePut(value = DfpCacheConstants.DEMAND_DELIVERY_PRODUCTION_REPORT, key = "'all_report_data'")
    public List<DemandDeliveryProductionVO> setDemandDeliveryProductionReport(DemandDeliveryProductionDetailDTO dto, List<DemandDeliveryProductionVO> data) {
        log.info("手动更新报表方法级缓存，DTO: {}", dto);
        return data;
    }
}