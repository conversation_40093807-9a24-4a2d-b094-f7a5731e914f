package com.yhl.scp.dfp.cache.service.impl;

import com.yhl.scp.dfp.cache.constants.DfpCacheConstants;
import com.yhl.scp.dfp.cache.service.CacheDelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

/**
 * DFP缓存删除服务实现
 */
@Slf4j
@Service
public class CacheDelServiceImpl implements CacheDelService {
    
    @Override
    @CacheEvict(value = DfpCacheConstants.INVENTORY_BATCH_DETAIL_LIST, allEntries = true)
    public void clearInventoryCache(String scenario) {
        log.info("已清除库存缓存，场景: {}", scenario);
    }
    
    @Override
    @CacheEvict(value = DfpCacheConstants.NEW_STOCK_POINT_LIST, key = "#scenario")
    public void clearStockPointCache(String scenario) {
        log.info("已清除库存点缓存，场景: {}", scenario);
    }
    
    @Override
    @CacheEvict(value = DfpCacheConstants.STANDARD_STEP_MAP, key = "#scenario")
    public void clearStandardStepCache(String scenario) {
        log.info("已清除标准步骤缓存，场景: {}", scenario);
    }
    
    @Override
    @CacheEvict(value = DfpCacheConstants.DEMAND_DELIVERY_PRODUCTION_REPORT, allEntries = true)
    public void clearDemandDeliveryProductionReportCache() {
        log.info("已清除需求发货生产报表缓存");
    }
    
    @Override
    @Caching(evict = {
            @CacheEvict(value = DfpCacheConstants.INVENTORY_BATCH_DETAIL_LIST, allEntries = true),
            @CacheEvict(value = DfpCacheConstants.NEW_STOCK_POINT_LIST, allEntries = true),
            @CacheEvict(value = DfpCacheConstants.STANDARD_STEP_MAP, allEntries = true),
            @CacheEvict(value = DfpCacheConstants.DEMAND_DELIVERY_PRODUCTION_REPORT, allEntries = true)
    })
    public void clearAllCache() {
        log.info("已清除所有DFP缓存");
    }
}