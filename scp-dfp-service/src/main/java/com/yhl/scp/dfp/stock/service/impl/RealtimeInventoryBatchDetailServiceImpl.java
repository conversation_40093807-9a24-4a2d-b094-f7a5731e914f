package com.yhl.scp.dfp.stock.service.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.feign.NewDcpFeign;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanPublishedDao;
import com.yhl.scp.dfp.stock.convertor.RealtimeInventoryBatchDetailConvertor;
import com.yhl.scp.dfp.stock.domain.entity.RealtimeInventoryBatchDetailDO;
import com.yhl.scp.dfp.stock.domain.service.RealtimeInventoryBatchDetailDomainService;
import com.yhl.scp.dfp.stock.dto.RealtimeInventoryBatchDetailDTO;
import com.yhl.scp.dfp.stock.infrastructure.dao.RealtimeInventoryBatchDetailDao;
import com.yhl.scp.dfp.stock.infrastructure.po.RealtimeInventoryBatchDetailPO;
import com.yhl.scp.dfp.stock.service.RealtimeInventoryBatchDetailService;
import com.yhl.scp.dfp.stock.vo.RealtimeInventoryBatchDetailVO;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.NewMdsFeign;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.feign.MpsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>RealtimeInventoryBatchDetailServiceImpl</code>
 * <p>
 * 实时库存批次明细应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-27 17:52:25
 */
@Slf4j
@Service
public class RealtimeInventoryBatchDetailServiceImpl extends AbstractService implements RealtimeInventoryBatchDetailService {

    @Resource
    private RealtimeInventoryBatchDetailDao realtimeInventoryBatchDetailDao;

    @Resource
    private RealtimeInventoryBatchDetailDomainService realtimeInventoryBatchDetailDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewMdsFeign mdsFeign;
    @Resource
    DeliveryPlanPublishedDao deliveryPlanPublishedDao;
    @Resource
    private NewDcpFeign newDcpFeign;
    @Resource
    private MpsFeign mpsFeign;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(RealtimeInventoryBatchDetailDTO realtimeInventoryBatchDetailDTO) {
        // 0.数据转换
        RealtimeInventoryBatchDetailDO realtimeInventoryBatchDetailDO = RealtimeInventoryBatchDetailConvertor.INSTANCE.dto2Do(realtimeInventoryBatchDetailDTO);
        RealtimeInventoryBatchDetailPO realtimeInventoryBatchDetailPO = RealtimeInventoryBatchDetailConvertor.INSTANCE.dto2Po(realtimeInventoryBatchDetailDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        realtimeInventoryBatchDetailDomainService.validation(realtimeInventoryBatchDetailDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(realtimeInventoryBatchDetailPO);
        realtimeInventoryBatchDetailDao.insert(realtimeInventoryBatchDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(RealtimeInventoryBatchDetailDTO realtimeInventoryBatchDetailDTO) {
        // 0.数据转换
        RealtimeInventoryBatchDetailDO realtimeInventoryBatchDetailDO = RealtimeInventoryBatchDetailConvertor.INSTANCE.dto2Do(realtimeInventoryBatchDetailDTO);
        RealtimeInventoryBatchDetailPO realtimeInventoryBatchDetailPO = RealtimeInventoryBatchDetailConvertor.INSTANCE.dto2Po(realtimeInventoryBatchDetailDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        realtimeInventoryBatchDetailDomainService.validation(realtimeInventoryBatchDetailDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(realtimeInventoryBatchDetailPO);
        realtimeInventoryBatchDetailDao.update(realtimeInventoryBatchDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<RealtimeInventoryBatchDetailDTO> list) {
        List<RealtimeInventoryBatchDetailPO> newList = RealtimeInventoryBatchDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        realtimeInventoryBatchDetailDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<RealtimeInventoryBatchDetailDTO> list) {
        List<RealtimeInventoryBatchDetailPO> newList = RealtimeInventoryBatchDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        realtimeInventoryBatchDetailDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return realtimeInventoryBatchDetailDao.deleteBatch(idList);
        }
        return realtimeInventoryBatchDetailDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public RealtimeInventoryBatchDetailVO selectByPrimaryKey(String id) {
        RealtimeInventoryBatchDetailPO po = realtimeInventoryBatchDetailDao.selectByPrimaryKey(id);
        return RealtimeInventoryBatchDetailConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "REALTIME_INVENTORY_BATCH_DETAIL")
    public List<RealtimeInventoryBatchDetailVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "REALTIME_INVENTORY_BATCH_DETAIL")
    public List<RealtimeInventoryBatchDetailVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<RealtimeInventoryBatchDetailVO> dataList = realtimeInventoryBatchDetailDao.selectByCondition(sortParam, queryCriteriaParam);
        RealtimeInventoryBatchDetailServiceImpl target = SpringBeanUtils.getBean(RealtimeInventoryBatchDetailServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<RealtimeInventoryBatchDetailVO> selectByParams(Map<String, Object> params) {
        List<RealtimeInventoryBatchDetailPO> list = realtimeInventoryBatchDetailDao.selectByParams(params);
        return RealtimeInventoryBatchDetailConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<RealtimeInventoryBatchDetailVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<RealtimeInventoryBatchDetailVO> invocation(List<RealtimeInventoryBatchDetailVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public BaseResponse<Void> doSync(String scenario) {
        log.info("开始处理MES实时库存数据");
        DynamicDataSourceContextHolder.setDataSource(scenario);

        List<RealtimeInventoryBatchDetailVO> mesRealTimeInventories = realtimeInventoryBatchDetailDao.queryListFromMid();
        if (CollectionUtils.isEmpty(mesRealTimeInventories)) {
            return BaseResponse.success();
        }
        List<RealtimeInventoryBatchDetailDTO> batchList = new ArrayList<>();

        for (RealtimeInventoryBatchDetailVO detail : mesRealTimeInventories) {
            String itemCode = detail.getProductCode();
            String itemId = detail.getItemId();
            String plantId = detail.getOriginalOrgId();
            RealtimeInventoryBatchDetailDTO dto = new RealtimeInventoryBatchDetailDTO();

            boolean isNegativeItemId = false;

            try {
                long itemIdValue = Long.parseLong(itemId);
                isNegativeItemId = itemIdValue < 0L;
            } catch (NumberFormatException e) {
                log.info("itemId不是有效数字");
            }
            if (isNegativeItemId) {
                int lastDashIndex = itemCode.lastIndexOf('-');
                if (lastDashIndex > 0 && lastDashIndex < itemCode.length() - 1) {
                    // 检查"-"后是否都是数字
                    String suffix = itemCode.substring(lastDashIndex + 1);
                    if (suffix.matches("\\d+")) {
                        dto.setProductCode(itemCode.substring(0, lastDashIndex));
                    } else {
                        dto.setProductCode(itemCode);
                    }
                } else {
                    dto.setProductCode(itemCode);
                }
            } else {
                dto.setProductCode(itemCode);
            }
            dto.setOriginalProductCode(itemCode);
            dto.setOriginalOrgId(plantId);
            dto.setStockPointCode(detail.getStockPointCode());
            dto.setCurrentQuantity(String.valueOf(detail.getCurrentQuantity()));
            dto.setFreightSpace(detail.getFreightSpace());
            dto.setFreightSpaceDescription(detail.getFreightSpaceDescription());
            dto.setSubinventory(detail.getSubinventory());
            dto.setSubinventoryDescription(detail.getSubinventoryDescription());
            dto.setEnabled(YesOrNoEnum.YES.getCode());
            dto.setOperationCode(detail.getOperationCode());
            batchList.add(dto);
        }

        if (CollectionUtils.isNotEmpty(batchList)) {
            this.doTruncate();
            log.info("删除实时库存批次明细历史数据");

            int optimalBatchSize = Math.min(Math.max(batchList.size() / 10, 1000), 3000);
            List<List<RealtimeInventoryBatchDetailDTO>> partitions =
                    com.google.common.collect.Lists.partition(batchList, optimalBatchSize);

            log.info("开始批量插入数据，总数量：{}，批次大小：{}，批次数量：{}",
                    batchList.size(), optimalBatchSize, partitions.size());

            for (int i = 0; i < partitions.size(); i++) {
                List<RealtimeInventoryBatchDetailDTO> partList = partitions.get(i);
                long batchStartTime = System.currentTimeMillis();
                this.doCreateBatch(partList);
                long batchEndTime = System.currentTimeMillis();

                log.info("批次{}插入完成，数量：{}，耗时：{}ms",
                        i + 1, partList.size(), batchEndTime - batchStartTime);
            }
        }
        DynamicDataSourceContextHolder.clearDataSource();
        mpsFeign.refreshInventoryBatchDetailCache(scenario);
        log.info("同步实时库存批次明细数据完成，新增:{}", batchList.size());
        return BaseResponse.success("同步成功");
    }

    @Override
    public int doDeleteAll() {
        return realtimeInventoryBatchDetailDao.doDeleteAll();
    }

    @Override
    public int doTruncate() {
        return realtimeInventoryBatchDetailDao.doTruncate();
    }

    @Override
    public BaseResponse<Void> syncRealtimeInventoryBatchDetail(Scenario scenario) {
//        Date planStartTime = DateUtils.getCurrentDateTruncateTime();

//        List<DeliveryPlanPublishedVO> deliveryList = deliveryPlanPublishedDao.selectVOByParams(ImmutableMap.of(
//                "startTimeStrYMD", DateUtils.dateToString(planStartTime, DateUtils.COMMON_DATE_STR3)
//        ));
//        List<String> finishProductCodes = deliveryList.stream().map(DeliveryPlanPublishedVO::getProductCode).distinct().collect(Collectors.toList());
//        log.info("发货计划物料编码数量：{}", finishProductCodes.size());
//        List<NewStockPointVO> allOrganization = mdsFeign.selectStockPointByParams(scenario.getDataBaseName(), ImmutableMap
//                        .of("stockPointType", StockPointTypeEnum.BC.getCode())).stream()
//                .filter(e -> org.apache.commons.lang3.StringUtils.isNotEmpty(e.getOrganizeType())
//                ).collect(Collectors.toList());
//        List<NewStockPointVO> saleOrganizations = allOrganization.stream().filter(e -> StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode().equals(e.getOrganizeType())).collect(Collectors.toList());
//
//        if(CollectionUtils.isEmpty(saleOrganizations)){
//            return BaseResponse.error("销售组织信息为空");
//        }
//        String saleOrganization= saleOrganizations.get(0).getStockPointCode();
//        List<NewProductStockPointVO> newProductStockPointVOS = mdsFeign.selectProductStockPointByParams(scenario.getDataBaseName(), ImmutableMap.of("productCodes", finishProductCodes,"stockPointCode",saleOrganization));
//        // 获取总成物品下面所有对应的半品物料（包含传入的总成物料本身）
//        List<NewProductStockPointVO> allRelatedProducts = new ArrayList<>();
//        try {
//            BaseResponse<List<NewProductStockPointVO>> semiFinishedResponse =
//                    mdsFeign.getAllSemiFinishedProducts(scenario.getDataBaseName(), newProductStockPointVOS);
//            if (semiFinishedResponse != null && semiFinishedResponse.getSuccess() &&
//                    CollectionUtils.isNotEmpty(semiFinishedResponse.getData())) {
//                allRelatedProducts = semiFinishedResponse.getData();
//                log.info("获取到{}个相关物料（包含传入的总成物料、生产组织下的总成物料和半品物料）", allRelatedProducts.size());
//
//                // 统计不同类型物料的数量
//                Set<String> inputProductCodes = newProductStockPointVOS.stream()
//                        .map(NewProductStockPointVO::getProductCode)
//                        .collect(Collectors.toSet());
//
//                long inputProductCount = allRelatedProducts.stream()
//                        .filter(p -> inputProductCodes.contains(p.getProductCode()))
//                        .count();
//                long semiFinishedCount = allRelatedProducts.size() - inputProductCount;
//
//                log.info("其中总成相关物料{}个，半品物料{}个", inputProductCount, semiFinishedCount);
//            } else {
//                log.warn("获取相关物料失败或结果为空");
//            }
//        } catch (Exception e) {
//            log.error("调用获取相关物料方法失败", e);
//        }
//        List<String> colsIn = allRelatedProducts.stream().map(t -> t.getOrganizationId() + ";" + t.getInventoryItemId()).collect(Collectors.toList());
        List<NewStockPointVO> newStockPointVOS = mdsFeign.selectStockPointByParams(scenario.getDataBaseName(),
                ImmutableMap.of("enabled",YesOrNoEnum.YES.getCode()));
        if (CollectionUtils.isEmpty(newStockPointVOS)) {
            log.warn("scenario: {} 下库存点信息为空", scenario.getDataBaseName());
            return BaseResponse.error("库存点信息为空");
        }
        String plantIds = newStockPointVOS.stream()
                .filter(t -> StringUtils.isNotEmpty(t.getOrganizeId()) && StringUtils.isNotEmpty(t.getInterfaceFlag()))
                .filter(t -> t.getInterfaceFlag().contains(ApiCategoryEnum.MES_INVENTORY_BATCH_DETAIL.getCode()))
                .filter(t -> !Objects.equals(t.getOrganizeType(), StockPointOrganizeTypeEnum.PURCHASE_ORGANIZATION.getCode()))
                .map(NewStockPointVO::getOrganizeId)
                .distinct()
                .collect(Collectors.joining(","));
        Map<String, Object> payLoad = MapUtil.newHashMap();
//        payLoad.put("colsIn", colsIn);
        payLoad.put("plantId",plantIds);
        payLoad.put("scenario", scenario.getDataBaseName());
        log.info("调用DCP的MES实时库存接口，请求参数: {}", JSONObject.toJSONString(payLoad));
        BaseResponse<String> baseResponse = newDcpFeign.callExternalApi(scenario.getTenantId(), ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.MES_REALTIME_INVENTORY.getCode(), payLoad);
        if (baseResponse != null && baseResponse.getSuccess()){
        }else{
            log.error("调用获取实时库存方法失败: {}", baseResponse.getMsg());
            throw new BusinessException("调用获取实时库存方法失败：" + baseResponse.getMsg());
        }
        return null;
    }

}
