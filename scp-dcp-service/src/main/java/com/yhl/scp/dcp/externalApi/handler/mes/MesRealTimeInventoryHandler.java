package com.yhl.scp.dcp.externalApi.handler.mes;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesRealTimeInventory;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponseData;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.enmus.MesApiReqCodeEnum;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dcp.itf.service.RealtimeInventoryBatchDetailService;
import com.yhl.scp.ips.constant.MqConstants;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * <code>MesRealTimeInventoryHandler</code>
 * <p>
 * MES实时库存批次查询
 * 同步方式：全量
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-15 11:23:52
 */
@Component
@Slf4j
public class MesRealTimeInventoryHandler extends SyncDataHandler<List<MesRealTimeInventory>> {

    @Resource
    private AuthHandler authHandler;

    @Resource
    private RealtimeInventoryBatchDetailService realtimeInventoryBatchDetailService;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Override
    protected List<MesRealTimeInventory> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("MES获取外部库存批次明细数据为空");
            return null;
        }
        return JSONArray.parseArray(body, MesRealTimeInventory.class);
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<MesRealTimeInventory> mesInventoryBatchDetails) {

        if (CollectionUtils.isEmpty(mesInventoryBatchDetails)) {
            log.error("库存批次明细为空");
            return null;
        }

        String scenario = (String) params.get("scenario");
        DynamicDataSourceContextHolder.setDataSource(scenario);
        realtimeInventoryBatchDetailService.sync(mesInventoryBatchDetails);
        DynamicDataSourceContextHolder.clearDataSource();

        String profile = System.getProperty("spring.profiles.active") + ".";
        rabbitTemplate.convertAndSend(profile + MqConstants.BPIM_SYNC_EXCHANGE, profile + MqConstants.REALTIME_INVENTORY_SYNC_KEY, scenario);
        return null;
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步MES实时库存:{},{}", apiConfigVO, params);
        }
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        try {
            String mesToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String apiParams = apiConfigVO.getApiParams();

            String url = apiUri + apiParams;
            String reqCode = MesApiReqCodeEnum.FY_INV_ONHAND_FOR_BPIM.getCode();

            if (log.isInfoEnabled()) {
                log.info("apiUri={},url={},reqCode={}", apiUri, url, reqCode);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", "Bearer " + mesToken);

            List<Object> finalResult = Lists.newArrayList();

            List<Object> chunkResult = Lists.newArrayList();
            int pageSize = Objects.isNull(apiConfigVO.getOffsetSize()) ? 10000 : apiConfigVO.getOffsetSize();
            int totalRecords;
            Object plantId = params.get("plantId");

            // 针对当前批次进行分页查询
            for (int currentPage = 1; ; currentPage++) {
                long startTime = System.currentTimeMillis(); // 记录开始时间
                HashMap<Object, Object> paramMap = MapUtil.newHashMap();
                paramMap.put("reqCode", reqCode);
                paramMap.put("currentPage", currentPage);
                paramMap.put("pageSize", pageSize);
                paramMap.put("plantId", plantId);
                List<HashMap<Object, Object>> conditions = new ArrayList<>();

                HashMap<Object, Object> conditionMap = MapUtil.newHashMap();
                conditionMap.put("cKey", "isb");
                conditionMap.put("cValue", "N");
                conditions.add(conditionMap);
                paramMap.put("conditions", conditions);

                // 创建子日志
                ExtApiLogDTO subLog = extApiLogService.createLog(apiConfigVO, params, mainLog,
                        httpHeaders.toString(),
                        JSONObject.toJSONString(paramMap));
                HttpEntity<String> httpEntity = new HttpEntity<>(JSON.toJSONString(paramMap), httpHeaders);
                ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                log.info("MES实时库存请求第{}页耗时: {} ms", currentPage, duration);
                int statusCodeValue = responseEntity.getStatusCodeValue();
                if (HttpStatus.OK.value() != statusCodeValue) {
                    extApiLogService.updateResponse(subLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                }
                Assert.isTrue(HttpStatus.OK.value() == statusCodeValue, "MES同步实时库存失败！");
                String body = responseEntity.getBody();
                log.info("请求MES实时库存完成,返回数据:{}!", body);
                MesResponse mesResponse = JSON.parseObject(body, MesResponse.class);

                // 失败时 data.status="fail"
                MesResponseData data = Optional.ofNullable(mesResponse).map(MesResponse::getData).orElse(null);
                if (data == null || "fail".equalsIgnoreCase(data.getStatus())) {
                    String errorMessage = data != null && data.getMessage() != null ? JSON.toJSONString(data.getMessage()) : "MES接口返回失败，且无详细错误信息";
                    throw new BusinessException("MES实时库存接口返回业务失败: " + errorMessage);
                }

                if (CollectionUtils.isNotEmpty(data.getMessage())) {
                    chunkResult.addAll(data.getMessage());
                }

                // 总记录数
                totalRecords = data.getTotal();
                // 如果当前页的数据量小于pageSize，或已获取完当前批次的所有数据，则结束当前批次的分页
                if (CollectionUtils.isEmpty(data.getMessage()) || data.getMessage().size() < pageSize || chunkResult.size() >= totalRecords) {
                    log.info("当前批次的数据获取完成，总记录数: {}", chunkResult.size());
                    break;
                }
                extApiLogService.updateResponse(subLog, responseEntity, data.getMessage().size(), DcpConstants.TASKS_STATUS_SUCCESS);
            }
            finalResult.addAll(chunkResult);
            // 3. 完成主日志
            extApiLogService.updateResponse(mainLog, null, finalResult.size(), DcpConstants.TASKS_STATUS_SUCCESS);
            return JSON.toJSONString(finalResult);
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            log.error("MES实时接口同步报错，{}!", e.getMessage(),e);
            throw e;
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.MES_REALTIME_INVENTORY.getCode());

    }
}
