package com.yhl.scp.dcp.externalApi.handler.mes;


import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponseData;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dfp.common.enums.GranularityEnum;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.originDemand.dto.FdpOriginDemandInterfaceLogDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;

/**
 * <code>ShanghaiGrpLoadingDemandHandler</code>
 * <p>
 * FYE-MES发运需求接口
 * 同步方式：增量
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 14:57:15
 */
@Component
@Slf4j
public class ShipDemandHandler extends SyncDataHandler<List<FdpOriginDemandInterfaceLogDTO>> {
    @Resource
    private AuthHandler authHandler;

    @Resource
    private DfpFeign dfpFeign;

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<FdpOriginDemandInterfaceLogDTO> fdpOriginDemandInterfaceLogDTOS) {
        Date lastUpdateDate = fdpOriginDemandInterfaceLogDTOS.stream().map(FdpOriginDemandInterfaceLogDTO::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步MES需求数据:{},{}", apiConfigVO, params);
        }
        //开始时间
        long methodStartTime = System.currentTimeMillis();
        Object scenarioObj = params.get("scenario");
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        try {
            String mesToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String apiParams = apiConfigVO.getApiParams();

            String url = apiUri +  apiParams;
            String reqCode = "FYE_SHIP_REQ_FOR_ERP";
            String lastUpdateDateStr = this.getSyncRefValue(apiConfigVO, params);
            Date calculateDate = DateUtils.stringToDate(lastUpdateDateStr, DateUtils.COMMON_DATE_STR3);
            Date currentDate = DateUtils.truncateTimeOfDate(new Date());
            if (log.isInfoEnabled()) {
                log.info("mesToken={},apiUri={},apiParams={},url={},reqCode={},lastUpdateDateStr={},currentDate={}", mesToken, apiUri, apiParams,
                        url, reqCode, lastUpdateDateStr, currentDate);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", "Bearer " + mesToken);

            int period = (int) DateUtil.between(calculateDate, currentDate, DateUnit.DAY);
            int calculatePeriod = 5;
            int count = period / calculatePeriod + 1;
            int pageSize = Objects.isNull(apiConfigVO.getOffsetSize())?10000:apiConfigVO.getOffsetSize();
            Date beginTime = calculateDate;
            List<Object> result = Lists.newArrayList();
            for (int i = 0; i < count; i++) {
                int currentPage = 1;
                boolean hasNextSize = true;
                while (hasNextSize) {
                    long pageStartTime = System.currentTimeMillis();
                    HashMap<Object, Object> paramMap = MapUtil.newHashMap();
                    paramMap.put("currentPage", currentPage);
                    paramMap.put("pageSize", pageSize);
                    paramMap.put("reqCode", reqCode);
                    paramMap.put("beginTime", DateUtils.dateToString(beginTime, DateUtils.COMMON_DATE_STR1));
                    Date endTime = org.apache.commons.lang3.time.DateUtils.addDays(beginTime, calculatePeriod);
                    paramMap.put("endTime", DateUtils.dateToString(endTime, DateUtils.COMMON_DATE_STR1));
                    if (log.isInfoEnabled()) {
                        log.info("request paramMap={}", paramMap);
                    }
                    // 创建子日志
                    ExtApiLogDTO subLog = extApiLogService.createLog(apiConfigVO, params, mainLog,
                            httpHeaders.toString(),
                            JSONObject.toJSONString(paramMap));
                    HttpEntity<String> httpEntity = new HttpEntity<>(JSON.toJSONString(paramMap), httpHeaders);
                    ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
                    int statusCodeValue = responseEntity.getStatusCodeValue();
                    if (HttpStatus.OK.value() != statusCodeValue) {
                        extApiLogService.updateResponse(subLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                    }
                    Assert.isTrue(HttpStatus.OK.value() == statusCodeValue, "MES同步需求信息失败！");
                    // 计算单个页请求耗时（秒）
                    long pageEndTime = System.currentTimeMillis();
                    double pageTimeSeconds = pageEndTime - pageStartTime;
                    String body = responseEntity.getBody();
                    log.info("同步MES需求数据完成,返回数据:{}!", body);
                    MesResponse mesResponse = JSON.parseObject(body, MesResponse.class);
                    if (mesResponse.getCode() != 0) {
                        String errorStr = mesResponse.getData().getMessage().toString();
                        log.error("数据报错，{}", mesResponse.getData().getMessage().toString());
                        throw new BusinessException(StrUtil.format("MES调用接口报错:{}!", errorStr));
                    }
                    MesResponseData data = Objects.requireNonNull(mesResponse).getData();
                    extApiLogService.updateResponse(subLog, responseEntity, data.getMessage().size(), DcpConstants.TASKS_STATUS_SUCCESS);
                    result.addAll(data.getMessage());
                    if (data.getTotalPage() <= data.getCurrentPage()) {
                        hasNextSize = false;
                        beginTime = endTime;
                    } else {
                        currentPage++;
                    }
                    log.info("MES需求接口第{}批,第{}页请求完成，数据量：{}，耗时：{}ms",i, currentPage, data.getMessage().size(),pageTimeSeconds);

                }
            }
            // 3. 完成主日志
            extApiLogService.updateResponse(mainLog, null, result.size(), DcpConstants.TASKS_STATUS_SUCCESS);
            long methodEndTime = System.currentTimeMillis();
            double totalTimeSeconds = methodEndTime - methodStartTime;

            log.info("MES需求数据,数据条数{}，总耗时：{}ms,", result.size(),totalTimeSeconds);
            return JSON.toJSONString(result);
        } catch (Exception e) {
            long errorTime = System.currentTimeMillis();
            double errorTimeSeconds = errorTime - methodStartTime;
            log.error("调用MES发运需求接口异常，已执行耗时：{}ms，错误：{}", errorTimeSeconds, e.getMessage(), e);
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw new BusinessException("MES发运需求调用异常: " + e.getMessage(), e);
        }
    }

    @Override
    protected List<FdpOriginDemandInterfaceLogDTO> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("MES获取发运需求数据为空");
            return null;
        }
        return JSONArray.parseArray(body, FdpOriginDemandInterfaceLogDTO.class);
    }

    @Override
    protected String handleBody(ApiConfigVO
                                        apiConfigVO, Map<String, Object> params, List<FdpOriginDemandInterfaceLogDTO> list) {
        long handleStartTime = System.currentTimeMillis();
        this.saveSyncCtrlSyncTime(apiConfigVO, params);

        if (CollectionUtils.isEmpty(list)) {
            log.warn("MES发运需求数据为空，跳过处理");
            return "数据为空，跳过处理";
        }
        list.forEach(t -> {
            t.setSubmissionType(GranularityEnum.DAY.getCode());
            t.setEnabled(YesOrNoEnum.YES.getCode());
            t.setImportType(ApiSourceEnum.MES.getCode());

        });
        log.info("接口返回MES需求数据大小:{}",list.size());
        long dfpCallStartTime = System.currentTimeMillis();
        dfpFeign.syncOriginDemandLog((String) params.get("scenario"), list, ApiSourceEnum.MES.getCode());
        long dfpCallEndTime = System.currentTimeMillis();
        long handleEndTime = System.currentTimeMillis();
        double totalHandleTimeSeconds = handleEndTime - handleStartTime;
        double dfpCallTimeSeconds = dfpCallEndTime - dfpCallStartTime;
        log.info("MES发运需求数据总处理耗时：{}秒，其中DFP服务调用耗时：{}ms",
                totalHandleTimeSeconds, dfpCallTimeSeconds);
        this.saveSyncCtrl(apiConfigVO, params, list);
        return "同步数据成功";
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.MES_SHIP_DEMAND.getCode());
    }

}
