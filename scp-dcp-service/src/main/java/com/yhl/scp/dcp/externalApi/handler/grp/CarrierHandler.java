package com.yhl.scp.dcp.externalApi.handler.grp;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dfp.carrier.dto.CarrierDataDTO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * description: GRP承运商接口开发
 * author：李杰
 * email: <EMAIL>
 * date: 2025/1/14
 */
@Component
@Slf4j
public class CarrierHandler extends SyncDataHandler<List<CarrierDataDTO>> {
    @Resource
    private AuthHandler authHandler;

    @Resource
    private DfpFeign dfpFeign;

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步GRP承运商数据:{},{}", apiConfigVO, params);
        }
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        try {
            //获取GRP的token
            String GRPToken = authHandler.handle(MapUtil.newHashMap());
            //地址接口
            String apiUri = apiConfigVO.getApiUri();
            //系统号
            String systemNumber = apiConfigVO.getSystemNumber();
            //流水号
            String suffix = this.sequenceService.getSuffix(systemNumber, getCommand(), 5);
            //组合地址
            String url = apiUri + "/" + systemNumber + "/" + suffix;
            if (log.isInfoEnabled()) {
                log.info("GRPToken={},apiUri={},suffix={},url={}", GRPToken, apiUri, suffix, url);
            }
            //请求头
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", "Bearer " + GRPToken);
            //请求体
            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("lang", "zh_CN");
            paramMap.put("txDate", DateUtils.dateToString(new Date(), DateUtils.COMMON_DATE_STR1));
            HashMap<String, Object> map = MapUtil.newHashMap();
            map.put("ebsOrgId", params.get("orgId"));
            paramMap.put("data", map);
            //组装发送接口
            HttpEntity httpEntity = new HttpEntity(JSONObject.toJSONString(paramMap), httpHeaders);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() == statusCodeValue) {
                String body = responseEntity.getBody();
                log.info("同步GRP承运商数据完成,返回数据:{}!", body);
                Map<String, Object> map1 = JSON.parseObject(body, Map.class);
                if ((boolean) Objects.requireNonNull(map1).get("success")) {
                    extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_SUCCESS);
                    return JSON.toJSONString(map1.get("data"));
                } else {
                    extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                }
            } else {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
            }
            return null;
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            log.error("CarrierHandler里面数据报错，{}",e.getMessage());
            throw new BusinessException(e.getMessage());
        }
    }

    @Override
    protected List<CarrierDataDTO> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            return null;
        }
        return JSONObject.parseArray(body, CarrierDataDTO.class);
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<CarrierDataDTO> list) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(list)) {
            log.error("获取GRP承运商数据为空");
            return null;
        }
        list.forEach(x -> x.setSourceType(ApiSourceEnum.GRP.getCode()));
        dfpFeign.syncCarrierData(params.get("scenario").toString(), list);
        //记录同步数据
        this.saveSyncCtrl(apiConfigVO, params, list);
        return "同步成功";
    }

    @Override
    protected String getSyncGroupValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        return String.valueOf(params.get("orgId"));
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.GRP.getCode(), ApiCategoryEnum.CARRIER.getCode());
    }
}
