<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dcp.itf.infrastructure.dao.CuringTimeDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dcp.itf.infrastructure.po.CuringTimePO">
        <!--@Table itf_curing_time-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="company_code" jdbcType="VARCHAR" property="companyCode"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="enable_flag" jdbcType="VARCHAR" property="enableFlag"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="gh_flag" jdbcType="VARCHAR" property="ghFlag"/>
        <result column="gh_time" jdbcType="VARCHAR" property="ghTime"/>
        <result column="item_code" jdbcType="VARCHAR" property="itemCode"/>
        <result column="kid" jdbcType="VARCHAR" property="kid"/>
        <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="plant_code" jdbcType="VARCHAR" property="plantCode"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="sync_flag" jdbcType="VARCHAR" property="syncFlag"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dcp.itf.vo.CuringTimeVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,company_code,create_time,create_user,creator,enable_flag,enabled,gh_flag,gh_time,item_code,kid,last_update_date,modifier,modify_time,plant_code,remark,sync_flag,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.companyCode != null and params.companyCode != ''">
                and company_code = #{params.companyCode,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.createUser != null and params.createUser != ''">
                and create_user = #{params.createUser,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.enableFlag != null and params.enableFlag != ''">
                and enable_flag = #{params.enableFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.ghFlag != null and params.ghFlag != ''">
                and gh_flag = #{params.ghFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.ghTime != null and params.ghTime != ''">
                and gh_time = #{params.ghTime,jdbcType=VARCHAR}
            </if>
            <if test="params.itemCode != null and params.itemCode != ''">
                and item_code = #{params.itemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.kid != null and params.kid != ''">
                and kid = #{params.kid,jdbcType=VARCHAR}
            </if>
            <if test="params.lastUpdateDate != null">
                and last_update_date = #{params.lastUpdateDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.plantCode != null and params.plantCode != ''">
                and plant_code = #{params.plantCode,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.syncFlag != null and params.syncFlag != ''">
                and sync_flag = #{params.syncFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.kids != null and params.kids.size() > 0">
                and kid in
                <foreach collection="params.kids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from itf_curing_time
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from itf_curing_time
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from itf_curing_time
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from itf_curing_time
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dcp.itf.infrastructure.po.CuringTimePO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into itf_curing_time(
        id,
        company_code,
        create_time,
        create_user,
        creator,
        enable_flag,
        enabled,
        gh_flag,
        gh_time,
        item_code,
        kid,
        last_update_date,
        modifier,
        modify_time,
        plant_code,
        remark,
        sync_flag,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{companyCode,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{createUser,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{enableFlag,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{ghFlag,jdbcType=VARCHAR},
        #{ghTime,jdbcType=VARCHAR},
        #{itemCode,jdbcType=VARCHAR},
        #{kid,jdbcType=VARCHAR},
        #{lastUpdateDate,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{plantCode,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{syncFlag,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dcp.itf.infrastructure.po.CuringTimePO">
        insert into itf_curing_time(
        id,
        company_code,
        create_time,
        create_user,
        creator,
        enable_flag,
        enabled,
        gh_flag,
        gh_time,
        item_code,
        kid,
        last_update_date,
        modifier,
        modify_time,
        plant_code,
        remark,
        sync_flag,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{companyCode,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{createUser,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{enableFlag,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{ghFlag,jdbcType=VARCHAR},
        #{ghTime,jdbcType=VARCHAR},
        #{itemCode,jdbcType=VARCHAR},
        #{kid,jdbcType=VARCHAR},
        #{lastUpdateDate,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{plantCode,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{syncFlag,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into itf_curing_time(
        id,
        company_code,
        create_time,
        create_user,
        creator,
        enable_flag,
        enabled,
        gh_flag,
        gh_time,
        item_code,
        kid,
        last_update_date,
        modifier,
        modify_time,
        plant_code,
        remark,
        sync_flag,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.companyCode,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.createUser,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.enableFlag,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.ghFlag,jdbcType=VARCHAR},
            #{entity.ghTime,jdbcType=VARCHAR},
            #{entity.itemCode,jdbcType=VARCHAR},
            #{entity.kid,jdbcType=VARCHAR},
            #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.plantCode,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.syncFlag,jdbcType=VARCHAR},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into itf_curing_time(
        id,
        company_code,
        create_time,
        create_user,
        creator,
        enable_flag,
        enabled,
        gh_flag,
        gh_time,
        item_code,
        kid,
        last_update_date,
        modifier,
        modify_time,
        plant_code,
        remark,
        sync_flag,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.companyCode,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.createUser,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.enableFlag,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.ghFlag,jdbcType=VARCHAR},
            #{entity.ghTime,jdbcType=VARCHAR},
            #{entity.itemCode,jdbcType=VARCHAR},
            #{entity.kid,jdbcType=VARCHAR},
            #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.plantCode,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.syncFlag,jdbcType=VARCHAR},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dcp.itf.infrastructure.po.CuringTimePO">
        update itf_curing_time set
        company_code = #{companyCode,jdbcType=VARCHAR},
        create_user = #{createUser,jdbcType=VARCHAR},
        enable_flag = #{enableFlag,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        gh_flag = #{ghFlag,jdbcType=VARCHAR},
        gh_time = #{ghTime,jdbcType=VARCHAR},
        item_code = #{itemCode,jdbcType=VARCHAR},
        kid = #{kid,jdbcType=VARCHAR},
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        plant_code = #{plantCode,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        sync_flag = #{syncFlag,jdbcType=VARCHAR},
        version_value = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dcp.itf.infrastructure.po.CuringTimePO">
        update itf_curing_time
        <set>
            <if test="item.companyCode != null and item.companyCode != ''">
                company_code = #{item.companyCode,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.createUser != null and item.createUser != ''">
                create_user = #{item.createUser,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.enableFlag != null and item.enableFlag != ''">
                enable_flag = #{item.enableFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.ghFlag != null and item.ghFlag != ''">
                gh_flag = #{item.ghFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.ghTime != null and item.ghTime != ''">
                gh_time = #{item.ghTime,jdbcType=VARCHAR},
            </if>
            <if test="item.itemCode != null and item.itemCode != ''">
                item_code = #{item.itemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.kid != null and item.kid != ''">
                kid = #{item.kid,jdbcType=VARCHAR},
            </if>
            <if test="item.lastUpdateDate != null">
                last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.plantCode != null and item.plantCode != ''">
                plant_code = #{item.plantCode,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.syncFlag != null and item.syncFlag != ''">
                sync_flag = #{item.syncFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update itf_curing_time
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="company_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.companyCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="create_user = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createUser,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enable_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enableFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="gh_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ghFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="gh_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ghTime,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="item_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.itemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="kid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.kid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="last_update_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lastUpdateDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="plant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.plantCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sync_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.syncFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update itf_curing_time
            <set>
                <if test="item.companyCode != null and item.companyCode != ''">
                    company_code = #{item.companyCode,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.createUser != null and item.createUser != ''">
                    create_user = #{item.createUser,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.enableFlag != null and item.enableFlag != ''">
                    enable_flag = #{item.enableFlag,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.ghFlag != null and item.ghFlag != ''">
                    gh_flag = #{item.ghFlag,jdbcType=VARCHAR},
                </if>
                <if test="item.ghTime != null and item.ghTime != ''">
                    gh_time = #{item.ghTime,jdbcType=VARCHAR},
                </if>
                <if test="item.itemCode != null and item.itemCode != ''">
                    item_code = #{item.itemCode,jdbcType=VARCHAR},
                </if>
                <if test="item.kid != null and item.kid != ''">
                    kid = #{item.kid,jdbcType=VARCHAR},
                </if>
                <if test="item.lastUpdateDate != null">
                    last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.plantCode != null and item.plantCode != ''">
                    plant_code = #{item.plantCode,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.syncFlag != null and item.syncFlag != ''">
                    sync_flag = #{item.syncFlag,jdbcType=VARCHAR},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from itf_curing_time where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from itf_curing_time where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
