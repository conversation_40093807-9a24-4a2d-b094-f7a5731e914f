package com.yhl.scp.dcp.feign.controller;

import cn.hutool.json.JSONUtil;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.biz.common.util.FileUtils;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.service.ApiConfigService;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.apiLog.vo.ExtApiLogVO;
import com.yhl.scp.dcp.externalApi.ExternalApiHandler;
import com.yhl.scp.dcp.externalApi.handler.Handler;
import com.yhl.scp.dcp.feign.NewDcpFeign;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ExternalApiController
 * @Description TODO
 * @Date 2024-09-09 09:21:05
 * <AUTHOR>
 * @Copyright 哈哈哈
 * @Version 1.0
 */
@Slf4j
@Api(tags = "外部接口控制器")
@RestController
//@RequestMapping("externalApi")
public class ExternalApiController implements NewDcpFeign {

    @Resource
    private ExternalApiHandler externalApiHandler;

    @Resource
    private ApiConfigService apiConfigService;

    @Resource
    private ExtApiLogService extApiLogService;

    @ApiOperation("调用外部系统接口")
//    @PostMapping("call")
    public BaseResponse<String> callExternalApi(@RequestParam("tenantCode") String tenantCode,
                                                @RequestParam("apiSource") String apiSource,
                                                @RequestParam("apiCategory") String apiCategory,
                                                @RequestBody Map<String, Object> params) {
        DynamicDataSourceContextHolder.clearDataSource();
        String command = String.join(Handler.CMD_DELIMITER, tenantCode, apiSource, apiCategory);
        log.info("调用外部系统接口：{}", command);
        return externalApiHandler.handle(command, params);
    }

    @ApiOperation("调用外部系统带文件接口")
//    @RequestMapping(method = RequestMethod.POST, value = "callWithAttach", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public BaseResponse<String> callWithAttach(@RequestPart("data") String data, @RequestPart("files") MultipartFile[] multipartFiles) {
        try{
            DynamicDataSourceContextHolder.clearDataSource();
            Map dataMap = JSONUtil.toBean(data, Map.class);
            dataMap.put("files", FileUtils.convertBatch(Arrays.asList(multipartFiles)));
            String command = String.join(Handler.CMD_DELIMITER, (String) dataMap.get("tenantCode"), (String) dataMap.get("apiSource"), (String) dataMap.get("apiCategory") + "");
            return externalApiHandler.handle(command, dataMap);
        }catch (Exception e){
            log.error("调用外部接口失败:{}", e);
            throw new BusinessException("调用外部接口失败" + e.getMessage());
        }
    }

    @ApiOperation("获取接口日志")
//    @PostMapping("apiLog")
    public ExtApiLogVO getExtApiLog(@RequestParam(value = "id") String id) {
        DynamicDataSourceContextHolder.clearDataSource();
        return extApiLogService.selectByPrimaryKey(id);
    }

    @ApiOperation("根据参数获取外部接口标识")
//    @PostMapping("apiMapLog")
    public List<ExtApiLogVO> getByParamsExtApiLog(@RequestBody Map<String, Object> params) {
        DynamicDataSourceContextHolder.clearDataSource();
        return extApiLogService.selectByParams(params);
    }

}