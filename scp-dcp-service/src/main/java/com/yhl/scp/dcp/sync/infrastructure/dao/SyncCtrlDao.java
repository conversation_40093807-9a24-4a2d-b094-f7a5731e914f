package com.yhl.scp.dcp.sync.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dcp.sync.infrastructure.po.SyncCtrlPO;
import com.yhl.scp.dcp.sync.vo.SyncCtrlVO;
import com.yhl.scp.mrp.inventory.vo.InventoryQuayDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>SyncCtrlDao</code>
 * <p>
 * 外部api同步控制表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-16 18:17:37
 */
public interface SyncCtrlDao extends BaseDao<SyncCtrlPO, SyncCtrlVO> {
    List<SyncCtrlPO> selectByGroupValueConfigId(@Param("params") Map<String, Object> params);

}
