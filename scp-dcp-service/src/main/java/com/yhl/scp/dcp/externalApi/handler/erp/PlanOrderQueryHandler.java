package com.yhl.scp.dcp.externalApi.handler.erp;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpPlanOrderQuery;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dcp.sync.vo.SyncCtrlVO;
import com.yhl.scp.mps.feign.MpsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <code>PlanOrderHandler</code>
 * <p>
 * ERP计划单创建接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-20 17:26:37
 */
@Component
@Slf4j
public class PlanOrderQueryHandler extends SyncDataHandler<List<ErpPlanOrderQuery>> {
    @Resource
    private AuthHandler authHandler;

    @Resource
    private MpsFeign mpsFeign;
    @Override
    protected List<ErpPlanOrderQuery> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("ERP查询计划单接口返回数据为空");
            return null;
        }
        List<ErpPlanOrderQuery> erpCreatePlanOrders = JSONObject.parseArray(body, ErpPlanOrderQuery.class);
        return erpCreatePlanOrders;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<ErpPlanOrderQuery> list) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(list)) {
            log.error("erp查询计划单接口返回数据为空");
            return null;
        }
        mpsFeign.handlePlanOrder((String) params.get("scenario"),list);
        this.saveSyncCtrl(apiConfigVO, params, list);
        return null;
    }
    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<ErpPlanOrderQuery> erpPlanOrderQueries) {
        Date lastUpdateDate = erpPlanOrderQueries.stream().map(ErpPlanOrderQuery::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }
    @Override
    protected String getSyncGroupValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        return String.valueOf(params.get("orgId"));
    }
    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        if (log.isInfoEnabled()) {
            log.info("开始查询ERP计划单数据，报文参数:{},{}", apiConfigVO, params);
        }
        try {
            String erpToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String url = apiUri + "?token=" + erpToken;

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            String lastUpdateDate = this.getSyncRefValue(apiConfigVO, params);
            params.put("lastUpdateDate", lastUpdateDate);
            String bodyStr = JSONObject.toJSONString(params);
            if (log.isInfoEnabled()) {
                log.info("erpToken={},apiUri={},url={},bodyStr={}", erpToken, apiUri, url, bodyStr);
            }
            HttpEntity httpEntity = new HttpEntity(bodyStr, httpHeaders);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
//                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                throw new BusinessException(StrUtil.format("计划单查询接口请求失败,HTTP状态码:{}!", statusCodeValue));
            }
            extApiLogService.updateResponse(mainLog, responseEntity, 1, DcpConstants.TASKS_STATUS_SUCCESS);
            ErpResponse erpResponse = JSONObject.parseObject(responseEntity.getBody(), ErpResponse.class);
            if (erpResponse.getSuccess()) {
                extApiLogService.updateResponse(mainLog, responseEntity, 1, DcpConstants.TASKS_STATUS_SUCCESS);
                return JSONObject.toJSONString(erpResponse.getData());
            } else {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                log.error("计划单查询接口报错，{}!", erpResponse.getMessage());
                return null;
            }
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            log.error("计划单接口同步报错，{}!", e.getMessage(),e);
            throw e;
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.ERP.getCode(),
                ApiCategoryEnum.PLAN_ORDER_QUERY.getCode());
    }
    @Override
    protected String getSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        SyncCtrlVO syncCtrlVO = syncCtrlService.getSyncCtrl(apiConfigVO, this.getSyncGroupValue(apiConfigVO, params));
        return ObjUtil.isNotNull(syncCtrlVO) ? DateUtil.offsetHour(DateUtils.stringToDate(syncCtrlVO.getReferValue(),
                DateUtils.COMMON_DATE_STR1) ,-1).toString()  : DateUtil.offsetDay(DateTime.now(), -7)
                .toString(DatePattern.NORM_DATETIME_FORMAT);
    }
}
