package com.yhl.scp.dcp.externalApi.handler.iam;

import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.IamResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.ips.rbac.dto.UserDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * description:增量同步用户
 * author：李杰
 * email: <EMAIL>
 * date: 2024/11/7
 */
@Component
@Slf4j
public class UserHandler extends SyncDataHandler<List<UserDTO>> {

    @Resource
    private AuthHandler iamAuthHandler;

    @Resource
    private UserCallbackHandler userCallbackHandler;

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步IAM的账户数据:{},{}", apiConfigVO, params);
        }
        String token = iamAuthHandler.getToken();
        String apiUri = apiConfigVO.getApiUri();
        String systemNumber = apiConfigVO.getSystemNumber();
        String url = apiUri + "/" + systemNumber + "/" + this.sequenceService.getSuffix(systemNumber, getCommand(), 5);
        if (log.isInfoEnabled()) {
            log.info("token={},apiUri={},systemNumber={},url={}", token, apiUri, systemNumber, url);
        }
        //设置请求头
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.set("Authorization", token);
        //设置请求体
        HashMap<String, Object> map = new HashMap<>();
        int size = Objects.isNull(apiConfigVO.getOffsetSize()) ? 5000 : apiConfigVO.getOffsetSize();
        long startTimeLong;
        if (Objects.isNull(params.get("triggerType"))) {
            //定时任务
            Date date = DateUtils.stringToDate(this.getSyncRefValue(apiConfigVO, params), DateUtils.COMMON_DATE_STR1);
            startTimeLong = date.getTime();
        } else {
            startTimeLong = Long.parseLong(params.get("startTime").toString());
        }
        params.put("lastUpdate", new Date());
        //数据集合
        List<UserDTO> arrayList = new ArrayList<>();
        for (int page = 1; ; page++) {
            map.put("page", page);
            map.put("size", size);
            map.put("startTime", startTimeLong);
            if (log.isInfoEnabled()) {
                log.info("token={},apiUri={},systemNumber={},url={},map={}", token, apiUri
                        , systemNumber, url, map);
            }
            HttpEntity httpEntity = new HttpEntity(JSONObject.toJSONString(map), httpHeaders);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            Assert.isTrue(HttpStatus.OK.value() == statusCodeValue, "IAM同步用户失败！");
            String body = responseEntity.getBody();
            log.info("同步IAM的用户数据请求完成，返回数据:{}!", body);
            IamResponse iamResponse = JSONObject.parseObject(body, IamResponse.class);
            if ("0".equals(iamResponse.getCode())) {
                String str = JSONObject.toJSONString(iamResponse.getData());
                Map<String, Object> map1 = JSONObject.parseObject(str, Map.class);
                List<UserDTO> object = (List) map1.get("list");
                arrayList.addAll(object);
                if (Integer.parseInt(map1.get("total").toString()) < size) {
                    return JSONObject.toJSONString(arrayList);
                }
            } else {
                log.error("IAM同步用户报错，{}", iamResponse.getMsg());
                return null;
            }
        }
    }

    @Override
    protected List<UserDTO> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            return null;
        }
        List<Map> list = JSONObject.parseArray(body, Map.class);
        List<UserDTO> arrayList = new ArrayList<>();
        list.stream().forEach(x -> {
                    if (StringUtils.isNotEmpty(x.get("idt_user__user_name").toString()) && StringUtils.isNotEmpty(x.get("request_log__id").toString())) {
                        arrayList.add(JSONObject.parseObject(JSONObject.toJSONString(x), UserDTO.class));
                    }
                }
        );
        return arrayList;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<UserDTO> list) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);

        if (CollectionUtils.isEmpty(list)) {
            log.info("IAM同步用户列表数据为空");
            return null;
        }
        newIpsFeign.syncUserData(list);
        //回调账号数据至IAM
        List<String> collect = list.stream().map(UserDTO::getRequestLogId).collect(Collectors.toList());
        boolean isCallback = userCallbackHandler.callApi(collect);
        if (Objects.isNull(params.get("triggerType")) && isCallback) {
            this.saveSyncCtrl(apiConfigVO, params, list);
        }
        return "同步数据成功";
    }

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<UserDTO> userDTOS) {
        Date date = (Date) params.get("lastUpdate");
        return DateUtils.dateToString(date, DateUtils.COMMON_DATE_STR1);
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.IAM.getCode(), ApiCategoryEnum.ALL_USER.getCode());
    }

}
