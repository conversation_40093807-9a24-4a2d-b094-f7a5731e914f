package com.yhl.scp.dcp.externalApi.handler.erp;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.highValueMaterials.dto.MpsHighValueMaterialsDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * description:高价值物料同步
 * author：李杰
 * email: <EMAIL>
 * date: 2024/9/4
 */
@Component
@Slf4j
public class HighValueMaterialsHandler extends SyncDataHandler<List<MpsHighValueMaterialsDTO>> {

    @Resource
    private AuthHandler authHandler;

    @Resource
    private MpsFeign mpsFeign;

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        if (log.isInfoEnabled()) {
            log.info("开始同步ERP高价值物料:{},{}", apiConfigVO, params);
        }
        try {
            //获取ERP的token
            String erpToken = authHandler.handle(MapUtil.newHashMap());
            //erp地址接口
            String apiUri = apiConfigVO.getApiUri();
            //管理员编码
            String systemNumber = apiConfigVO.getSystemNumber();
            String url = apiUri + "/" + systemNumber + "/" + this.sequenceService.getSuffix(systemNumber, getCommand(), 6)
                    + "?token=" + erpToken + "&organizationCode=" + params.get("orgCode")
                    + "&bomOrganizationCode" + params.get("bomCodes");
            if (log.isInfoEnabled()) {
                log.info("erpToken={},apiUri={},systemNumber={},url={}", erpToken, apiUri
                        , systemNumber, url);
            }
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                throw new BusinessException(StrUtil.format("同步ERP高价值物料失败,HTTP状态码:{}!", statusCodeValue));
            }
            ErpResponse erpResponse = JSONObject.parseObject(responseEntity.getBody(), ErpResponse.class);
            if (log.isInfoEnabled()) {
                log.info("同步ERP高价值物料请求完成,返回数据:{}!", JSONUtil.toJsonStr(erpResponse));
            }
            if (erpResponse.getSuccess()) {
                extApiLogService.updateResponse(mainLog, responseEntity, 1, DcpConstants.TASKS_STATUS_SUCCESS);
                return JSONObject.toJSONString(erpResponse.getData());
            } else {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                log.error("同步ERP高价值物料请求报错，{}!", erpResponse.getMessage());
                return null;
            }
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    protected List<MpsHighValueMaterialsDTO> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("ERP同步高价值物料数据为空！");
            return null;
        }
        List<MpsHighValueMaterialsDTO> highValueMaterialsDTOList = JSONObject.parseArray(body, MpsHighValueMaterialsDTO.class);
        return highValueMaterialsDTOList;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<MpsHighValueMaterialsDTO> o) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(o)) {
            log.error("同步高价值物料数据为空!");
            return null;
        }
        //筛选高价值为是的数据
        List<MpsHighValueMaterialsDTO> dtoList = o.stream().filter(x -> "是".equals(x.getHighValue()))
                .peek(x -> {
                    if (Objects.nonNull(x.getHighValue())) {
                        x.setHighValue("Y");
                    }
                })
                .collect(Collectors.toList());
        if (!dtoList.isEmpty()) {
            mpsFeign.createNewData(params.get("scenario").toString(), dtoList);
        }
        return "同步数据成功";
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.ERP.getCode(), ApiCategoryEnum.HIGH_VALUE_PRODUCT.getCode());
    }

}
