package com.yhl.scp.dcp.externalApi.handler.erp;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.mds.substitution.dto.ProductSubstitutionRelationshipDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * description: 同步ERP的BOM替代料关系
 * author：李杰
 * email: <EMAIL>
 * date: 2024/11/13
 */
@Component
@Slf4j
public class ProductSubstitutionHandler extends SyncDataHandler<List<ProductSubstitutionRelationshipDTO>> {

    @Resource
    private AuthHandler authHandler;

    @Override
    protected String getSyncGroupValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        return (String) params.get("organizationCode");
    }

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<ProductSubstitutionRelationshipDTO> productSubstitutionRelationshipDTO) {
        Date lastUpdateDate = productSubstitutionRelationshipDTO.stream().map(ProductSubstitutionRelationshipDTO::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        if (log.isInfoEnabled()) {
            log.info("开始同步ERP的BOM替代料关系:{},{}", apiConfigVO, params);
        }
        try {
            //获取ERP的token
            String erpToken = authHandler.handle(MapUtil.newHashMap());
            //BOM替代料关系地址接口
            String apiUri = apiConfigVO.getApiUri();
            String systemNumber = apiConfigVO.getSystemNumber();
            String lastUpdateStr = this.getSyncRefValue(apiConfigVO, params);
            apiUri = apiUri + "/" + systemNumber + "/" + this.sequenceService.getSuffix(systemNumber, getCommand(), 5)
                    + "?token=" + erpToken + "&lastUpdateDate=" + lastUpdateStr + "&organizationCode=" + params.get("organizationCode").toString();

            log.info("apiUri={},systemNumber={}", apiUri, systemNumber);
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(apiUri, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                throw new BusinessException(StrUtil.format("同步ERP的BOM替代料关系失败,HTTP状态码:{}!", statusCodeValue));
            }
            ErpResponse erpResponse = JSONObject.parseObject(responseEntity.getBody(), ErpResponse.class);
            if (log.isInfoEnabled()) {
                log.info("同步ERP的BOM替代料关系完成,返回数据:{}!", JSONUtil.toJsonStr(erpResponse));
            }
            if (erpResponse.getSuccess()) {
                Object data = erpResponse.getData();
                extApiLogService.updateResponse(mainLog, null, 1, DcpConstants.TASKS_STATUS_SUCCESS);
                return JSONObject.toJSONString(data);
            } else {
                log.error("同步ERP的BOM替代料关系报错，{}!", erpResponse.getMessage());
                return "";
            }
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    protected List<ProductSubstitutionRelationshipDTO> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            return null;
        }
        List<ProductSubstitutionRelationshipDTO> routingStepInputSyncDTOList = JSONObject.parseArray(body, ProductSubstitutionRelationshipDTO.class);
        return routingStepInputSyncDTOList;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<ProductSubstitutionRelationshipDTO> list) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(list)) {
            log.error("ERP同步BOM替代料关系数据为空");
            return null;
        }
        //获取模块标识
        list.stream().forEach(x -> {
            x.setEnabled(StringUtils.isEmpty(x.getEnabled()) ? YesOrNoEnum.NO.getCode() :
                    ("有效".equals(x.getEnabled()) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode())
            );
            x.setIsDeleted("Y".equals(x.getIsDeleted()) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
        });
        newMdsFeign.syncProductSubstitutionData(params.get("scenario").toString(), list, params.get("organizationCode").toString());
        this.saveSyncCtrl(apiConfigVO, params, list);
        return "同步数据成功";
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.ERP.getCode(), ApiCategoryEnum.PRODUCT_SUBSTITUTION.getCode());
    }
}
