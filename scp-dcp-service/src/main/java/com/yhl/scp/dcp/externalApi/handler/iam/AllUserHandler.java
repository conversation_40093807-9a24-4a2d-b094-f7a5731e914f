package com.yhl.scp.dcp.externalApi.handler.iam;

import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.IamResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.ips.rbac.dto.UserDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;

/**
 * description:全量同步用户
 * author：李杰
 * email: <EMAIL>
 * date: 2025-06-17
 */
@Component
@Slf4j
public class AllUserHandler extends SyncDataHandler<List<UserDTO>> {

    @Resource
    private AuthHandler iamAuthHandler;

    @Resource
    private UserCallbackHandler userCallbackHandler;

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map params) {
        if (log.isInfoEnabled()) {
            log.info("开始全量同步IAM的账户数据:{},{}", apiConfigVO, params);
        }
        String token = iamAuthHandler.getToken();
        String apiUri = apiConfigVO.getApiUri();
        String systemNumber = apiConfigVO.getSystemNumber();
        String url = apiUri + "/" + systemNumber + "/" + this.sequenceService.getSuffix(systemNumber, getCommand(), 5);
        if (log.isInfoEnabled()) {
            log.info("token={},url={}", token, url);
        }
        //设置请求头
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.set("Authorization", token);
        //设置请求体
        HashMap<String, Object> map = new HashMap<>();
        int size = Objects.isNull(apiConfigVO.getOffsetSize()) ? 5000 : apiConfigVO.getOffsetSize();
        //数据集合
        List<UserDTO> arrayList = new ArrayList<>();
        for (int page = 1; ; page++) {
            map.put("page", page);
            map.put("size", size);
            if (log.isInfoEnabled()) {
                log.info("url={},map={}", url, map);
            }
            HttpEntity httpEntity = new HttpEntity(JSONObject.toJSONString(map), httpHeaders);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            Assert.isTrue(HttpStatus.OK.value() == statusCodeValue, "IAM全量同步用户失败！");
            String body = responseEntity.getBody();
            log.info("全量同步IAM的用户数据请求完成，返回数据:{}!", body);
            IamResponse iamResponse = JSONObject.parseObject(body, IamResponse.class);
            if ("0".equals(iamResponse.getCode())) {
                String str = JSONObject.toJSONString(iamResponse.getData());
                Map<String, Object> map1 = JSONObject.parseObject(str, Map.class);
                List<UserDTO> object = (List) map1.get("list");
                arrayList.addAll(object);
                if (Integer.parseInt(map1.get("total").toString()) < size) {
                    return JSONObject.toJSONString(arrayList);
                }
            } else {
                log.error("IAM全量同步用户报错，{}", iamResponse.getMsg());
                return null;
            }
        }
    }

    @Override
    protected List<UserDTO> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            return null;
        }
        List<Map> list = JSONObject.parseArray(body, Map.class);
        List<UserDTO> arrayList = new ArrayList<>();
        list.stream().forEach(x -> {
                    if (StringUtils.isNotEmpty(x.get("idt_user__user_name").toString())) {
                        arrayList.add(JSONObject.parseObject(JSONObject.toJSONString(x), UserDTO.class));
                    }
                }
        );
        return arrayList;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<UserDTO> list) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);

        if (CollectionUtils.isEmpty(list)) {
            log.error("IAM全量同步用户数据为空");
            return null;
        }
        BaseResponse<String> baseResponse = newIpsFeign.syncAllUserData(list);
        if (baseResponse.getSuccess()) {
            this.saveSyncCtrl(apiConfigVO, params, list);
            return "同步数据成功";
        } else {
            log.error("全量同步IAM帐号报错，{}", baseResponse.getMsg());
            throw new BusinessException("全量同步IAM帐号报错" + baseResponse.getMsg());
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.IAM.getCode(), ApiCategoryEnum.REAL_ALL_USER.getCode());
    }

}
