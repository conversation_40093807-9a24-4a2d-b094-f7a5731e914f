package com.yhl.scp.mds.curingTime.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesCuringTime;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.curingTime.dto.MdsCuringTimeDTO;
import com.yhl.scp.mds.curingTime.vo.MdsCuringTimeVO;

import java.util.List;

/**
 * <code>MdsCuringTimeService</code>
 * <p>
 * mes固化时间接口同步中间表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-24 15:23:37
 */
public interface MdsCuringTimeService extends BaseService<MdsCuringTimeDTO, MdsCuringTimeVO> {

    /**
     * 查询所有
     *
     * @return list {@link MdsCuringTimeVO}
     */
    List<MdsCuringTimeVO> selectAll();
    BaseResponse<Void> doSync(String scenario);
    BaseResponse<Void> handleSyncCuringTime(Scenario scenario);

}
