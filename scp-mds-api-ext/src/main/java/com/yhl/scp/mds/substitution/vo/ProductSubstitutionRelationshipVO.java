package com.yhl.scp.mds.substitution.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>ProductSubstitutionRelationshipVO</code>
 * <p>
 * 物料替代关系VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-20 11:32:20
 */
@ApiModel(value = "物料替代关系VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductSubstitutionRelationshipVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 375101713002260970L;

    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    @FieldInterpretation(value = "库存点代码")
    private String stockPointCode;
    /**
     * 库存点名称
     */
    @ApiModelProperty(value = "库存点名称")
    @FieldInterpretation(value = "库存点名称")
    private String stockPointName;
    /**
     * 工序编码
     */
    @ApiModelProperty(value = "工序编码")
    @FieldInterpretation(value = "工序编码")
    private String operationCode;
    /**
     * 工序名称
     */
    @ApiModelProperty(value = "工序名称")
    @FieldInterpretation(value = "工序名称")
    private String operationName;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String productCode;
    /**
     * 物品名称
     */
    @ApiModelProperty(value = "物品名称")
    @FieldInterpretation(value = "物品名称")
    private String productName;

    /**
     * 原物料Id
     */
    private String rawProductId;


    /**
     * 原物料编码
     */
    @ApiModelProperty(value = "原物料编码")
    @FieldInterpretation(value = "原物料编码")
    private String rawProductCode;
    /**
     * 原物料名称
     */
    @ApiModelProperty(value = "原物料名称")
    @FieldInterpretation(value = "原物料名称")
    private String rawProductName;

    /**
     * 替代料id
     */
    @ApiModelProperty(value = "替代料id")
    @FieldInterpretation(value = "替代料id")
    private String substituteProductId;

    /**
     * 替代料编码
     */
    @ApiModelProperty(value = "替代料编码")
    @FieldInterpretation(value = "替代料编码")
    private String substituteProductCode;
    /**
     * 替代料名称
     */
    @ApiModelProperty(value = "替代料名称")
    @FieldInterpretation(value = "替代料名称")
    private String substituteProductName;
    /**
     * 单位替代量
     */
    @ApiModelProperty(value = "单位替代量")
    @FieldInterpretation(value = "单位替代量")
    private BigDecimal unitSubstitute;
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    @FieldInterpretation(value = "优先级")
    private Integer priority;
    /**
     * 生效时间
     */
    @ApiModelProperty(value = "生效时间")
    @FieldInterpretation(value = "生效时间")
    private Date effectiveTime;
    /**
     * 失效时间
     */
    @ApiModelProperty(value = "失效时间")
    @FieldInterpretation(value = "失效时间")
    private Date failureTime;

    /**
     * 长
     */
    @ApiModelProperty(value = "长")
    @FieldInterpretation(value = "长")
    private BigDecimal length;

    /**
     * 宽
     */
    @ApiModelProperty(value = "宽")
    @FieldInterpretation(value = "宽")
    private BigDecimal wide;

    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    @FieldInterpretation(value = "颜色")
    private String color;

    /**
     * 厚度
     */
    @ApiModelProperty(value = "厚度")
    @FieldInterpretation(value = "厚度")
    private BigDecimal thickness;

    /**
     * 原物品单位
     */
    @ApiModelProperty(value = "原物料单位")
    @FieldInterpretation(value = "原物料单位")
    private String rawUnit;

    /**
     * 替代料单位
     */
    @ApiModelProperty(value = "替代料单位")
    @FieldInterpretation(value = "替代料单位")
    private String subUnit;

    /**
     * 需求计算规则
     */
    @ApiModelProperty(value = "需求计算规则")
    @FieldInterpretation(value = "需求计算规则")
    private String rule;

    /**
     * 物料分类
     */
    @ApiModelProperty(value = "物料分类")
    @FieldInterpretation(value = "物料分类")
    private String productClassify;
    /**
     * erp最近更新时间
     */
    @ApiModelProperty(value = "erp最近更新时间")
    @FieldInterpretation(value = "erp最近更新时间")
    private Date lastUpdateDate;
    /**
     * bom头id
     */
    @ApiModelProperty(value = "bom头id")
    @FieldInterpretation(value = "bom头id")
    private String billSequenceId;
    /**
     * bom行id
     */
    @ApiModelProperty(value = "bom行id")
    @FieldInterpretation(value = "bom行id")
    private String componentSequenceId;

    /**
     * 单位输入量
     */
    @ApiModelProperty(value = "单位输入量")
    @FieldInterpretation(value = "单位输入量")
    private BigDecimal inputFactor;

    /**
     * 数据来源
     */
    @ApiModelProperty(value = "数据来源")
    @FieldInterpretation(value = "数据来源")
    private String dataSource;

    /**
     * 切拉膜系数
     */
    @ApiModelProperty(value = "切拉膜系数")
    @ExcelProperty(value = "切拉膜系数*")
    private BigDecimal filmCoefficient;

    /**
     * 物品类型
     */
    @ApiModelProperty(value = "物品类型")
    @ExcelProperty(value = "物品类型")
    private String productCategory;
    /**
     * 是/否禁用主料库存
     */
    @ApiModelProperty(value = "是/否禁用主料库存")
    @FieldInterpretation(value = "是/否禁用主料库存")
    private String isDisableMasterProduct;
    /**
     * 替代类型
     */
    @ApiModelProperty(value = "替代类型")
    @FieldInterpretation(value = "替代类型")
    private String substitutionType;
    /**
     * 替代类型描述
     */
    @ApiModelProperty(value = "替代类型描述")
    @FieldInterpretation(value = "替代类型描述")
    private String substitutionTypeDesc;
    /**
     * 是否已经删除  YES代表已删除，NO代表未删除
     */
    @ApiModelProperty(value = "是否已经删除，YES代表已删除，NO代表未删除")
    @FieldInterpretation(value = "是否已经删除，YES代表已删除，NO代表未删除")
    private String isDeleted;
    @Override
    public void clean() {

    }

}
