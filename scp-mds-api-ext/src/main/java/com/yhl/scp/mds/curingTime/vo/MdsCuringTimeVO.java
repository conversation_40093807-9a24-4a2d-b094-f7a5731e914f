package com.yhl.scp.mds.curingTime.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MdsCuringTimeVO</code>
 * <p>
 * mes固化时间接口同步中间表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-24 15:23:37
 */
@ApiModel(value = "mes固化时间接口同步中间表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MdsCuringTimeVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -28870493589559290L;

    /**
     * 物品编码
     */
    @ApiModelProperty(value = "物品编码")
    @FieldInterpretation(value = "物品编码")
    private String productCode;
    /**
     * 公司代码
     */
    @ApiModelProperty(value = "公司代码")
    @FieldInterpretation(value = "公司代码")
    private String companyCode;
    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    @FieldInterpretation(value = "库存点代码")
    private String stockPointCode;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    @FieldInterpretation(value = "最后更新时间")
    private Date lastUpdateDate;
    /**
     * 是否固化
     */
    @ApiModelProperty(value = "是否固化")
    @FieldInterpretation(value = "是否固化")
    private String ghFlag;
    /**
     * 固化时长
     */
    @ApiModelProperty(value = "固化时长")
    @FieldInterpretation(value = "固化时长")
    private String ghTime;
    /**
     * 原创建人
     */
    @ApiModelProperty(value = "原创建人")
    @FieldInterpretation(value = "原创建人")
    private String createUser;
    @ApiModelProperty(value = "${column.comment}")
    @FieldInterpretation(value = "${column.comment}")
    private String kid;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @FieldInterpretation(value = "版本")
    private Integer versionValue;
    /**
     * 包装前固化时长
     */
    @ApiModelProperty(value = "包装前固化时长")
    @FieldInterpretation(value = "包装前固化时长")
    private String prePackageCuringTime;
    /**
     * 包装后固化时长
     */
    @ApiModelProperty(value = "包装后固化时长")
    @FieldInterpretation(value = "包装后固化时长")
    private String postPackageCuringTime;
    @Override
    public void clean() {

    }

}
