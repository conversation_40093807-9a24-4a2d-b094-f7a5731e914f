<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao">
    <resultMap id="BaseResultMap" extends="com.yhl.scp.sds.order.infrastructure.dao.OperationTaskBasicDao.BaseResultMap"
               type="com.yhl.scp.sds.extension.order.infrastructure.po.OperationTaskPO">
        <!--项目自定义字段,参考示例-->
        <!--<result column="new_column" jdbcType="TIMESTAMP" property="newColumn"/>-->
    </resultMap>
    <resultMap id="VOResultMap" extends="com.yhl.scp.sds.order.infrastructure.dao.OperationTaskBasicDao.VOResultMap"
               type="com.yhl.scp.mps.plan.vo.MasterPlanTaskVO">
        <!--项目自定义字段,参考示例-->
        <!--<result column="new_column" jdbcType="TIMESTAMP" property="newColumn"/>-->
        <result column="standard_step_id" jdbcType="VARCHAR" property="standardStepId"/>
        <result column="standard_step_type" jdbcType="VARCHAR" property="standardStepType"/>
        <result column="standard_step_name" jdbcType="VARCHAR" property="standardStepName"/>
        <result column="standard_step_code" jdbcType="VARCHAR" property="standardStepCode"/>
        <result column="operation_code" jdbcType="VARCHAR" property="operationCode"/>
        <result column="routing_step_sequence_no" jdbcType="INTEGER" property="routingStepNo"/>
        <result column="product_stock_point_code" jdbcType="VARCHAR" property="productStockPointCode"/>
        <result column="box_type" jdbcType="VARCHAR" property="boxType"/>
    </resultMap>
    <resultMap id="WorkOrderBaseResultMap"
               extends="com.yhl.scp.sds.order.infrastructure.dao.WorkOrderBasicDao.BaseResultMap"
               type="com.yhl.scp.sds.extension.order.infrastructure.po.WorkOrderPO">
        <!--项目自定义字段,参考示例-->
        <!--<result column="new_column" jdbcType="TIMESTAMP" property="newColumn"/>-->
    </resultMap>
    <resultMap id="OperationVOResultMap"
               extends="com.yhl.scp.sds.order.infrastructure.dao.OperationDao.VOResultMap"
               type="com.yhl.scp.sds.extension.order.vo.OperationVO">
    </resultMap>
    <resultMap id="WarehouseReleaseRecordVOMap"
               type="com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO">
        <result column="item_code" jdbcType="VARCHAR" property="itemCode"/>
        <result column="sum_qty" jdbcType="VARCHAR" property="sumQty"/>
    </resultMap>
    <resultMap id="PhysicalResourceBaseResultMap"
               type="com.yhl.scp.mds.extension.resource.infrastructure.po.PhysicalResourcePO">
        <!--@Table mds_res_physical_resource-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="standard_resource_id" jdbcType="VARCHAR" property="standardResourceId"/>
        <result column="physical_resource_code" jdbcType="VARCHAR" property="physicalResourceCode"/>
        <result column="physical_resource_name" jdbcType="VARCHAR" property="physicalResourceName"/>
        <result column="resource_category" jdbcType="VARCHAR" property="resourceCategory"/>
        <result column="resource_type" jdbcType="VARCHAR" property="resourceType"/>
        <result column="resource_classification" jdbcType="VARCHAR" property="resourceClassification"/>
        <result column="subtask_type" jdbcType="VARCHAR" property="subtaskType"/>
        <result column="assign_quantity_type" jdbcType="VARCHAR" property="assignQuantityType"/>
        <result column="display_index" jdbcType="INTEGER" property="displayIndex"/>
        <result column="bottleneck" jdbcType="VARCHAR" property="bottleneck"/>
        <result column="infinite_capacity" jdbcType="VARCHAR" property="infiniteCapacity"/>
        <result column="sequence_code" jdbcType="VARCHAR" property="sequenceCode"/>
        <result column="variable_work_hours" jdbcType="VARCHAR" property="variableWorkHours"/>
        <result column="resource_quantity_coefficient" jdbcType="VARCHAR" property="resourceQuantityCoefficient"/>
        <result column="counting_unit_id" jdbcType="VARCHAR" property="countingUnitId"/>
        <result column="production_efficiency" jdbcType="VARCHAR" property="productionEfficiency"/>
        <result column="setup_efficiency" jdbcType="VARCHAR" property="setupEfficiency"/>
        <result column="cleanup_efficiency" jdbcType="VARCHAR" property="cleanupEfficiency"/>
        <result column="setup_duration" jdbcType="INTEGER" property="setupDuration"/>
        <result column="cleanup_duration" jdbcType="INTEGER" property="cleanupDuration"/>
        <result column="buffer_time_before" jdbcType="INTEGER" property="bufferTimeBefore"/>
        <result column="buffer_time_after" jdbcType="INTEGER" property="bufferTimeAfter"/>
        <result column="max_production_suspend_duration" jdbcType="INTEGER" property="maxProductionSuspendDuration"/>
        <result column="max_setup_suspend_duration" jdbcType="INTEGER" property="maxSetupSuspendDuration"/>
        <result column="max_cleanup_suspend_duration" jdbcType="INTEGER" property="maxCleanupSuspendDuration"/>
        <result column="production_line" jdbcType="VARCHAR" property="productionLine"/>
        <result column="strict_production_line_constraints" jdbcType="VARCHAR"
                property="strictProductionLineConstraints"/>
        <result column="no_buffer_action_type" jdbcType="VARCHAR" property="noBufferActionType"/>
        <result column="no_buffer_action_duration" jdbcType="INTEGER" property="noBufferActionDuration"/>
        <result column="lot_size" jdbcType="INTEGER" property="lotSize"/>
        <result column="max_lot_size" jdbcType="INTEGER" property="maxLotSize"/>
        <result column="production_date_last_num_change_unit" jdbcType="VARCHAR"
                property="productionDateLastNumChangeUnit"/>
        <result column="production_time_last_num_change_unit" jdbcType="VARCHAR"
                property="productionTimeLastNumChangeUnit"/>
        <result column="production_duration_logic" jdbcType="VARCHAR" property="productionDurationLogic"/>
        <result column="setup_and_cleanup_duration_logic" jdbcType="VARCHAR" property="setupAndCleanupDurationLogic"/>
        <result column="dynamic_setup_and_cleanup_duration_logic" jdbcType="VARCHAR"
                property="dynamicSetupAndCleanupDurationLogic"/>
        <result column="changeover_duration_logic" jdbcType="VARCHAR" property="changeoverDurationLogic"/>
        <result column="dynamic_changeover_duration_logic" jdbcType="VARCHAR"
                property="dynamicChangeoverDurationLogic"/>
        <result column="effective_time" jdbcType="TIMESTAMP" property="effectiveTime"/>
        <result column="expiry_time" jdbcType="TIMESTAMP" property="expiryTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="PhysicalResourceVOResultMap" extends="PhysicalResourceBaseResultMap"
               type="com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO">
        <result column="standard_resource_name" jdbcType="VARCHAR" property="standardResourceName"/>
        <result column="standard_resource_code" jdbcType="VARCHAR" property="standardResourceCode"/>
        <result column="counting_unit_code" jdbcType="VARCHAR" property="countingUnitCode"/>
        <result column="counting_unit_desc" jdbcType="VARCHAR" property="countingUnitDesc"/>
        <result column="production_planner" jdbcType="VARCHAR" property="productionPlanner"/>
    </resultMap>
    <resultMap id="StandardResourceVOResultMap"
               type="com.yhl.scp.mds.extension.resource.vo.StandardResourceVO">
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="standard_resource_name" jdbcType="VARCHAR" property="standardResourceName"/>
        <result column="standard_resource_code" jdbcType="VARCHAR" property="standardResourceCode"/>
    </resultMap>

    <resultMap id="ResourceCalendarMap"
               type="com.yhl.scp.mds.extension.calendar.vo.ResourceCalendarVO">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="organization_id" jdbcType="VARCHAR" property="organizationId"/>
        <result column="standard_resource_id" jdbcType="VARCHAR" property="standardResourceId"/>
        <result column="physical_resource_id" jdbcType="VARCHAR" property="physicalResourceId"/>
        <result column="rule_id" jdbcType="VARCHAR" property="ruleId"/>
        <result column="shift_id" jdbcType="VARCHAR" property="shiftId"/>
        <result column="shift_pattern" jdbcType="VARCHAR" property="shiftPattern"/>
        <result column="work_hours" jdbcType="VARCHAR" property="workHours"/>
        <result column="overtime_hours" jdbcType="VARCHAR" property="overtimeHours"/>
        <result column="work_day" jdbcType="TIMESTAMP" property="workDay"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="calendar_type" jdbcType="VARCHAR" property="calendarType"/>
        <result column="efficiency" jdbcType="VARCHAR" property="efficiency"/>
        <result column="resource_quantity" jdbcType="INTEGER" property="resourceQuantity"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <resultMap id="InventoryBatchDetailVOResultMap" type="com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="subinventory" jdbcType="VARCHAR" property="subinventory"/>
        <result column="subinventory_description" jdbcType="VARCHAR" property="subinventoryDescription"/>
        <result column="freight_space" jdbcType="VARCHAR" property="freightSpace"/>
        <result column="freight_space_description" jdbcType="VARCHAR" property="freightSpaceDescription"/>
        <result column="batch" jdbcType="VARCHAR" property="batch"/>
        <result column="bar_code" jdbcType="VARCHAR" property="barCode"/>
        <result column="current_quantity" jdbcType="VARCHAR" property="currentQuantity"/>
        <result column="customer_num" jdbcType="VARCHAR" property="customerNum"/>
        <result column="part_num" jdbcType="VARCHAR" property="partNum"/>
        <result column="assigned_time" jdbcType="VARCHAR" property="assignedTime"/>
        <result column="last_update_date" jdbcType="VARCHAR" property="lastUpdateDate"/>
        <result column="stock_age" jdbcType="VARCHAR" property="stockAge"/>
        <result column="stock_age_day" jdbcType="VARCHAR" property="stockAgeDay"/>
        <result column="warranty_date" jdbcType="VARCHAR" property="warrantyDate"/>
        <result column="distance_enable_date" jdbcType="VARCHAR" property="distanceEnableDate"/>
        <result column="source_type" jdbcType="VARCHAR" property="sourceType"/>
        <result column="original_org_id" jdbcType="VARCHAR" property="originalOrgId"/>
        <result column="allocation_status" jdbcType="VARCHAR" property="allocationStatus"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="operation_code" jdbcType="VARCHAR" property="operationCode"/>
        <result column="measurement_unit" jdbcType="VARCHAR" property="measurementUnit"/>
        <result column="loading_Position" jdbcType="VARCHAR" property="loadingPosition"/>
    </resultMap>

    <resultMap id="AdjustOperationVOMap" type="com.yhl.scp.mps.algorithm.schedule.output.RzzOperationDTO">
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="plan_status" jdbcType="VARCHAR" property="planStatus"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="earliest_start_time" jdbcType="TIMESTAMP" property="earliestStartTime"/>
        <result column="latest_end_time" jdbcType="TIMESTAMP" property="latestEndTime"/>
        <result column="calc_earliest_start_time" jdbcType="TIMESTAMP" property="calcEarliestStartTime"/>
        <result column="calc_latest_end_time" jdbcType="TIMESTAMP" property="calcLatestEndTime"/>
    </resultMap>

    <!-- 查询工序供应数据 -->
    <resultMap id="DemandVOResultMap" extends="com.yhl.scp.sds.pegging.infrastructure.dao.DemandBasicDao.VOResultMap"
               type="com.yhl.scp.sds.extension.pegging.vo.DemandVO">
        <result column="operation_id" jdbcType="VARCHAR" property="operationId"/>
    </resultMap>

    <resultMap id="SupplyVOResultMap" extends="com.yhl.scp.sds.pegging.infrastructure.dao.SupplyBasicDao.VOResultMap"
               type="com.yhl.scp.sds.extension.pegging.vo.SupplyVO">
    </resultMap>
    <resultMap id="FeedBackResultMap"
               extends="com.yhl.scp.sds.feedback.infrastructure.dao.FeedbackProductionBasicDao.BaseResultMap"
               type="com.yhl.scp.sds.extension.feedback.infrastructure.po.FeedbackProductionPO">
        <!--项目自定义字段,参考示例-->
        <!--<result column="new_column" jdbcType="TIMESTAMP" property="newColumn"/>-->
    </resultMap>

    <resultMap id="MasterPlanWorkOrderVOResultMap"
               extends="com.yhl.scp.sds.order.infrastructure.dao.WorkOrderBasicDao.VOResultMap"
               type="com.yhl.scp.sds.extension.order.vo.WorkOrderVO">
        <!--项目自定义字段,参考示例-->
        <!--<result column="new_column" jdbcType="TIMESTAMP" property="newColumn"/>-->
    </resultMap>

    <resultMap id="CuringTimeMap" type="com.yhl.scp.mds.curingTime.vo.MasterPlanCuringTimeVO">
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="curing_time" jdbcType="DECIMAL" property="curingTime"/>
    </resultMap>
    <resultMap id="InventoryBatchDetailResultMap" type="com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="stock_point_code" property="stockPointCode" jdbcType="VARCHAR"/>
        <result column="product_code" property="productCode" jdbcType="VARCHAR"/>
        <result column="original_product_code" property="originalProductCode" jdbcType="VARCHAR"/>
        <result column="operation_code" property="operationCode" jdbcType="VARCHAR"/>
        <result column="subinventory" property="subinventory" jdbcType="VARCHAR"/>
        <result column="freightSpace" property="freightSpace" jdbcType="VARCHAR"/>
        <result column="current_quantity" property="currentQuantity" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="ResourceCalendar_Column_List">
        id
        ,organization_id,standard_resource_id,physical_resource_id,rule_id,shift_id,shift_pattern,work_hours,overtime_hours,
        work_day,priority,calendar_type,efficiency,resource_quantity,start_time,end_time,remark,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="ResourceCalendar_Where_Condition">
        <if test="params.id != null and params.id != ''">
            and id = #{params.id,jdbcType=VARCHAR}
        </if>
        <if test="params.standardResourceId != null and params.standardResourceId != ''">
            and standard_resource_id = #{params.standardResourceId,jdbcType=VARCHAR}
        </if>
        <if test="params.organizationId != null and params.organizationId != ''">
            and organization_id = #{params.organizationId,jdbcType=VARCHAR}
        </if>
        <if test="params.physicalResourceId != null and params.physicalResourceId != ''">
            and physical_resource_id = #{params.physicalResourceId,jdbcType=VARCHAR}
        </if>
        <if test="params.physicalResourceIds != null and params.physicalResourceIds.size()>0">
            and physical_resource_id in
            <foreach collection="params.physicalResourceIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.ruleId != null and params.ruleId != ''">
            and rule_id = #{params.ruleId,jdbcType=VARCHAR}
        </if>
        <if test="params.shiftId != null and params.shiftId != ''">
            and shift_id = #{params.shiftId,jdbcType=VARCHAR}
        </if>
        <if test="params.shiftPattern != null and params.shiftPattern != ''">
            and shift_pattern = #{params.shiftPattern,jdbcType=VARCHAR}
        </if>
        <if test="params.workHours != null">
            and work_hours = #{params.workHours,jdbcType=VARCHAR}
        </if>
        <if test="params.overtimeHours != null">
            and overtime_hours = #{params.overtimeHours,jdbcType=VARCHAR}
        </if>
        <if test="params.workDay != null">
            and work_day = #{params.workDay,jdbcType=TIMESTAMP}
        </if>
        <if test="params.workDayList != null and params.workDayList.size()>0">
            and work_day in
            <foreach collection="params.workDayList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.priority != null">
            and priority = #{params.priority,jdbcType=INTEGER}
        </if>
        <if test="params.calendarType != null and params.calendarType != ''">
            and calendar_type = #{params.calendarType,jdbcType=VARCHAR}
        </if>
        <if test="params.efficiency != null">
            and efficiency = #{params.efficiency,jdbcType=VARCHAR}
        </if>
        <if test="params.resourceQuantity != null">
            and resource_quantity = #{params.resourceQuantity,jdbcType=INTEGER}
        </if>
        <if test="params.startTime != null">
            and start_time = #{params.startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="params.endTime != null">
            and end_time = #{params.endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="params.remark != null and params.remark != ''">
            and remark = #{params.remark,jdbcType=VARCHAR}
        </if>
        <if test="params.enabled != null and params.enabled != ''">
            and enabled = #{params.enabled,jdbcType=VARCHAR}
        </if>
        <if test="params.creator != null and params.creator != ''">
            and creator = #{params.creator,jdbcType=VARCHAR}
        </if>
        <if test="params.createTime != null">
            and create_time = #{params.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="params.modifier != null and params.modifier != ''">
            and modifier = #{params.modifier,jdbcType=VARCHAR}
        </if>
        <if test="params.modifyTime != null">
            and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
        </if>
    </sql>

    <sql id="Base_Column_List">
        <!--项目自定义字段,参考示例,逗号不能缺失-->
        <include refid="com.yhl.scp.sds.order.infrastructure.dao.OperationTaskBasicDao.Base_Column_List"/>
        <!--,new_column-->
    </sql>
    <sql id="VO_Column_List">
        <!--项目自定义字段,参考示例,逗号不能缺失-->
        <include refid="com.yhl.scp.sds.order.infrastructure.dao.OperationTaskBasicDao.VO_Column_List"/>
        <!--,new_column-->
    </sql>
    <sql id="Base_Where_Condition">
        <include refid="com.yhl.scp.sds.order.infrastructure.dao.OperationTaskBasicDao.Base_Where_Condition"/>
        <!--项目自定义查询条件,参考示例-->
        <!--<if test="params.newColumn != null and params.newColumn != ''">
            and new_column = #{params.newColumn,jdbcType=VARCHAR}
        </if>-->
    </sql>
    <update id="updateBatchOperations" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update sds_ord_operation
            <set>
                operation_code = #{item.operationCode,jdbcType=VARCHAR},
                order_id = #{item.orderId,jdbcType=VARCHAR},
                plan_unit_id = #{item.planUnitId,jdbcType=VARCHAR},
                routing_step_id = #{item.routingStepId,jdbcType=VARCHAR},
                standard_step_id = #{item.standardStepId,jdbcType=VARCHAR},
                routing_step_sequence_no = #{item.routingStepSequenceNo,jdbcType=INTEGER},
                pre_routing_step_sequence_no = #{item.preRoutingStepSequenceNo,jdbcType=VARCHAR},
                next_routing_step_sequence_no = #{item.nextRoutingStepSequenceNo,jdbcType=VARCHAR},
                product_stock_point_id = #{item.productStockPointId,jdbcType=VARCHAR},
                product_id = #{item.productId,jdbcType=VARCHAR},
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
                quantity = #{item.quantity,jdbcType=VARCHAR},
                planned_resource_id = #{item.plannedResourceId,jdbcType=VARCHAR},
                frozen = #{item.frozen,jdbcType=VARCHAR},
                plan_status = #{item.planStatus,jdbcType=VARCHAR},
                order_type = #{item.orderType,jdbcType=VARCHAR},
                kit_status = #{item.kitStatus,jdbcType=VARCHAR},
                processing_time = #{item.processingTime,jdbcType=INTEGER},
                start_time = #{item.startTime,jdbcType=TIMESTAMP},
                end_time = #{item.endTime,jdbcType=TIMESTAMP},
                earliest_start_time = #{item.earliestStartTime,jdbcType=TIMESTAMP},
                latest_end_time = #{item.latestEndTime,jdbcType=TIMESTAMP},
                calc_earliest_start_time = #{item.calcEarliestStartTime,jdbcType=TIMESTAMP},
                calc_latest_end_time = #{item.calcLatestEndTime,jdbcType=TIMESTAMP},
                connection_task = #{item.connectionTask,jdbcType=VARCHAR},
                connection_type = #{item.connectionType,jdbcType=VARCHAR},
                max_connection_duration = #{item.maxConnectionDuration,jdbcType=INTEGER},
                min_connection_duration = #{item.minConnectionDuration,jdbcType=INTEGER},
                operation_index = #{item.operationIndex,jdbcType=INTEGER},
                parent_id = #{item.parentId,jdbcType=VARCHAR},
                partition_type = #{item.partitionType,jdbcType=VARCHAR},
                resource_fixed = #{item.resourceFixed,jdbcType=VARCHAR},
                time_fixed = #{item.timeFixed,jdbcType=VARCHAR},
                lock_status = #{item.lockStatus,jdbcType=VARCHAR},
                delay_reason = #{item.delayReason,jdbcType=VARCHAR},
                unscheduled_reason = #{item.unscheduledReason,jdbcType=VARCHAR},
                last_insertion = #{item.lastInsertion,jdbcType=VARCHAR},
                remark = #{item.remark,jdbcType=VARCHAR},
                enabled = #{item.enabled,jdbcType=VARCHAR},
                modifier = #{item.modifier,jdbcType=VARCHAR},
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                schedule_pre_operation_id = #{item.schedulePreOperationId,jdbcType=VARCHAR},
                schedule_next_operation_id = #{item.scheduleNextOperationId,jdbcType=VARCHAR},
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="updateBatchOperationExtend" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update sds_ord_operation_extend
            <set>
                pre_operation_ids = #{item.preOperationIds,jdbcType=VARCHAR},
                resource_ids = #{item.resourceIds,jdbcType=VARCHAR},
                next_operation_ids = #{item.nextOperationIds,jdbcType=VARCHAR},
                partition_type = #{item.partitionType,jdbcType=VARCHAR},
                resource_fixed = #{item.resourceFixed,jdbcType=VARCHAR},
                time_fixed = #{item.timeFixed,jdbcType=VARCHAR},
                appoint_production_time = #{item.appointProductionTime,jdbcType=TIMESTAMP},
                appoint_quantity = #{item.appointQuantity,jdbcType=INTEGER},
                appoint_main_resource_id = #{item.appointMainResourceId,jdbcType=VARCHAR},
                appoint_start_time = #{item.appointStartTime,jdbcType=TIMESTAMP},
                appoint_end_time = #{item.appointEndTime,jdbcType=TIMESTAMP},
                partition_rate = #{item.partitionRate,jdbcType=VARCHAR},
                partition_num = #{item.partitionNum,jdbcType=INTEGER},
                partition_batch = #{item.partitionBatch,jdbcType=VARCHAR},
                max_partition_batch = #{item.maxPartitionBatch,jdbcType=VARCHAR},
                min_partition_batch = #{item.minPartitionBatch,jdbcType=VARCHAR},
                partition_batch_unit = #{item.partitionBatchUnit,jdbcType=VARCHAR},
                partition_mantissa_deal = #{item.partitionMantissaDeal,jdbcType=VARCHAR},
                planned_main_resource_id = #{item.plannedMainResourceId,jdbcType=VARCHAR},
                last_main_resource_id = #{item.lastMainResourceId,jdbcType=VARCHAR},
                planned_tool_resource_id = #{item.plannedToolResourceId,jdbcType=VARCHAR},
                last_tool_resource_id = #{item.lastToolResourceId,jdbcType=VARCHAR},
                planned_skill_id = #{item.plannedSkillId,jdbcType=VARCHAR},
                last_skill_id = #{item.lastSkillId,jdbcType=VARCHAR},
                setup_start_time = #{item.setupStartTime,jdbcType=TIMESTAMP},
                last_setup_start_time = #{item.lastSetupStartTime,jdbcType=TIMESTAMP},
                setup_end_time = #{item.setupEndTime,jdbcType=TIMESTAMP},
                last_setup_end_time = #{item.lastSetupEndTime,jdbcType=TIMESTAMP},
                setup_duration = #{item.setupDuration,jdbcType=INTEGER},
                production_start_time = #{item.productionStartTime,jdbcType=TIMESTAMP},
                last_production_start_time = #{item.lastProductionStartTime,jdbcType=TIMESTAMP},
                production_end_time = #{item.productionEndTime,jdbcType=TIMESTAMP},
                last_production_end_time = #{item.lastProductionEndTime,jdbcType=TIMESTAMP},
                lock_start_time = #{item.lockStartTime,jdbcType=TIMESTAMP},
                last_lock_start_time = #{item.lastLockStartTime,jdbcType=TIMESTAMP},
                lock_end_time = #{item.lockEndTime,jdbcType=TIMESTAMP},
                last_lock_end_time = #{item.lastLockEndTime,jdbcType=TIMESTAMP},
                lock_duration = #{item.lockDuration,jdbcType=INTEGER},
                cleanup_start_time = #{item.cleanupStartTime,jdbcType=TIMESTAMP},
                last_cleanup_start_time = #{item.lastCleanupStartTime,jdbcType=TIMESTAMP},
                cleanup_end_time = #{item.cleanupEndTime,jdbcType=TIMESTAMP},
                last_cleanup_end_time = #{item.lastCleanupEndTime,jdbcType=TIMESTAMP},
                cleanup_duration = #{item.cleanupDuration,jdbcType=INTEGER},
                lock_status = #{item.lockStatus,jdbcType=VARCHAR},
                delay_reason = #{item.delayReason,jdbcType=VARCHAR},
                unscheduled_reason = #{item.unscheduledReason,jdbcType=VARCHAR},
                feed_finish_time = #{item.feedFinishTime,jdbcType=TIMESTAMP},
                last_insertion = #{item.lastInsertion,jdbcType=VARCHAR},
                operation_type = #{item.operationType,jdbcType=VARCHAR},
                remark = #{item.remark,jdbcType=VARCHAR},
                enabled = #{item.enabled,jdbcType=VARCHAR},
                modifier = #{item.modifier,jdbcType=VARCHAR},
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </set>
            where operation_id = #{item.operationId,jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="updateBatchWorkOrder" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update sds_ord_work_order
            <set>
                order_no = #{item.orderNo,jdbcType=VARCHAR},
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
                product_id = #{item.productId,jdbcType=VARCHAR},
                organization_id = #{item.organizationId,jdbcType=VARCHAR},
                parent_id = #{item.parentId,jdbcType=VARCHAR},
                bom_version_id = #{item.bomVersionId,jdbcType=VARCHAR},
                upper_order_id = #{item.upperOrderId,jdbcType=VARCHAR},
                top_order_id = #{item.topOrderId,jdbcType=VARCHAR},
                lower_order_id = #{item.lowerOrderId,jdbcType=VARCHAR},
                bottom_order_id = #{item.bottomOrderId,jdbcType=VARCHAR},
                batch_split = #{item.batchSplit,jdbcType=VARCHAR},
                quantity = #{item.quantity,jdbcType=VARCHAR},
                amount = #{item.amount,jdbcType=VARCHAR},
                order_time = #{item.orderTime,jdbcType=TIMESTAMP},
                due_date = #{item.dueDate,jdbcType=TIMESTAMP},
                delay_penalty = #{item.delayPenalty,jdbcType=VARCHAR},
                order_status = #{item.orderStatus,jdbcType=VARCHAR},
                priority = #{item.priority,jdbcType=INTEGER},
                delay_status = #{item.delayStatus,jdbcType=VARCHAR},
                fixed = #{item.fixed,jdbcType=VARCHAR},
                feedback_status = #{item.feedbackStatus,jdbcType=VARCHAR},
                operation_conflict = #{item.operationConflict,jdbcType=VARCHAR},
                over_production = #{item.overProduction,jdbcType=VARCHAR},
                resync_allowed = #{item.resyncAllowed,jdbcType=VARCHAR},
                plan_status = #{item.planStatus,jdbcType=VARCHAR},
                kit_status = #{item.kitStatus,jdbcType=VARCHAR},
                sync_status = #{item.syncStatus,jdbcType=VARCHAR},
                start_time = #{item.startTime,jdbcType=TIMESTAMP},
                end_time = #{item.endTime,jdbcType=TIMESTAMP},
                earliest_start_time = #{item.earliestStartTime,jdbcType=TIMESTAMP},
                calc_earliest_start_time = #{item.calcEarliestStartTime,jdbcType=TIMESTAMP},
                calc_latest_end_time = #{item.calcLatestEndTime,jdbcType=TIMESTAMP},
                plannable = #{item.plannable,jdbcType=VARCHAR},
                appoint_plannable = #{item.appointPlannable,jdbcType=VARCHAR},
                routing_id = #{item.routingId,jdbcType=VARCHAR},
                appoint_routing_id = #{item.appointRoutingId,jdbcType=VARCHAR},
                routing_type = #{item.routingType,jdbcType=VARCHAR},
                lead_time = #{item.leadTime,jdbcType=INTEGER},
                appoint_lead_time = #{item.appointLeadTime,jdbcType=INTEGER},
                appoint_customer_id = #{item.appointCustomerId,jdbcType=VARCHAR},
                appoint_demand_type = #{item.appointDemandType,jdbcType=VARCHAR},
                appoint_demand_order_id = #{item.appointDemandOrderId,jdbcType=VARCHAR},
                appoint_customer_order_id = #{item.appointCustomerOrderId,jdbcType=VARCHAR},
                appoint_parent_work_order_id = #{item.appointParentWorkOrderId,jdbcType=VARCHAR},
                sync_failure_reason = #{item.syncFailureReason,jdbcType=VARCHAR},
                stocking_quantity = #{item.stockingQuantity,jdbcType=VARCHAR},
                planned_production_date = #{item.plannedProductionDate,jdbcType=TIMESTAMP},
                planned_output_date = #{item.plannedOutputDate,jdbcType=TIMESTAMP},
                fulfilled_quantity = #{item.fulfilledQuantity,jdbcType=VARCHAR},
                fulfillment_info = #{item.fulfillmentInfo,jdbcType=VARCHAR},
                unfulfilled_quantity = #{item.unfulfilledQuantity,jdbcType=VARCHAR},
                fulfillment_status = #{item.fulfillmentStatus,jdbcType=VARCHAR},
                counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
                currency_unit_id = #{item.currencyUnitId,jdbcType=VARCHAR},
                participate_scheduling = #{item.participateScheduling,jdbcType=VARCHAR},
                fixed_quantity_status = #{item.fixedQuantityStatus,jdbcType=VARCHAR},
                quantity_consistent = #{item.quantityConsistent,jdbcType=VARCHAR},
                remark = #{item.remark,jdbcType=VARCHAR},
                enabled = #{item.enabled,jdbcType=VARCHAR},
                modifier = #{item.modifier,jdbcType=VARCHAR},
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                ending_inventory_min_safe_diff = #{item.endingInventoryMinSafeDiff,jdbcType=INTEGER},
                demand_category = #{item.demandCategory,jdbcType=VARCHAR},
                order_type = #{item.orderType,jdbcType=VARCHAR},
                test_order_number = #{item.testOrderNumber,jdbcType=VARCHAR},
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="updateBatchWorkOrderByCaseWhen" parameterType="java.util.List">
        update sds_ord_work_order
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="plan_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.startTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.endTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="earliest_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.earliestStartTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="calc_earliest_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.calcEarliestStartTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="calc_latest_end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.calcLatestEndTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="delay_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.delayStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="updateOperationRemark" parameterType="java.lang.String">
        update sds_ord_operation
        set remark_info = #{remark,jdbcType=VARCHAR}
        where id = #{operationId,jdbcType=VARCHAR}
    </update>
    <update id="updateOperationFixedWorkHours" parameterType="java.lang.String">
        update sds_ord_operation
        set fixed_work_hours = #{fixedWorkHours,jdbcType=VARCHAR}
        where id = #{operationId,jdbcType=VARCHAR}
    </update>
    <update id="updateOperationTaskRemark" parameterType="java.lang.String">
        update sds_ord_operation_task
        set remark = #{remark,jdbcType=VARCHAR}
        where id = #{operationTaskId,jdbcType=VARCHAR}
    </update>
    <delete id="deleteTaskByOperationIds">
        delete from sds_ord_operation_task
        where id in (
        select id from(
        select id from sds_ord_operation_task
        where operation_id in
        <foreach collection="operationIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        ) as temp
        )
    </delete>

    <delete id="deleteSubTaskByOperationIds">
        delete from sds_ord_operation_sub_task
        where id in (
        select id from(
        select id from sds_ord_operation_sub_task
        where operation_id in
        <foreach collection="operationIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        ) as temp
        )
    </delete>

    <resultMap id="OperationPlanResultMap"
               extends="com.yhl.scp.sds.order.infrastructure.dao.OperationBasicDao.VOResultMap"
               type="com.yhl.scp.mps.plan.vo.OperationPlanVO">
        <result column="standard_step_type" jdbcType="VARCHAR" property="standardStepType"/>
        <result column="standard_step_name" jdbcType="VARCHAR" property="standardStepName"/>
        <result column="standard_step_id" jdbcType="VARCHAR" property="standardStepId"/>
        <result column="standard_step_code" jdbcType="VARCHAR" property="standardStepCode"/>
        <result column="work_order_kit_status" jdbcType="VARCHAR" property="workOrderKitStatus"/>
        <result column="order_code" jdbcType="VARCHAR" property="orderCode"/>
        <result column="work_order_due_date" jdbcType="TIMESTAMP" property="workOrderDuedate"/>
        <result column="box_type" jdbcType="VARCHAR" property="boxType"/>
    </resultMap>

    <select id="selectUnPlanOperation" resultMap="OperationPlanResultMap">
        SELECT so.id as id,
        so.order_id as order_id,
        so.routing_step_id as routing_step_id,
        so.quantity as quantity,
        so.product_id as product_id,
        so.standard_step_id as standard_step_id,
        so.plan_status as plan_status,
        so.routing_step_sequence_no as routing_step_sequence_no,
        rso.standard_step_type AS standard_step_type,
        rso.standard_step_name AS standard_step_name,
        rso.standard_step_code AS standard_step_code,
        wo.kit_status AS work_order_kit_status,
        wo.order_no AS order_code,
        wo.due_date as work_order_due_date
        from (SELECT * from sds_ord_operation where plan_status = 'UNPLAN') so
        LEFT JOIN mds_rou_routing_step rs on so.routing_step_id = rs.id
        LEFT JOIN mds_rou_standard_step rso ON rso.id = rs.standard_step_id
        LEFT JOIN sds_ord_work_order wo on so.order_id = wo.id
        LEFT JOIN mds_product_stock_point psp on so.product_id = psp.id
        <where>
            rso.standard_step_type != 'NORMEAL_PROCESS'
            <if test="masterPlanReq.killStatus != null and masterPlanReq.killStatus != ''">
                and wo.kit_status = #{masterPlanReq.killStatus,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.orgId != null and masterPlanReq.orgId != ''">
                and psp.organization_id = #{masterPlanReq.orgId,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.planOperation != null and masterPlanReq.planOperation != ''">
                and rso.id = #{masterPlanReq.planOperation,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.planStatus != null and masterPlanReq.planStatus != ''">
                and so.plan_status = #{masterPlanReq.planStatus,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.resourceId != null and masterPlanReq.resourceId != ''">
                and so.planned_resource_id = #{masterPlanReq.resourceId,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.killStatus != null and masterPlanReq.killStatus != ''">
                and wo.kit_status = #{masterPlanReq.killStatus,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.productCode != null and masterPlanReq.productCode != ''">
                and psp.product_code = #{masterPlanReq.productCode,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.productCodes != null and masterPlanReq.productCodes.size() > 0">
                <choose>
                    <when test="masterPlanReq.productCodes.size == 1">
                        and psp.product_code = #{masterPlanReq.productCodes[0],jdbcType=VARCHAR}
                    </when>
                    <otherwise>
                        and psp.product_code in
                        <foreach collection="masterPlanReq.productCodes" item="item" index="index" open="("
                                 separator=","
                                 close=")">
                            #{item,jdbcType=VARCHAR}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>

    <select id="selectByMasterReq" resultMap="VOResultMap">
        SELECT ot.*,
        rso.id AS standard_step_id,
        rso.standard_step_type AS standard_step_type,
        rso.standard_step_name AS standard_step_name,
        rso.standard_step_code AS standard_step_code,
        so.operation_code as operation_code,
        so.routing_step_sequence_no as routing_step_sequence_no,
        psp.product_code AS product_stock_point_code
        from sds_ord_operation_task ot
        LEFT JOIN sds_ord_operation so on ot.operation_id = so.id
        LEFT JOIN mds_rou_routing_step rs on so.routing_step_id = rs.id
        LEFT JOIN mds_rou_standard_step rso ON rso.id = rs.standard_step_id
        LEFT JOIN sds_ord_work_order wo on so.order_id = wo.id
        LEFT JOIN mds_product_stock_point psp on so.product_id = psp.id
        <where>
            <if test="masterPlanReq.killStatus != null and masterPlanReq.killStatus != ''">
                and wo.kit_status = #{masterPlanReq.killStatus,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.resourceId != null and masterPlanReq.resourceId != ''">
                and ot.physical_resource_id = #{masterPlanReq.resourceId,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.orgId != null and masterPlanReq.orgId != ''">
                and psp.organization_id = #{masterPlanReq.orgId,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.historyRetrospectStartTime != null">
                and ot.start_time &gt;= #{masterPlanReq.historyRetrospectStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="masterPlanReq.planStatus != null and masterPlanReq.planStatus != ''">
                and so.plan_status = #{masterPlanReq.planStatus,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.productCode != null and masterPlanReq.productCode != ''">
                and psp.product_code = #{masterPlanReq.productCode,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.productCodes != null and masterPlanReq.productCodes.size() > 0">
                <choose>
                    <when test="masterPlanReq.productCodes.size == 1">
                        and psp.product_code = #{masterPlanReq.productCodes[0],jdbcType=VARCHAR}
                    </when>
                    <otherwise>
                        and psp.product_code in
                        <foreach collection="masterPlanReq.productCodes" item="item" index="index" open="("
                                 separator=","
                                 close=")">
                            #{item,jdbcType=VARCHAR}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="masterPlanReq.planOperation != null and masterPlanReq.planOperation != ''">
                and rso.id = #{masterPlanReq.planOperation,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.physicalResourceIds != null and masterPlanReq.physicalResourceIds.size() > 0">
                and ot.physical_resource_id in
                <foreach collection="masterPlanReq.physicalResourceIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectWorkOrderByMasterReq" resultMap="WorkOrderBaseResultMap">
        SELECT wo.*
        from sds_ord_work_order wo
        LEFT JOIN mds_product_stock_point psp on wo.product_id = psp.id
        <where>
            <if test="masterPlanReq.productCode != null and masterPlanReq.productCode != ''">
                and psp.product_code = #{masterPlanReq.productCode,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.orgId != null and masterPlanReq.orgId != ''">
                and psp.organization_id = #{masterPlanReq.orgId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectSubOperationById" resultMap="OperationVOResultMap">
        SELECT *
        FROM sds_ord_operation t
        WHERE
        t.order_id IN ( SELECT soo.order_id
        FROM sds_ord_operation soo WHERE soo.id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>)
        AND t.parent_id IS NOT NULL
    </select>

    <select id="selectPhysicalResourceVO" resultMap="PhysicalResourceVOResultMap">
        SELECT mrpr.*
        FROM mds_res_physical_resource mrpr
        WHERE mrpr.id IN (SELECT sor.physical_resource_id
                          FROM mds_rou_routing_step_resource sor,
                               mds_rou_routing_step mrs,
                               mds_rou_standard_step mrss
                          WHERE sor.routing_step_id = mrs.id
                            AND mrs.standard_step_id = mrss.id
                            AND mrss.key_step = 'YES'
                          GROUP BY sor.physical_resource_id)
          and mrpr.resource_category = 'MAIN'
          and mrpr.infinite_capacity = 'NO'
        order by mrpr.physical_resource_code
    </select>

    <select id="selectStandardResource" parameterType="java.lang.String" resultMap="StandardResourceVOResultMap">
        SELECT pr.id,
        pr.standard_resource_code,
        pr.standard_resource_name
        FROM mds_res_standard_resource pr
        LEFT JOIN mds_org_production_organization po ON pr.organization_id = po.id
        WHERE pr.resource_category = 'MAIN'
        <if test="organizationCode != null and organizationCode != ''">
            and po.organization_code = #{organizationCode,jdbcType=VARCHAR}
        </if>
        ORDER BY CASE WHEN po.organization_code IS NULL THEN 1 ELSE 0 END,
        CASE WHEN pr.standard_resource_code IS NULL THEN 1 ELSE 0 END
    </select>
    <select id="selectPhysicalResourceByUserId" resultMap="PhysicalResourceVOResultMap">
        SELECT pr.id,
        pr.physical_resource_code,
        pr.physical_resource_name
        FROM mds_res_physical_resource pr
        LEFT JOIN mds_res_standard_resource sr ON pr.standard_resource_id = sr.id
        LEFT JOIN mds_org_production_organization po ON sr.organization_id = po.id
        WHERE pr.resource_category = 'MAIN'
        <if test="userId != null and userId != ''">
            and find_in_set(#{userId,jdbcType=VARCHAR}, pr.production_planner) > 0
        </if>
        ORDER BY CASE WHEN po.organization_code IS NULL THEN 1 ELSE 0 END,
        CASE WHEN pr.sequence_code IS NULL THEN 1 ELSE 0 END,
        CASE WHEN pr.physical_resource_code IS NULL THEN 1 ELSE 0 END
    </select>
    <select id="selectPhysicalResource" parameterType="java.lang.String" resultMap="PhysicalResourceVOResultMap">
        SELECT pr.id,
        pr.physical_resource_code,
        pr.physical_resource_name,
        pr.standard_resource_id,
        pr.production_planner
        FROM mds_res_physical_resource pr
        LEFT JOIN mds_res_standard_resource sr ON pr.standard_resource_id = sr.id
        LEFT JOIN mds_org_production_organization po ON sr.organization_id = po.id
        WHERE pr.resource_category = 'MAIN'
        <if test="organizationCode != null and organizationCode != ''">
            and po.organization_code = #{organizationCode,jdbcType=VARCHAR}
        </if>
        <if test="standardResourceId != null and standardResourceId != ''">
            and sr.id = #{standardResourceId,jdbcType=VARCHAR}
        </if>
        <if test="standardResourceCode != null and standardResourceCode != ''">
            and sr.standard_resource_code = #{standardResourceCode,jdbcType=VARCHAR}
        </if>
        ORDER BY CASE WHEN po.organization_code IS NULL THEN 1 ELSE 0 END,
        CASE WHEN pr.sequence_code IS NULL THEN 1 ELSE 0 END,
        CASE WHEN pr.physical_resource_code IS NULL THEN 1 ELSE 0 END
    </select>
    <select id="selectOperationTaskVOByTime" resultMap="BaseResultMap">
        select *
        from sds_ord_operation_task t
        where #{time,jdbcType=VARCHAR} BETWEEN t.start_time AND t.end_time
          and t.physical_resource_id = #{resourceId}
    </select>
    <select id="selectWorkOrderVOByTime" resultMap="WorkOrderBaseResultMap">
        SELECT swo.*
        FROM sds_ord_work_order swo,
             mds_product_stock_point mpsp
        WHERE swo.product_id = mpsp.id
          AND mpsp.product_code = #{productCode,jdbcType=VARCHAR}
          AND swo.end_time &lt;= #{time,jdbcType=TIMESTAMP}
        order by swo.end_time desc
        limit 1
    </select>

    <select id="selectTimeIntervalOperationTask" resultMap="BaseResultMap">
        SELECT id,
               operation_id,
               standard_resource_id,
               physical_resource_id,
               start_time,
               end_time,
               setup_start_time,
               setup_end_time,
               production_start_time,
               production_end_time,
               cleanup_start_time,
               cleanup_end_time
        FROM sds_ord_operation_task
        WHERE physical_resource_id = #{resourceId,jdbcType=VARCHAR}
          AND (
            (start_time <![CDATA[ >=  ]]> #{startTime}
                AND start_time <![CDATA[ <  ]]> #{endTime})
                OR
            (end_time <![CDATA[ >  ]]> #{startTime}
                AND end_time <![CDATA[ <=  ]]> #{endTime})
            )
        ORDER BY start_time ASC
        limit 1
    </select>


    <select id="queryOperationList" resultType="com.yhl.scp.mps.demand.vo.OperationEarilWarningInfoVO">
        with base as (
        select DISTINCT product_code
        from mds_product_stock_point_base
        where line_group in (select standard_resource_code
        from mds_res_standard_resource
        where find_in_set(#{userId,jdbcType=VARCHAR}, production_planner) > 0)
        )
        SELECT err.id,
        err.parent_id parentId,
        err.product_id productId,
        err.product_code productCode,
        err.product_name productName,
        err.vehicle_model_code vehicleModelCode,
        err.order_id orderId,
        err.due_date dueDate,
        err.order_no orderNo,
        err.planned_resource_id plannedResourceId,
        err.physical_resource_code physicalResourceCode,
        err.physical_resource_name physicalResourceName,
        err.quantity,
        err.start_time startTime,
        err.end_time endTime,
        err.kit_status kitStatus,
        err.plan_status planStatus,
        err.unscheduled_reason unscheduledReason,
        err.routing_step_id routingStepId,
        rso.standard_step_name standardStepName,
        rso.standard_step_code standardStepCode
        FROM (SELECT a.id,
        a.parent_id,
        a.product_id,
        b.product_code,
        b.product_name,
        b.vehicle_model_code,
        a.order_id,
        c.due_date,
        c.order_no,
        a.planned_resource_id,
        d.physical_resource_code,
        d.physical_resource_name,
        a.quantity,
        a.start_time,
        a.end_time,
        c.kit_status,
        a.plan_status,
        a.unscheduled_reason,
        a.routing_step_id
        FROM sds_ord_operation a
        LEFT JOIN mds_product_stock_point b ON a.product_id = b.id
        LEFT JOIN sds_ord_work_order c ON a.order_id = c.id
        LEFT JOIN mds_res_physical_resource d ON a.planned_resource_id = d.id
        inner join base on b.product_code = base.product_code
        WHERE a.parent_id IS NOT NULL
        AND a.plan_status = 'PLANNED'

        UNION ALL

        SELECT a.id,
        a.parent_id,
        a.product_id,
        b.product_code,
        b.product_name,
        b.vehicle_model_code,
        a.order_id,
        c.due_date,
        c.order_no,
        a.planned_resource_id,
        d.physical_resource_code,
        d.physical_resource_name,
        a.quantity,
        a.start_time,
        a.end_time,
        c.kit_status,
        a.plan_status,
        a.unscheduled_reason,
        a.routing_step_id
        FROM sds_ord_operation a
        LEFT JOIN mds_product_stock_point b ON a.product_id = b.id
        LEFT JOIN sds_ord_work_order c ON a.order_id = c.id
        LEFT JOIN mds_res_physical_resource d ON a.planned_resource_id = d.id
        inner join base on b.product_code = base.product_code
        WHERE a.parent_id IS NULL
        AND a.plan_status = 'UNPLAN') err
        LEFT JOIN mds_rou_routing_step rs on err.routing_step_id = rs.id
        LEFT JOIN mds_rou_standard_step rso ON rso.id = rs.standard_step_id
        <where>
            <!--预警只需判断排产的关键工序，S1夹层镀膜和成型，S2钢化-->
            <if test="params.productCode != null and params.productCode != ''">
                and err.product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.resourceId != null and params.resourceId != ''">
                and err.planned_resource_id = #{params.resourceId,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and err.vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.planStatus != null and params.planStatus != ''">
                and err.plan_status = #{params.planStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.dueStartTime != null">
                and err.due_date &gt;= #{params.dueStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.dueEndTime != null">
                and err.due_date &lt;= #{params.dueEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.productStartTime != null">
                and err.start_time &gt;= #{params.productStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.productEndTime != null">
                and err.start_time &lt;= #{params.productEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.standardStepId != null and params.standardStepId != ''">
                and rso.id = #{params.standardStepId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="queryOperationListByOrderIds" resultType="com.yhl.scp.mps.demand.vo.OperationEarilWarningInfoVO">
        SELECT so.id,
        so.order_id orderId,
        so.start_time startTime,
        so.end_time endTime,
        rso.standard_step_name standardStepName,
        rso.standard_step_code standardStepCode,
        psp.product_code productCode,
        psp.stock_point_code stockPointCode
        FROM sds_ord_operation so
        LEFT JOIN mds_rou_routing_step rs ON so.routing_step_id = rs.id
        LEFT JOIN mds_rou_standard_step rso ON rso.id = rs.standard_step_id
        LEFT JOIN mds_product_stock_point psp ON psp.id = so.product_id
        WHERE parent_id IS NULL
        and so.order_id in
        <foreach collection="orderIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectWarehouseReleaseRecord" resultMap="WarehouseReleaseRecordVOMap">
        <!--SELECT t.item_code,
               sum(t.sum_qty) as sum_qty
        FROM fdp_warehouse_release_record t,
             mds_stock_point msp
        WHERE t.plant_code = msp.stock_point_code
          and msp.stock_point_type = 'BC'
          and msp.organize_type = 'SALE_ORGANIZATION'
          and DATE(t.creation_date) = CURDATE()
          and t.item_code in
        <foreach collection="productCodeList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by t.item_code-->
        select tt.item_code,
        sum(tt.sum_qty) as sum_qty
        from (SELECT t.item_code,
        sum(t.sum_qty) as sum_qty
        FROM fdp_warehouse_release_record t,
        mds_stock_point msp
        WHERE t.plant_code = msp.stock_point_code
        and msp.stock_point_type = 'BC'
        and msp.organize_type = 'SALE_ORGANIZATION'
        and t.creation_date <![CDATA[ >= ]]> CURDATE()
        and t.creation_date <![CDATA[ < ]]> DATE(CURDATE() + 1)
        <if test="productCodeList != null and productCodeList.size > 0">
            and t.item_code in
            <foreach collection="productCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by t.item_code
        union all
        SELECT t.item_code,
        sum(t.sum_qty) as sum_qty
        FROM fdp_warehouse_release_to_warehouse t,
        mds_stock_point msp
        WHERE t.plant_code = msp.stock_point_code
        and msp.stock_point_type = 'BC'
        and msp.organize_type = 'SALE_ORGANIZATION'
        and t.creation_date <![CDATA[ >= ]]> CURDATE()
        and t.creation_date <![CDATA[ < ]]> DATE(CURDATE() + 1)
        <if test="productCodeList != null and productCodeList.size > 0">
            and t.item_code in
            <foreach collection="productCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by t.item_code) tt
        group by tt.item_code
    </select>
    <select id="selectPhysicalResourceByPlanner" parameterType="java.lang.String"
            resultType="com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO">
        select id                     as id,
               physical_resource_code as physicalResourceCode,
               physical_resource_name as physicalResourceName
        from mds_res_physical_resource
        where find_in_set(#{productionPlanner,jdbcType=VARCHAR}, production_planner) > 0
    </select>

    <select id="selectPartRiskLevels" resultType="com.yhl.scp.dfp.material.vo.PartRiskLevelVO">
        select product_code as productCode,
        material_risk_level as materialRiskLevel
        from fdp_part_risk_level
        where 1 = 1
        <if test="productCodeList != null and productCodeList.size() > 0">
            and product_code in
            <foreach collection="productCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectBoxPieces" resultType="com.yhl.scp.mds.productBox.vo.ProductBoxRelationVO">
        SELECT product_code as productCode,
        priority as priority,
        standard_load as piecePerBox
        FROM (
        SELECT psp.product_code,
        IFNULL(pbr.priority, 999) AS priority,
        pbr.standard_load,
        ROW_NUMBER() OVER (PARTITION BY psp.product_code ORDER BY IFNULL(pbr.priority, 999)) AS rn
        FROM mds_product_box_relation pbr
        LEFT JOIN (
        SELECT DISTINCT mps.inventory_item_id AS inventory_item_id,
        mps.product_code AS product_code
        FROM mds_stock_point msp
        LEFT JOIN mds_product_stock_point mps
        ON msp.stock_point_code = mps.stock_point_code
        WHERE mps.inventory_item_id IS NOT NULL
        AND msp.organize_type = 'SALE_ORGANIZATION'
        <if test="productCodes != null and productCodes.size > 0">
            and mps.product_code in
            <foreach collection="productCodes" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        ) psp ON pbr.product_stock_point_id = psp.inventory_item_id
        WHERE psp.product_code IS NOT NULL
        AND pbr.standard_load IS NOT NULL
        ) t
        WHERE rn = 1
    </select>

    <select id="selectProductionOrganizationWithPermission" parameterType="java.lang.String"
            resultType="com.yhl.scp.mds.extension.organization.vo.ProductionOrganizationVO">
        SELECT DISTINCT po.id as id,
        po.organization_code as organizationCode,
        po.organization_name as organizationName
        FROM mds_org_production_organization po
        LEFT JOIN mds_res_standard_resource sr ON po.id = sr.organization_id
        <if test="userId != null and userId != ''">
            WHERE find_in_set(#{userId,jdbcType=VARCHAR}, sr.production_planner) > 0
        </if>
    </select>

    <select id="selectBCStockPoint" parameterType="java.lang.String"
            resultType="com.yhl.scp.mds.stock.vo.NewStockPointVO">
        SELECT t.id               as id,
               t.stock_point_code as stockPointCode,
               t.stock_point_name as stockPointName
        FROM mds_stock_point t
        WHERE t.stock_point_type = 'BC'
          AND t.organize_type = 'PRODUCT_ORGANIZATION'

    </select>

    <select id="selectPhysicalResourceByOperationTask" resultType="String">
        SELECT DISTINCT physical_resource_id
        FROM sds_ord_operation_task task
        UNION
        SELECT DISTINCT re.physical_resource_id
        FROM mds_rou_routing_step_resource re
        UNION
        SELECT id
        FROM mds_res_physical_resource pr
        where pr.remark = 'YZT'
    </select>

    <select id="selectPhysicalResourceByOperationTaskByRoutingStepId" resultType="String" parameterType="List">
        SELECT DISTINCT physical_resource_id
        FROM mds_rou_routing_step_resource sr
        where sr.routing_step_id in
        <foreach collection="routingStepIds" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        UNION
        SELECT id
        FROM mds_res_physical_resource pr
        where pr.remark = 'YZT'
        <if test="resourceCodeList != null and resourceCodeList.size > 0">
            or pr.physical_resource_code in
            <foreach collection="resourceCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectOperationDemandByOperationIds" parameterType="java.util.List"
            resultType="com.yhl.scp.mps.plan.vo.OperationDemandVO">
        select o1.id as operationId,
        o2.id as parentOperationId,
        d.id as demandId,
        oi.input_quantity as quantity,
        rsi.yield as yield,
        rsi.input_factor as inputFactor
        from sds_ord_operation o1
        left join sds_ord_operation o2 on o1.parent_id = o2.id
        left join sds_ord_operation_input oi on o2.id = oi.operation_id
        left join sds_peg_demand d on oi.id = d.operation_input_id
        left join mds_rou_routing_step_input rsi on oi.product_id = rsi.input_product_id
        and oi.routing_step_id = rsi.routing_step_id
        where o1.id in
        <foreach collection="operationIds" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and rsi.enabled = 'YES'
        and d.id is not null
    </select>

    <select id="selectByMasterReq2" resultMap="VOResultMap">
        SELECT ot.*,
        rso.id AS standard_step_id,
        rso.standard_step_type AS standard_step_type,
        rso.standard_step_name AS standard_step_name,
        rso.standard_step_code AS standard_step_code,
        so.operation_code as operation_code,
        so.routing_step_sequence_no as routing_step_sequence_no,
        psp.product_code AS product_stock_point_code,
        pr.box_type AS box_type
        from sds_ord_operation_task_published ot
        LEFT JOIN sds_ord_operation_published so on ot.operation_id = so.id
        LEFT JOIN mds_rou_routing_step rs on so.routing_step_id = rs.id
        LEFT JOIN mds_rou_standard_step rso ON rso.id = rs.standard_step_id
        LEFT JOIN sds_ord_work_order_published wo on so.order_id = wo.id
        LEFT JOIN mds_product_stock_point psp on so.product_id = psp.id
        LEFT JOIN mds_product_box_relation pr on psp.id = pr.product_stock_point_id
        <where>
            <if test="masterPlanReq.lastPublishedLogId != null and masterPlanReq.lastPublishedLogId != ''">
                and ot.published_log_id = #{masterPlanReq.lastPublishedLogId,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.killStatus != null and masterPlanReq.killStatus != ''">
                and wo.kit_status = #{masterPlanReq.killStatus,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.resourceId != null and masterPlanReq.resourceId != ''">
                and ot.physical_resource_id = #{masterPlanReq.resourceId,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.productCode != null and masterPlanReq.productCode != ''">
                and psp.product_code = #{masterPlanReq.productCode,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.orgId != null and masterPlanReq.orgId != ''">
                and psp.organization_id = #{masterPlanReq.orgId,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.historyRetrospectStartTime != null">
                and ot.start_time &gt;= #{masterPlanReq.historyRetrospectStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="masterPlanReq.planStatus != null and masterPlanReq.planStatus != ''">
                and so.plan_status = #{masterPlanReq.planStatus,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.productCodes != null and masterPlanReq.productCodes.size() > 0">
                and psp.product_code in
                <foreach collection="masterPlanReq.productCodes" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="masterPlanReq.planOperation != null and masterPlanReq.planOperation != ''">
                and rso.id = #{masterPlanReq.planOperation,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.physicalResourceIds != null and masterPlanReq.physicalResourceIds.size() > 0">
                and ot.physical_resource_id in
                <foreach collection="masterPlanReq.physicalResourceIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectUnPlanOperation2" resultMap="VOResultMap">
        SELECT so.id as id,
        so.order_id as order_id,
        so.routing_step_id as routing_step_id,
        so.quantity as quantity,
        so.product_id as product_id,
        so.standard_step_id as standard_step_id,
        so.plan_status as plan_status,
        so.routing_step_sequence_no as routing_step_sequence_no,
        rso.standard_step_type AS standard_step_type,
        rso.standard_step_name AS standard_step_name,
        rso.standard_step_code AS standard_step_code,
        wo.kit_status AS work_order_kit_status,
        wo.order_no AS order_code,
        wo.due_date as work_order_due_date,
        pr.box_type AS box_type
        from (SELECT * from sds_ord_operation_published) so
        LEFT JOIN mds_rou_routing_step rs on so.routing_step_id = rs.id
        LEFT JOIN mds_rou_standard_step rso ON rso.id = rs.standard_step_id
        LEFT JOIN sds_ord_work_order_published wo on so.order_id = wo.id
        LEFT JOIN mds_product_stock_point psp on so.product_id = psp.id
        LEFT JOIN mds_product_box_relation pr on psp.id = pr.product_stock_point_id
        <where>
            rso.standard_step_type != 'NORMEAL_PROCESS'
            <if test="masterPlanReq.killStatus != null and masterPlanReq.killStatus != ''">
                and wo.kit_status = #{masterPlanReq.killStatus,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.productCode != null and masterPlanReq.productCode != ''">
                and psp.product_code = #{masterPlanReq.productCode,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.orgId != null and masterPlanReq.orgId != ''">
                and psp.organization_id = #{masterPlanReq.orgId,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.planOperation != null and masterPlanReq.planOperation != ''">
                and rso.id = #{masterPlanReq.planOperation,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.planStatus != null and masterPlanReq.planStatus != ''">
                and so.plan_status = #{masterPlanReq.planStatus,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.resourceId != null and masterPlanReq.resourceId != ''">
                and so.planned_resource_id = #{masterPlanReq.resourceId,jdbcType=VARCHAR}
            </if>
            <if test="masterPlanReq.killStatus != null and masterPlanReq.killStatus != ''">
                and wo.kit_status = #{masterPlanReq.killStatus,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectToolOperationTasks" resultMap="VOResultMap">
        SELECT ot.id, ot.operation_id, ot.physical_resource_id, ot.cleanup_start_time
        from sds_ord_operation_task ot
        left join mds_res_physical_resource pr on ot.physical_resource_id=pr.id
        where pr.resource_category='TOOL'
        and ot.cleanup_start_time is not null
        and ot.operation_id in
        <foreach collection="operationIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectPhysicalOperation2" resultMap="VOResultMap">
        SELECT id, operation_id, physical_resource_id, cleanup_start_time
        from sds_ord_operation_task_published
        where cleanup_start_time is not null
        and operation_id in
        <foreach collection="operationIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectLastOperationTaskBetweenDates" resultType="com.yhl.scp.sds.extension.order.vo.OperationTaskVO">
        select 'false'      as QUERYID,
               id,
               operation_id as operationId
        from sds_ord_operation_task
        where 1 = 1
          and production_start_time >= #{startDate}
          and production_start_time <![CDATA[ <  ]]> #{endDate}
          and physical_resource_id = #{physicalResourceId,jdbcType=VARCHAR}
        order by production_start_time desc
        limit 1
    </select>

    <select id="getPlanWorkOrderIds" resultType="java.lang.String">
        SELECT so.order_id
        from sds_ord_operation_task st
        LEFT JOIN sds_ord_operation so on st.operation_id = so.id
        where st.physical_resource_id in
        <foreach collection="physicalResourceIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectSaProductCodes" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT distinct psp1.product_code
        FROM sds_ord_work_order so
                 INNER JOIN (SELECT wo.id
                             FROM sds_ord_work_order wo
                                      LEFT JOIN mds_product_stock_point psp ON wo.product_id = psp.id
                             WHERE psp.product_code = #{productCode,jdbcType=VARCHAR}) so1 ON so.top_order_id = so1.id
                 LEFT JOIN mds_product_stock_point psp1 ON so.product_id = psp1.id
    </select>

    <select id="selectFgProductCodes" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT distinct psp1.product_code
        FROM sds_ord_work_order so
                 INNER JOIN (SELECT wo.top_order_id
                             FROM sds_ord_work_order wo
                                      LEFT JOIN mds_product_stock_point psp ON wo.product_id = psp.id
                             WHERE psp.product_code = #{productCode,jdbcType=VARCHAR}) so1 ON so.id = so1.top_order_id
                 LEFT JOIN mds_product_stock_point psp1 ON so.product_id = psp1.id
    </select>

    <select id="selectRunResources" resultType="java.lang.String">
        select physical_resource_code
        from mds_res_physical_resource where id in
        <foreach collection="resourceIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectProductCascadeByIds" resultType="com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO">
        select
        id as id,
        product_code as productCode,
        product_name as productName,
        stock_point_code as stockPointCode,
        vehicle_model_code as vehicleModelCode
        from mds_product_stock_point
        where product_code in (
        select distinct product_code
        from mds_product_stock_point
        where id in
        <foreach collection="productIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
    </select>

    <select id="selectResourceCalendarsByParams" resultMap="ResourceCalendarMap">
        select
        <include refid="ResourceCalendar_Column_List"/>
        from mds_cal_resource_calendar
        <where>
            <include refid="ResourceCalendar_Where_Condition"/>
        </where>
    </select>
    <select id="selectCollectGroupByLoadingPosit" resultMap="InventoryBatchDetailVOResultMap">
        SELECT a.stock_point_code,
               a.operation_code,
               b.loading_position,
               sum(a.current_quantity) current_quantity
        FROM (SELECT `fibd`.`stock_point_code`                                           AS `stock_point_code`,
                     substr(`fibd`.`product_code`, 1, length(`fibd`.`product_code`) - 3) AS `product_code`,
                     substr(`fibd`.`product_code`, -2)                                   AS `operation_code`,
                     sum(`fibd`.`current_quantity`)                                      AS `current_quantity`
              FROM `fdp_inventory_batch_detail` `fibd`
                       JOIN (select distinct stock_point_code
                             from `mds_stock_point`
                             WHERE `mds_stock_point`.`stock_point_type` = 'BC') `msp` ON
                  `fibd`.`stock_point_code` = `msp`.`stock_point_code`
              WHERE substr(reverse(`fibd`.`product_code`), 3, 1) = '-'
              GROUP BY `fibd`.`stock_point_code`,
                       substr(`fibd`.`product_code`, 1, length(`fibd`.`product_code`) - 3),
                       substr(`fibd`.`product_code`, -2)
              UNION
              SELECT `fibd`.`stock_point_code`      AS `stock_point_code`,
                     `fibd`.`product_code`          AS `product_code`,
                     ''                             AS `operation_code`,
                     sum(`fibd`.`current_quantity`) AS `current_quantity`
              FROM `fdp_inventory_batch_detail` `fibd`
                       JOIN (select distinct stock_point_code
                             from `mds_stock_point`
                             WHERE `mds_stock_point`.`stock_point_type` = 'BC') `msp` ON
                  `fibd`.`stock_point_code` = `msp`.`stock_point_code`
              WHERE (substr(reverse(`fibd`.`product_code`), 3, 1) <![CDATA[ <> ]]> '-')
              GROUP BY `fibd`.`stock_point_code`,
                       `fibd`.`product_code`) a
                 LEFT JOIN mds_product_stock_point b ON a.product_code = b.product_code
            AND a.stock_point_code = b.stock_point_code
        WHERE b.loading_position IS NOT NULL
        GROUP BY a.stock_point_code,
                 a.operation_code,
                 b.loading_position
    </select>
    <select id="selectCollectGroupByNoLoadingPosit" resultMap="InventoryBatchDetailVOResultMap">
        SELECT a.stock_point_code,
               a.operation_code,
               sum(a.current_quantity) current_quantity
        FROM (SELECT `fibd`.`stock_point_code`                                           AS `stock_point_code`,
                     substr(`fibd`.`product_code`, 1, length(`fibd`.`product_code`) - 3) AS `product_code`,
                     substr(`fibd`.`product_code`, -2)                                   AS `operation_code`,
                     sum(`fibd`.`current_quantity`)                                      AS `current_quantity`
              FROM `fdp_inventory_batch_detail` `fibd`
                       JOIN (select distinct stock_point_code
                             from `mds_stock_point`
                             WHERE `mds_stock_point`.`stock_point_type` = 'BC') `msp` ON
                  `fibd`.`stock_point_code` = `msp`.`stock_point_code`
              WHERE substr(reverse(`fibd`.`product_code`), 3, 1) = '-'
              GROUP BY `fibd`.`stock_point_code`,
                       substr(`fibd`.`product_code`, 1, length(`fibd`.`product_code`) - 3),
                       substr(`fibd`.`product_code`, -2)
              UNION
              SELECT `fibd`.`stock_point_code`      AS `stock_point_code`,
                     `fibd`.`product_code`          AS `product_code`,
                     ''                             AS `operation_code`,
                     sum(`fibd`.`current_quantity`) AS `current_quantity`
              FROM `fdp_inventory_batch_detail` `fibd`
                       JOIN (select distinct stock_point_code
                             from `mds_stock_point`
                             WHERE `mds_stock_point`.`stock_point_type` = 'BC') `msp` ON
                  `fibd`.`stock_point_code` = `msp`.`stock_point_code`
              WHERE (substr(reverse(`fibd`.`product_code`), 3, 1) <![CDATA[ <> ]]> '-')
              GROUP BY `fibd`.`stock_point_code`,
                       `fibd`.`product_code`) a
                 LEFT JOIN mds_product_stock_point b ON a.product_code = b.product_code
            AND a.stock_point_code = b.stock_point_code
        GROUP BY a.stock_point_code,
                 a.operation_code
    </select>
    <select id="selectOperationInputsByParams"
            resultMap="com.yhl.scp.sds.order.infrastructure.dao.OperationInputBasicDao.VOResultMap">
        select id, operation_id
        from sds_ord_operation_input
        <where>
            <include refid="com.yhl.scp.sds.order.infrastructure.dao.OperationInputBasicDao.Base_Where_Condition"/>
        </where>
    </select>
    <select id="selectDemandVosByParams" resultMap="DemandVOResultMap">
        select
        `d`.`id` AS `id`,
        `d`.`demand_code` AS `demand_code`,
        `d`.`demand_time` AS `demand_time`,
        `d`.`product_stock_point_id` AS `product_stock_point_id`,
        `d`.`product_id` AS `product_id`,
        `d`.`stock_point_id` AS `stock_point_id`,
        `d`.`quantity` AS `quantity`,
        `d`.`unfulfilled_quantity` AS `unfulfilled_quantity`,
        `d`.`customer_id` AS `customer_id`,
        `d`.`demand_order_id` AS `demand_order_id`,
        `d`.`operation_input_id` AS `operation_input_id`,
        `d`.`demand_type` AS `demand_type`,
        `d`.`fulfillment_status` AS `fulfillment_status`,
        `d`.`counting_unit_id` AS `counting_unit_id`,
        `d`.`remark` AS `remark`,
        `d`.`enabled` AS `enabled`,
        `d`.`creator` AS `creator`,
        `d`.`create_time` AS `create_time`,
        `d`.`modifier` AS `modifier`,
        `d`.`modify_time` AS `modify_time`,
        `d`.`finished_product_code` AS `finished_product_code`,
        `p`.`product_code` AS `product_code`,
        `p`.`product_name` AS `product_name`,
        `sp`.`stock_point_code` AS `stock_point_code`,
        `sp`.`stock_point_name` AS `stock_point_name`,
        `wo`.`order_no` AS `order_no`,
        `o`.`operation_code` AS `operation_code`,
        `o`.`id` as `operation_id`,
        `pu`.`plan_unit_no` AS `plan_unit_no`,
        `rs`.`sequence_no` AS `sequence_number`,
        `oi`.`routing_step_id` AS `routing_step_id`
        from
        `sds_peg_demand` `d`
        left join `mds_product_stock_point` `p` on `p`.`id` = `d`.`product_id`
        left join `mds_stock_point` `sp` on `sp`.`stock_point_code` = `p`.`stock_point_code`
        left join `sds_ord_work_order` `wo` on `wo`.`id` = `d`.`demand_order_id`
        left join `sds_ord_operation_input` `oi` on `oi`.`id` = `d`.`operation_input_id`
        left join `sds_ord_operation` `o` on `o`.`id` = `oi`.`operation_id`
        left join `sds_ord_plan_unit` `pu` on `pu`.`id` = `oi`.`plan_unit_id`
        left join `mds_rou_routing_step` `rs` on `rs`.`id` = `oi`.`routing_step_id`
        where `o`.`id` in
        <foreach collection="params.operationIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectSupplyVosByParams" resultMap="SupplyVOResultMap">
        SELECT
        s.id,
        s.supply_code,
        s.product_stock_point_id,
        s.product_id,
        s.stock_point_id,
        s.supply_time,
        s.quantity,
        s.unfulfilled_quantity,
        s.supply_order_id,
        s.operation_output_id,
        s.supply_type,
        s.fulfillment_status,
        s.appoint_customer_id,
        s.appoint_demand_type,
        s.appoint_demand_order_id,
        s.counting_unit_id,
        s.remark,
        s.fixed,
        s.enabled,
        s.creator,
        s.create_time,
        s.modifier,
        s.modify_time,
        out.operation_id
        FROM
        `sds_peg_supply` `s`
        LEFT JOIN `sds_ord_operation_output` `out` ON `out`.`id` = `s`.`operation_output_id`
        where `out`.`operation_id` in
        <foreach collection="params.operationIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectMainYztResource" resultType="java.lang.String">
        select id
        from mds_res_physical_resource
        where standard_resource_id in
              (select id from mds_res_standard_resource where standard_resource_code = #{code,jdbcType=VARCHAR})
    </select>
    <select id="selectOperationInfoByWorkOrderIds" resultMap="AdjustOperationVOMap">
        select id, order_id, plan_status, start_time, end_time, earliest_start_time,
        latest_end_time, calc_earliest_start_time, calc_latest_end_time
        from sds_ord_operation
        where order_id in
        <foreach collection="params.workOrderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectOperationByResourceIds" resultMap="OperationVOResultMap">
        with a as (
        select *
        from v_sds_ord_operation t
        where t.planned_resource_id in
        <foreach collection="resourceIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ),
        b as (
        select t.*
        from v_sds_ord_operation t ,
        a
        where t.id = a.parent_id
        union
        select *
        from a)
        SELECT
        b.*,
        mrss.standard_step_code AS routing_step_code,
        mrss.standard_step_name AS routing_step_name,
        mrss.standard_step_type AS standard_step_type
        FROM
        b,
        mds_rou_standard_step mrss
        WHERE
        b.standard_step_id = mrss.id
    </select>

    <select id="selectResourcePermissions" resultMap="StandardResourceVOResultMap">
        select id, standard_resource_code
        from mds_res_standard_resource
        where find_in_set(#{userId,jdbcType=VARCHAR}, production_planner) > 0
          and infinite_capacity = 'NO'
    </select>

    <select id="selectPhysicalResourcePermissions" resultMap="PhysicalResourceVOResultMap">
        select id,standard_resource_id, physical_resource_code,physical_resource_name
        from mds_res_physical_resource
        where find_in_set(#{userId,jdbcType=VARCHAR}, production_planner) > 0
        <if test="standardResourceIds != null and standardResourceIds.size() > 0">
            and standard_resource_id in
            <foreach collection="standardResourceIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and infinite_capacity = 'NO'
    </select>

    <select id="selectFeedBackQuantityByOperationIds" resultMap="FeedBackResultMap">
        select operation_id, sum(reporting_quantity) as reporting_quantity
        from sds_fee_feedback_production
        where operation_id in
        <foreach collection="params.operationIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by operation_id
    </select>

    <select id="selectStandardStep" resultType="com.yhl.scp.mds.extension.routing.vo.StandardStepVO">
        select id                 as id,
               standard_step_name as standardStepName,
               standard_step_type as standardStepType
        from mds_rou_standard_step
    </select>

    <select id="selectInfiniteCapacityResourceIds" resultType="java.lang.String">
        select id
        from mds_res_physical_resource
        where infinite_capacity = 'YES'
          and enabled = 'YES'
    </select>

    <select id="selectPlannedOrderResource" resultType="java.lang.String">
        SELECT distinct so.order_id
        from sds_ord_operation_task st
        LEFT JOIN mds_res_physical_resource rs
        on rs.id = st.physical_resource_id
        LEFT JOIN sds_ord_operation so
        on st.operation_id = so.id
        where rs.physical_resource_code in
        <foreach collection="productLineList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and so.order_id in
        <foreach collection="plannedWorkOrderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectKeyStepUnPlanOrderIds" resultType="java.lang.String">
        select so.order_id
        from sds_ord_operation so
        LEFT JOIN mds_rou_standard_step ms
        ON so.standard_step_id = ms.id
        where so.parent_id is null
        and so.plan_status = 'UNPLAN'
        AND ms.standard_step_type = 'FORMING_PROCESS'
        and so.order_id in
        <foreach collection="allOrderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectResourceTask" resultMap="VOResultMap">
        select t.*
        from sds_ord_operation_task t
                 left join sds_ord_operation o
                           on t.operation_id = o.id
        where o.plan_status != 'FINISHED'
          AND t.physical_resource_id = #{resourceId,jdbcType=VARCHAR}
          and t.end_time &lt;= #{reportingStart,jdbcType=TIMESTAMP}
    </select>

    <select id="selectBeforeCuringTime" resultMap="CuringTimeMap">
        select stock_point_code, product_code, sum(gh_time) as curing_time from mds_curing_time
        where 1 = 1
        <if test="stockPointCodes != null and stockPointCodes.size() > 0">
            and stock_point_code in
            <foreach collection="stockPointCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and enabled = 'YES'
        <if test="productCodes != null and productCodes.size() > 0">
            and product_code in
            <foreach collection="productCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by stock_point_code, product_code
    </select>

    <select id="selectWorkOrderWithParentProductId" resultMap="MasterPlanWorkOrderVOResultMap">
        select
        a.id, a.product_id, psp.product_code, psp.stock_point_code
        from (
        select wo.id, case when pwo.product_id is null then wo.product_id else pwo.product_id end as product_id
        from sds_ord_work_order wo
        left join sds_ord_work_order pwo on pwo.id = wo.parent_id
        where 1 = 1
        <if test="workOrderIds != null and workOrderIds.size() > 0">
            and wo.id in
            <foreach collection="workOrderIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) a left join mds_product_stock_point psp on psp.id = a.product_id
    </select>

    <select id="selectAfterCuringTime" resultMap="CuringTimeMap">
        select stock_point_code, product_code, sum(storage_time) as curing_time
        from mds_finished_product_delivery
        where 1 = 1
        <if test="stockPointCode != null and stockPointCode != ''">
            and stock_point_code = #{stockPointCode}
        </if>
        and enabled = 'YES'
        <if test="productCodes != null and productCodes.size() > 0">
            and product_code in
            <foreach collection="productCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by stock_point_code, product_code
    </select>
    <select id="selectBcSaleStockPoint"
            resultType="com.yhl.scp.mds.stock.vo.NewStockPointVO">
        SELECT t.id               as id,
               t.stock_point_code as stockPointCode,
               t.stock_point_name as stockPointName
        FROM mds_stock_point t
        WHERE t.stock_point_type = 'BC'
          AND t.organize_type = 'SALE_ORGANIZATION'
        limit 1
    </select>
    <select id="selectMaxVersionId" parameterType="java.lang.String" resultType="java.lang.String">
        select id
        from fdp_consistence_demand_forecast_version
        where plan_period = #{planPeriod}
          and version_status = 'PUBLISHED'
        order by version_code desc
        limit 1
    </select>
    <select id="selectMasterPlanConsistenceDemandForecastDataDetails"
            resultType="com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO">
        select distinct cdfd.product_code as productCode, cdfdt.forecast_quantity as forecastQuantity
        from fdp_consistence_demand_forecast_data_detail cdfdt
        left join fdp_consistence_demand_forecast_data cdfd
        on cdfd.id = cdfdt.consistence_demand_forecast_data_id
        where cdfd.version_id = #{versionId}
        and cdfdt.forecast_time = #{forecastTime}
        and cdfd.product_code in
        <foreach collection="productCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectWarehouseReleaseRecordByTime" resultMap="WarehouseReleaseRecordVOMap">
        select tt.item_code,
        sum(tt.sum_qty) as sum_qty
        from (SELECT t.item_code,
        sum(t.sum_qty) as sum_qty
        FROM fdp_warehouse_release_record t,
        mds_stock_point msp
        WHERE t.plant_code = msp.stock_point_code
        and msp.stock_point_type = 'BC'
        and msp.organize_type = 'SALE_ORGANIZATION'
        and t.creation_date like concat(#{deliveryTime},'%')
        <if test="productCodeList != null and productCodeList.size > 0">
            and t.item_code in
            <foreach collection="productCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by t.item_code
        union all
        SELECT t.item_code,
        sum(t.sum_qty) as sum_qty
        FROM fdp_warehouse_release_to_warehouse t,
        mds_stock_point msp
        WHERE t.plant_code = msp.stock_point_code
        and msp.stock_point_type = 'BC'
        and msp.organize_type = 'SALE_ORGANIZATION'
        and t.creation_date like concat(#{deliveryTime},'%')
        <if test="productCodeList != null and productCodeList.size > 0">
            and t.item_code in
            <foreach collection="productCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by t.item_code) tt
        group by tt.item_code
    </select>

    <select id="selectResourceCalendars" resultMap="ResourceCalendarMap">
        select
        <include refid="ResourceCalendar_Column_List"/>
        from mds_cal_resource_calendar
        where physical_resource_id in
    </select>


    <update id="updateWorkOrderStatus">
        UPDATE sds_ord_work_order b
        JOIN sds_ord_operation a ON b.id = a.order_id
        SET b.plan_status = a.plan_status,b.fixed = null
        WHERE a.id IN
        <foreach collection="operationIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and b.plan_status = 'ISSUED'
    </update>

    <select id="selectByItemCodeAndPlantCode" resultMap="MasterPlanWorkOrderVOResultMap">
        select w.*,mp.product_code as product_code,mp.stock_point_code as stock_point_code from sds_ord_work_order w
        left join
        mds_product_stock_point mp on w.product_id = mp.id
        where mp.product_code in
        <foreach collection="productCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectPhysicalResourceByStockCodeList" resultMap="PhysicalResourceVOResultMap">
        select re.id                     as id,
               re.physical_resource_code as physical_resource_code,
               re.physical_resource_name as physical_resource_name,
               re.sequence_code          as sequence_code,
               mp.stock_point_code       as standard_resource_code
        from mds_res_physical_resource re
                 LEFT JOIN mds_res_standard_resource mr ON
            re.standard_resource_id = mr.id
                 LEFT JOIN mds_stock_point mp
                           on mr.organization_id = mp.id
        where mr.organization_id is not null
    </select>

    <select id="selectPhysicalResourceBySequenceList" resultMap="PhysicalResourceVOResultMap">
        select * from mds_res_physical_resource where sequence_code in
        <foreach collection="valueMeaningList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectAllIdMappingWorkOrders" resultType="java.lang.String">
        with a as (
        SELECT
        id,
        parent_id
        FROM
        sds_ord_work_order
        WHERE
        id IN
        <foreach collection="orderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
        -- 根据子订单关联父订单
        select wo.id
        from sds_ord_work_order wo,a where wo.id = a.parent_id
        union
        -- 根据子订单关联其余子订单
        select wo.id
        from sds_ord_work_order wo,a where wo.parent_id = a.parent_id
        union
        -- 获取当前订单
        select wo.id
        from sds_ord_work_order wo,a where wo.id = a.id
        union
        -- 根据父订单关联其下子订单
        select wo.id
        from sds_ord_work_order wo,a where wo.parent_id = a.id
    </select>

    <select id="selectVehicleModelCode" resultType="java.lang.String">
        select vehicle_model_code
        from mds_product_stock_point
        where id = (SELECT product_id
                    from mds_rou_routing
                    where product_code = #{productCode}
                      and enabled = 'YES')
          and enabled = 'YES'
    </select>

    <select id="selectProductFixtureRelationVO"
            resultType="com.yhl.scp.mps.fixtureRelation.vo.ProductFixtureRelationVO">
        SELECT
        mpf.product_code AS productCode,
        mmt.physical_resource_name AS toolStandardResource
        FROM
        mps_product_fixture_relation mpf
        LEFT JOIN mds_mold_tooling mmt ON mpf.physical_resource_id = mmt.id
        WHERE
        mpf.enabled = 'YES'
        AND mmt.enabled = 'YES'
        AND mpf.tooling_type = 'WIND_FENCE'
        AND mpf.product_code IN
        <foreach collection="productCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <resultMap id="nodeVOResultMap" extends="com.yhl.scp.sds.order.infrastructure.dao.OperationBasicDao.VOResultMap"
               type="com.yhl.scp.mps.plan.vo.PlanNodeTrackingVO">
        <result column="standard_step_id" jdbcType="VARCHAR" property="standardStepId"/>
        <result column="standard_step_type" jdbcType="VARCHAR" property="standardStepType"/>
        <result column="standard_step_name" jdbcType="VARCHAR" property="standardStepName"/>
        <result column="standard_step_code" jdbcType="VARCHAR" property="standardStepCode"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oem"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="order_kit_status" jdbcType="VARCHAR" property="orderKitStatus"/>
        <result column="reporting_quantity" jdbcType="DECIMAL" property="finishedQty"/>
        <result column="bom_type" jdbcType="VARCHAR" property="bomType"/>
        <result column="order_parent_id" jdbcType="VARCHAR" property="orderParentId"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
    </resultMap>

    <select id="selectPlanNodeTrackingList" resultMap="nodeVOResultMap">
        SELECT so.id as id,
        so.order_id as order_id,
        so.start_time as start_time,
        so.end_time as end_time,
        so.quantity as quantity,
        wo.kit_status as order_kit_status,
        wo.parent_id as order_parent_id,
        rso.standard_step_type AS standard_step_type,
        rso.standard_step_name AS standard_step_name,
        rso.standard_step_code AS standard_step_code,
        psp.product_code AS product_code,
        psp.stock_point_code AS stock_point_code,
        psp.vehicle_model_code as vehicle_model_code,
        mop.oem_code as oem_code,
        wo.bom_type as bom_type,
        IF(sff.reporting_status != 'FINISHED', 0, sff.reporting_quantity) as reporting_quantity
        from sds_ord_operation so
        LEFT JOIN mds_rou_routing_step rs on so.routing_step_id = rs.id
        LEFT JOIN mds_rou_standard_step rso ON rso.id = rs.standard_step_id
        LEFT JOIN sds_ord_work_order wo on so.order_id = wo.id
        LEFT JOIN mds_product_stock_point psp on so.product_id = psp.id
        LEFT JOIN mds_oem_product_line_map mop on psp.vehicle_model_code = mop.vehicle_model_code
        left join sds_fee_feedback_production sff on so.id = sff.operation_id
        where so.start_time >= #{planStartTime,jdbcType=TIMESTAMP}
        and so.product_id IN
        <foreach collection="planNodeTrackingReq.productIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="selectWareHousePlan" resultType="com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO">
        with temp as(
        SELECT
        item_code,
        sum_qty
        FROM fdp_warehouse_release_record
        WHERE item_code IN
        <foreach collection="productCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND creation_date >= #{currentDate,jdbcType=TIMESTAMP}
        AND creation_date <![CDATA[ <= ]]> #{dayLastTime,jdbcType=TIMESTAMP}
        AND plant_code = #{salesOrganization,jdbcType=VARCHAR}
        AND attribute1 = #{subInventory,jdbcType=VARCHAR}
        AND enabled = 'YES'
        UNION ALL
        SELECT
        item_code,
        sum_qty
        FROM fdp_warehouse_release_to_warehouse
        WHERE item_code IN
        <foreach collection="productCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND creation_date >= #{currentDate,jdbcType=TIMESTAMP}
        AND creation_date <![CDATA[ <= ]]> #{dayLastTime,jdbcType=TIMESTAMP}
        AND plant_code = #{salesOrganization,jdbcType=VARCHAR}
        AND attribute1 = #{subInventory,jdbcType=VARCHAR}
        AND enabled = 'YES'
        )
        SELECT item_code as itemCode,
        SUM(sum_qty) as sumQty
        from temp GROUP BY item_code
    </select>

    <select id="fulfillDetails" resultType="com.yhl.scp.mps.plan.vo.FulfillDetailVO">
        SELECT mmd.id                                              as demandId,
               mmd.material_product_id                             AS productId,
               mmd.material_product_code                           as productCode,
               mmd.product_code                                    as finishedProductCode,
               mmd.demand_time                                     as demandTime,
               mmd.material_type                                   as productType,
               mmd.quantity                                        as demandQuantity,
               mmd.unfulfilled_quantity                            as unFulfillQuantity,
               mmf.id                                              as fulfillmentId,
               mmf.fulfillment_quantity                            as fulfillmentQuantity,
               mmf.alt_material_used                               as altMaterialUsed,
               mmf.supply_type                                     as supplyType,
               mms.product_code                                    as supplyProductCode,
               IFNULL(mmf.unit_conversion_ratio, mmd.input_factor) AS unitConversionRatio,
               mmd.net_quantity                                    as netDemandQuantity,
               mmd.demand_time                                     as demandTime,
               mms.supply_time                                     as supplyTime,
               psp.product_name                                    as productName,
               mms.id                                              as supplyId,
               wo.order_no                                         as orderNo
        FROM mrp_material_demand mmd
                 LEFT JOIN sds_ord_work_order wo on mmd.demand_order_id = wo.id
                 left join mds_product_stock_point psp on mmd.product_id = psp.id
                 LEFT JOIN mrp_material_fulfillment mmf ON mmd.id = mmf.demand_id
                 LEFT JOIN mrp_material_supply mms on mmf.supply_id = mms.id
        WHERE mmd.demand_order_id = #{orderId}
    </select>

    <select id="supplyDetails" resultType="com.yhl.scp.mps.plan.vo.FulfillDetailVO">
        SELECT mmd.id as demandId,
        mmd.material_product_id AS productId,
        mmd.material_product_code as productCode,
        mmd.product_code as finishedProductCode,
        mmd.demand_time as demandTime,
        mmd.material_type as productType,
        mmd.quantity as demandQuantity,
        mmd.unfulfilled_quantity as unFulfillQuantity,
        mmf.id as fulfillmentId,
        mmf.fulfillment_quantity as fulfillmentQuantity,
        mmf.alt_material_used as altMaterialUsed,
        mmf.supply_type as supplyType,
        mms.product_code as supplyProductCode,
        IFNULL(mmf.unit_conversion_ratio, mmd.input_factor) AS unitConversionRatio,
        mmd.net_quantity as netDemandQuantity,
        mmd.demand_time as demandTime,
        mms.supply_time as supplyTime,
        psp.product_name as productName,
        mms.id as supplyId,
        wo.order_no as orderNo
        FROM mrp_material_supply mms
        LEFT JOIN mrp_material_fulfillment mmf ON mms.id = mmf.supply_id
        LEFT JOIN mrp_material_demand mmd on mmf.demand_id = mmd.id
        LEFT JOIN sds_ord_work_order wo on mmd.demand_order_id = wo.id
        left join mds_product_stock_point psp on mmd.product_id = psp.id
        WHERE 1 = 1
        <if test="supplyId != null and supplyId != ''">
            and mms.id = #{supplyId}
        </if>
        <if test="materialProductCode != null and materialProductCode != ''">
            and mms.product_code = #{materialProductCode}
        </if>
    </select>

    <select id="selectBomProduct" resultType="java.lang.String">
        SELECT DISTINCT merged_field
        FROM (SELECT mrr.product_id AS merged_field
        FROM mds_rou_routing mrr
        WHERE mrr.product_code in
        <foreach collection="productCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND mrr.enabled = 'YES'

        UNION ALL

        SELECT mmp.id AS merged_field
        FROM mds_rou_routing mrr
        LEFT JOIN mds_rou_routing_step_input mrrs ON mrr.id = mrrs.routing_id
        LEFT JOIN mds_product_stock_point mmp ON mrrs.input_product_id = mmp.id
        WHERE mrr.product_code in
        <foreach collection="productCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND mrr.enabled = 'YES'
        AND mrrs.enabled = 'YES'
        AND mmp.enabled = 'YES'
        AND mmp.product_type = 'SA') AS all_values
    </select>

    <select id="selectResourceStep" resultType="com.yhl.scp.mds.extension.routing.vo.RoutingStepVO">
        SELECT
        routing_id as routingId,
        routing_step_id as routingStepId,
        standard_resource_code as connectionTask,
        standard_step_type as standardStepCode,
        sequence_no as sequenceNo,
        unit_production_time as yield
        FROM
        (
        SELECT
        t.routing_id,
        t.routing_step_id,
        mrr.standard_resource_code,
        mr.standard_step_type,
        rrs.sequence_no,
        t.unit_production_time,
        ROW_NUMBER() over ( PARTITION BY routing_step_id ORDER BY priority ASC ) AS row_num
        FROM
        mds_rou_routing_step_resource t
        LEFT JOIN mds_rou_routing_step rrs on t.routing_step_id = rrs.id
        LEFT JOIN mds_rou_standard_step mr on rrs.standard_step_id = mr.id
        LEFT JOIN mds_res_physical_resource p ON t.physical_resource_id = p.id
        LEFT JOIN mds_res_standard_resource mrr on p.standard_resource_id = mrr.id
        WHERE
        t.routing_id in
        <foreach collection="routingIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and t.enabled = 'YES'
        and rrs.enabled = 'YES'
        and p.enabled = 'YES'
        ) ranked
        WHERE
        row_num = 1
    </select>

    <select id="selectPlanNodeDeliveryProduct" resultType="java.lang.String">
        SELECT DISTINCT psp.product_code
        from mds_product_stock_point psp
        LEFT JOIN mds_oem_product_line_map mop on psp.vehicle_model_code = mop.vehicle_model_code
        where psp.product_code in(
        select distinct product_code from fdp_delivery_plan_published
        <where>
            <if test="productCode != null and productCode != ''">
                and product_code = #{productCode,jdbcType=VARCHAR}
            </if>
            <if test="start != null">
                and demand_time >= #{start,jdbcType=TIMESTAMP}
            </if>
        </where>
        )
        <if test="planNodeTrackingReq.vehicleModelCode != null and planNodeTrackingReq.vehicleModelCode != ''">
            and psp.vehicle_model_code = #{planNodeTrackingReq.vehicleModelCode,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectBomProductList" resultType="com.yhl.scp.mps.plan.vo.BomProductVO">
        WITH finished_summary AS (
        SELECT
        mrr.id AS fid,
        mrr.product_code AS finishedProductCode,
        mrr.product_id AS finishedProductId,
        psp.vehicle_model_code AS vehicleModelCode,
        psp.stock_point_code AS finishedProductStockPointCode,
        mop.oem_code AS oemCode
        FROM
        mds_rou_routing mrr
        LEFT JOIN mds_product_stock_point psp on mrr.product_id = psp.id
        LEFT JOIN mds_oem_product_line_map mop on psp.vehicle_model_code = mop.vehicle_model_code
        WHERE
        mrr.product_code IN
        <foreach collection="productCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="oemCode != null and oemCode != ''">
            and mop.oem_code = #{oemCode,jdbcType=VARCHAR}
        </if>
        AND mrr.enabled = 'YES'
        ),
        semi_summary AS (
        SELECT
        mrr.id AS sid,
        mmp.product_code as semiProductCode,
        mmp.id AS semiProductId
        FROM
        mds_rou_routing mrr
        LEFT JOIN mds_rou_routing_step_input mrrs ON mrr.id = mrrs.routing_id
        LEFT JOIN mds_product_stock_point mmp ON mrrs.input_product_id = mmp.id
        WHERE
        mrr.product_code IN
        <foreach collection="productCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND mrr.enabled = 'YES'
        AND mrrs.enabled = 'YES'
        AND mmp.enabled = 'YES'
        AND mmp.product_type = 'SA'
        )
        select
            f.finishedProductStockPointCode,
            f.finishedProductCode,
            f.finishedProductId,
            f.vehicleModelCode,
            f.oemCode,
            s.semiProductCode,
            s.semiProductId
        from finished_summary f
        LEFT JOIN semi_summary s
        on f.fid = s.sid
    </select>
    <select id="selectInventoryBatchDetail"
            resultMap="InventoryBatchDetailResultMap">
        select
        id,
        stock_point_code,
        product_code,
        original_product_code,
        operation_code,
        subinventory,
        freight_space,
        current_quantity
        from v_fdp_realtime_inventory_batch_detail
        where stock_point_type = #{bc}
        and organize_type in
        <foreach collection="organizeTypes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectRealtimeWarehouseReleaseRecord" resultType="com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO">
        select
        id,acreage,acreage_sum,actual_arrive_port_time,actual_completion_time,attribute1,attribute2,bar_num,
        bill_of_lading_num,box_num,car_num,consigner,container_num,create_time,creation_date,creator,cust_part,
        cust_po,customer_number,descriptions,ebs_site_id,enabled,estimated_arrive_port_time,estimated_completion_time,
        in_warehouse_time,instock_source,is_receive,item_code,kid,line_num,list_num,lot_number,modifier,modify_time,
        plant_code,remark,req,req_num,ship_company,shipment_locator_code,shipment_warehouse_code,source_type,sum_qty,
        type_coode,version_value
        from fdp_translation_warehouse_release_record
    </select>
    <select id="selectRealtimeRelease2WarehouseRecord"
            resultType="com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseVO">
        select
        id,acreage,acreage_sum,actual_arrive_port_time,actual_completion_time,attribute1,attribute2,bar_num,
        bill_of_lading_num,box_num,car_num,consigner,container_num,create_time,creation_date,creator,cust_part,
        cust_po,customer_number,descriptions,ebs_site_id,enabled,estimated_arrive_port_time,estimated_completion_time,
        in_warehouse_time,instock_source,is_receive,item_code,kid,line_num,list_num,lot_number,modifier,modify_time,
        plant_code,remark,req,req_num,ship_company,shipment_locator_code,shipment_warehouse_code,source_type,sum_qty,
        type_coode,version_value
        from fdp_translation_warehouse_release_to_warehouse
    </select>
</mapper>
