package com.yhl.scp.mps.domain.workbench.model.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <code>WorkBenchUserInfo</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-09-24 10:54:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WorkBenchUserInfo implements Serializable {


    private static final long serialVersionUID = 6257384877175490617L;

    /**
     * 多个角色时拼接在一起
     */
    @ApiModelProperty(value = "角色名称")
    private String roleName;

    /**
     * 有权限的零件数量
     */
    @ApiModelProperty(value = "有权限的零件数量")
    private Integer productCount;

    /**
     * 有权限的主机厂数量
     */
    @ApiModelProperty(value = "有权限的主机厂数量")
    private Integer oemCount;
}
