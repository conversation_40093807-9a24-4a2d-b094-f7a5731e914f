package com.yhl.scp.mps.domain.algorithm.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.BasePO;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.common.utils.BulkOperationUtils;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mps.algorithm.schedule.output.RzzCalculateTimeParamDTO;
import com.yhl.scp.mps.algorithm.schedule.output.RzzOperationDTO;
import com.yhl.scp.mps.domain.algorithm.RzzAMSSupportService;
import com.yhl.scp.mps.domain.algorithm.RzzAlgorithmOutputService;
import com.yhl.scp.mps.enums.ReportingStatusEnum;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanExtDao;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.plan.infrastructure.po.SdsOrdOperationHistoryLogPO;
import com.yhl.scp.sds.basic.enums.ReportingTypeEnum;
import com.yhl.scp.sds.basic.feedback.dto.FeedbackProductionBasicDTO;
import com.yhl.scp.sds.basic.feedback.infrastructure.po.FeedbackProductionBasicPO;
import com.yhl.scp.sds.basic.order.enums.PlannedStatusEnum;
import com.yhl.scp.sds.basic.order.infrastructure.po.OperationBasicPO;
import com.yhl.scp.sds.basic.pegging.enums.DemandTypeEnum;
import com.yhl.scp.sds.basic.pegging.enums.SupplyTypeEnum;
import com.yhl.scp.sds.extension.feedback.dto.FeedbackProductionDTO;
import com.yhl.scp.sds.extension.feedback.infrastructure.po.FeedbackProductionPO;
import com.yhl.scp.sds.extension.feedback.vo.FeedbackProductionVO;
import com.yhl.scp.sds.extension.order.dto.OperationDTO;
import com.yhl.scp.sds.extension.order.dto.OperationExtendDTO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationSubTaskPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationTaskPO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.extension.pegging.infrastructure.po.DemandPO;
import com.yhl.scp.sds.extension.pegging.infrastructure.po.SupplyPO;
import com.yhl.scp.sds.extension.pegging.vo.DemandVO;
import com.yhl.scp.sds.extension.pegging.vo.SupplyVO;
import com.yhl.scp.sds.feedback.convertor.FeedbackProductionConvertor;
import com.yhl.scp.sds.feedback.service.FeedbackProductionService;
import com.yhl.scp.sds.order.convertor.OperationConvertor;
import com.yhl.scp.sds.order.convertor.WorkOrderConvertor;
import com.yhl.scp.sds.order.infrastructure.dao.OperationDao;
import com.yhl.scp.sds.order.infrastructure.dao.OperationSubTaskDao;
import com.yhl.scp.sds.order.infrastructure.dao.OperationTaskDao;
import com.yhl.scp.sds.order.service.OperationExtendService;
import com.yhl.scp.sds.order.service.WorkOrderService;
import com.yhl.scp.sds.pegging.convertor.DemandConvertor;
import com.yhl.scp.sds.pegging.convertor.SupplyConvertor;
import com.yhl.scp.sds.pegging.infrastructure.dao.DemandDao;
import com.yhl.scp.sds.pegging.infrastructure.dao.SupplyDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>CalculateDemandOrSupplyTimeServiceImpl</code>
 * <p>
 * 抽取了涉及AMS算法相关的公用逻辑
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-17 11:32:26
 */
@Slf4j
@Service
public class RzzAMSSupportServiceImpl implements RzzAMSSupportService {

    @Resource
    private OperationExtendService operationExtendService;
    @Resource
    private OperationDao operationDao;
    @Resource
    private SupplyDao supplyDao;
    @Resource
    private DemandDao demandDao;
    @Resource
    private FeedbackProductionService feedbackProductionService;
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private OperationTaskExtDao operationTaskExtDao;
    @Resource
    private MasterPlanExtDao masterPlanExtDao;
    @Resource
    private OperationTaskDao operationTaskDao;
    @Resource
    private OperationSubTaskDao operationSubTaskDao;
    @Resource
    private RzzAlgorithmOutputService rzzAlgorithmOutputService;

    private static final List<String> feedbackOperationStatusList = Arrays.asList(PlannedStatusEnum.STARTED.getCode(),
            PlannedStatusEnum.FINISHED.getCode());

    public List<DemandVO> calculateDemandTime(RzzCalculateTimeParamDTO calculateTimeParamDTO) {
        List<DemandVO> demandVOList = calculateTimeParamDTO.getDemandVOList();
        Map<String, WorkOrderVO> workOrderIdMaps = calculateTimeParamDTO.getWorkOrderIdMaps();
        Map<String, RzzOperationDTO> operationIdMaps = calculateTimeParamDTO.getOperationIdMaps();
        PlanningHorizonVO planningHorizon = calculateTimeParamDTO.getPlanningHorizon();
        List<DemandVO> updateList = new ArrayList<>();
        if (CollectionUtils.isEmpty(demandVOList)) {
            return updateList;
        }
        for (DemandVO demandVO : demandVOList) {
            //log.info("计算需求时间:{}",demandDTO.getId());
            if (!StringUtils.equals(DemandTypeEnum.WORK_ORDER_DEMAND.getCode(), demandVO.getDemandType())) {
                log.info("非制造订单需求");
                continue;
            }
            //获取产生supply的工序
            RzzOperationDTO operationDTO = operationIdMaps.get(demandVO.getOperationId());
            if (null == operationDTO) {
                log.info("工序为空:{}", demandVO.getOperationId());
                continue;
            }
            WorkOrderVO workOrderVO = workOrderIdMaps.get(operationDTO.getOrderId());
            if (null == workOrderVO) {
                log.info("制造订单为空:{}", operationDTO.getOrderId());
                continue;
            }
            Date sourceDemandTime = demandVO.getDemandTime();
            //工序状态未计划且制造订单交期不为空 demandTime = 制造订单交期
            if (PlannedStatusEnum.UNPLAN.getCode().equals(operationDTO.getPlanStatus()) && ObjUtil.isNotNull(workOrderVO.getDueDate())) {
                demandVO.setDemandTime(workOrderVO.getDueDate());
            }
            //工序状态未计划且制造订单交期为空 demandTime = 计划期间结束时间
            if (PlannedStatusEnum.UNPLAN.getCode().equals(operationDTO.getPlanStatus()) && !ObjUtil.isNotNull(workOrderVO.getDueDate())) {
                demandVO.setDemandTime(planningHorizon.getPlanEndTime());
            }

            //工序状态已计划 demandTime = 工序制造开始时间
            if (PlannedStatusEnum.PLANNED.getCode().equals(operationDTO.getPlanStatus())) {
                demandVO.setDemandTime(operationDTO.getProductionStartTime());
            }
            if (null == sourceDemandTime || null == demandVO.getDemandTime()) {
                log.error("需求时间为空demandId：{}，sourceDemand：{}，targetDemand：{}", demandVO.getId(), sourceDemandTime, demandVO.getDemandTime());
                continue;
            }
            if (!sourceDemandTime.equals(demandVO.getDemandTime())) {
                updateList.add(demandVO);
            }
        }
        return updateList;
    }

    public List<SupplyVO> calculateSupplyTime(RzzCalculateTimeParamDTO calculateTimeParamDTO) {
        List<SupplyVO> supplyVOList = calculateTimeParamDTO.getSupplyVOList();
        Map<String, WorkOrderVO> workOrderIdMaps = calculateTimeParamDTO.getWorkOrderIdMaps();
        Map<String, RzzOperationDTO> operationIdMaps = calculateTimeParamDTO.getOperationIdMaps();
        PlanningHorizonVO planningHorizon = calculateTimeParamDTO.getPlanningHorizon();
        List<SupplyVO> updateList = new ArrayList<>();
        if (CollectionUtils.isEmpty(supplyVOList)) {
            return updateList;
        }
        for (SupplyVO supplyVO : supplyVOList) {
            if (!StringUtils.equals(SupplyTypeEnum.WORK_ORDER_SUPPLY.getCode(), supplyVO.getSupplyType())) {
                continue;
            }
            //获取工序
            RzzOperationDTO operationDTO = operationIdMaps.get(supplyVO.getOperationId());
            if (null == operationDTO) {
                continue;
            }
            //获取制造订单
            WorkOrderVO workOrderVO = workOrderIdMaps.get(operationDTO.getOrderId());
            if (null == workOrderVO) {
                continue;
            }
            Date sourceSupplyTime = supplyVO.getSupplyTime();
            //工序状态未计划且制造订单交期不为空 supplyTime = 制造订单交期
            if (PlannedStatusEnum.UNPLAN.getCode().equals(operationDTO.getPlanStatus()) && ObjUtil.isNotNull(workOrderVO.getDueDate())) {
                supplyVO.setSupplyTime(workOrderVO.getDueDate());
            }
            //工序状态未计划且制造订单交期不为空 supplyTime = 计划期间计划开始时间 - 累计制造提前期
            if (PlannedStatusEnum.UNPLAN.getCode().equals(operationDTO.getPlanStatus()) && ObjUtil.isNull(workOrderVO.getDueDate())) {
                supplyVO.setSupplyTime(planningHorizon.getPlanStartTime());
            }
            //工序状态为已计划 supplyTime = 工序制造结束时间
            if (PlannedStatusEnum.PLANNED.getCode().equals(operationDTO.getPlanStatus())) {
                supplyVO.setSupplyTime(operationDTO.getProductionEndTime());
            }
            if (!sourceSupplyTime.equals(supplyVO.getSupplyTime())) {
                updateList.add(supplyVO);
            }
        }

        return updateList;
    }

    @Override
    public void doOperationUpdateBatch(List<OperationDTO> operList) {
        if (CollectionUtils.isEmpty(operList)) {
            return;
        }
        List<OperationExtendDTO> operationExtendDTOList = new ArrayList<>();
        // 修改扩展信息
        for (OperationDTO operationDTO : operList) {
            OperationExtendDTO operationExtendDTO = new OperationExtendDTO();
            operationDtoSplit(operationDTO, operationExtendDTO);
            operationExtendDTOList.add(operationExtendDTO);
        }
        List<OperationPO> newList = BeanUtil.copyToList(operList, OperationPO.class);
        BasePOUtils.updateBatchFiller(newList);
        List<List<OperationPO>> lists = com.yhl.platform.common.utils.CollectionUtils.splitList(newList, 2000);
        for (List<OperationPO> operationPOS : lists) {
            operationDao.updateBatch(operationPOS);
        }
        //修改扩展表信息
        //修改扩展表信息
        List<List<OperationExtendDTO>> operationExtendList = com.yhl.platform.common.utils.CollectionUtils.splitList(operationExtendDTOList, 2000);
        for (List<OperationExtendDTO> operationExtendDTOS : operationExtendList) {
            operationExtendService.doUpdateBatch(operationExtendDTOS);
        }
    }

    /**
     * 更新demand时间
     *
     * @param list
     */
    public void doDemandUpdateBatchSelective(List<DemandVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        log.info("doDemandUpdateBatchSelective update demand size{}", list.size());
        List<DemandPO> newList = DemandConvertor.INSTANCE.vo2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, demandDao::updateBatch, 2000);
    }


    public void doSupplyUpdateBatchSelective(List<SupplyVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        log.info("doSupplyUpdateBatchSelective update supply size{}", list.size());
        List<SupplyPO> newList = SupplyConvertor.INSTANCE.vo2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, supplyDao::updateBatch, null);
    }

    @Override
    public void calculateCommon(Map<String, WorkOrderVO> workOrderIdMaps, Map<String, RzzOperationDTO> operationDTOMapOfId,
                                List<String> parentOperationId, PlanningHorizonVO planningHorizon) {
        Map<String, Object> params = new HashMap<>();
        params.put("operationIds", parentOperationId);
        List<DemandVO> demandVOS = Lists.newArrayList();
        List<SupplyVO> supplyVOS = Lists.newArrayList();
        String scenario = SystemHolder.getScenario();
        CompletableFuture<Void> demandFuture = CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            //查询需求
            List<DemandVO> demands = operationTaskExtDao.selectDemandVosByParams(params);
            if (CollectionUtils.isNotEmpty(demands)) {
                demandVOS.addAll(demands);
            }
            DynamicDataSourceContextHolder.clearDataSource();
        });
        CompletableFuture<Void> supplyFuture = CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            //查询供应
            List<SupplyVO> supplies = operationTaskExtDao.selectSupplyVosByParams(params);
            if (CollectionUtils.isNotEmpty(supplies)) {
                supplyVOS.addAll(supplies);
            }
            DynamicDataSourceContextHolder.clearDataSource();
        });
        CompletableFuture.allOf(demandFuture, supplyFuture).join();

        long s8 = System.currentTimeMillis();
        //更新demand时间
        RzzCalculateTimeParamDTO calculateTimeParamDTO = new RzzCalculateTimeParamDTO();
        calculateTimeParamDTO.setDemandVOList(demandVOS);
        calculateTimeParamDTO.setWorkOrderIdMaps(workOrderIdMaps);
        calculateTimeParamDTO.setOperationIdMaps(operationDTOMapOfId);
        calculateTimeParamDTO.setPlanningHorizon(planningHorizon);
        List<DemandVO> updateDemandVOList = this.calculateDemandTime(calculateTimeParamDTO);
        log.info("算法结果解析 doUpdateDemandTime -重新计算状态数据耗时5：" + (System.currentTimeMillis() - s8));
        long s9 = System.currentTimeMillis();
        this.doDemandUpdateBatchSelective(Lists.newArrayList(updateDemandVOList));

        log.info("算法结果解析 doUpdateDemandTime -更新数据耗时6：" + (System.currentTimeMillis() - s9));

        long updateSupplyTime = System.currentTimeMillis();
        calculateTimeParamDTO.setSupplyVOList(supplyVOS);
        List<SupplyVO> updateSupplyVOList = this.calculateSupplyTime(calculateTimeParamDTO);
        this.doSupplyUpdateBatchSelective(updateSupplyVOList);
        log.info("算法结果解析 doUpdateSupplyTime-更新数据耗时：" + (System.currentTimeMillis() - updateSupplyTime));
    }

    public Map<String, RzzOperationDTO> operationVOToRzzDto(List<OperationVO> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return new HashMap<>();
        }
        Map<String, RzzOperationDTO> operationDTOMapOfId = voList.stream().collect(Collectors.toMap(OperationVO::getId, item -> {
            RzzOperationDTO dto = new RzzOperationDTO();
            BeanUtils.copyProperties(item, dto);
            return dto;
        }));
        return operationDTOMapOfId;
    }

    public void operationDtoSplit(OperationDTO operationDTO, OperationExtendDTO operationExtendDTO) {
        operationExtendDTO.setOperationId(operationDTO.getId());
        operationExtendDTO.setPreOperationIds(operationDTO.getPreOperationIds());
        operationExtendDTO.setResourceIds(operationDTO.getResourceIds());
        operationExtendDTO.setNextOperationIds(operationDTO.getNextOperationIds());
//        operationExtendDTO.setAppointProductionTime(operationDTO.getAppointProductionTime());
        operationExtendDTO.setAppointQuantity(operationDTO.getAppointQuantity());
        operationExtendDTO.setAppointMainResourceId(operationDTO.getAppointMainResourceId());
        operationExtendDTO.setAppointStartTime(operationDTO.getAppointStartTime());
        operationExtendDTO.setAppointEndTime(operationDTO.getAppointEndTime());
        operationExtendDTO.setPartitionRate(operationDTO.getPartitionRate());
        operationExtendDTO.setPartitionNum(operationDTO.getPartitionNum());
        operationExtendDTO.setPartitionBatch(operationDTO.getPartitionBatch());
        operationExtendDTO.setMaxPartitionBatch(operationDTO.getMaxPartitionBatch());
        operationExtendDTO.setMinPartitionBatch(operationDTO.getMinPartitionBatch());
        operationExtendDTO.setPartitionBatchUnit(operationDTO.getPartitionBatchUnit());
        operationExtendDTO.setPartitionMantissaDeal(operationDTO.getPartitionMantissaDeal());
        operationExtendDTO.setPlannedMainResourceId(operationDTO.getPlannedMainResourceId());
        operationExtendDTO.setLastMainResourceId(operationDTO.getLastMainResourceId());
        operationExtendDTO.setPlannedToolResourceId(operationDTO.getPlannedToolResourceId());
        operationExtendDTO.setLastToolResourceId(operationDTO.getLastToolResourceId());
        operationExtendDTO.setPlannedSkillId(operationDTO.getPlannedSkillId());
        operationExtendDTO.setLastSkillId(operationDTO.getLastSkillId());
        operationExtendDTO.setSetupStartTime(operationDTO.getSetupStartTime());
        operationExtendDTO.setLastSetupStartTime(operationDTO.getLastSetupStartTime());
        operationExtendDTO.setSetupEndTime(operationDTO.getSetupEndTime());
        operationExtendDTO.setLastSetupEndTime(operationDTO.getLastSetupEndTime() == null ? null : operationDTO.getLastSetupEndTime());
        operationExtendDTO.setSetupDuration(operationDTO.getSetupDuration());
        operationExtendDTO.setProductionStartTime(operationDTO.getProductionStartTime());
        operationExtendDTO.setLastProductionStartTime(operationDTO.getLastProductionStartTime());
        operationExtendDTO.setProductionEndTime(operationDTO.getProductionEndTime());
        operationExtendDTO.setLastProductionEndTime(operationDTO.getLastProductionEndTime());
//        operationExtendDTO.setProductionDuration(operationDTO.getProductionDuration());
        operationExtendDTO.setLockStartTime(operationDTO.getLockStartTime());
        operationExtendDTO.setLastLockStartTime(operationDTO.getLastLockStartTime());
        operationExtendDTO.setLockEndTime(operationDTO.getLockEndTime());
        operationExtendDTO.setLastLockEndTime(operationDTO.getLastLockEndTime());
        operationExtendDTO.setLockDuration(operationDTO.getLockDuration());
        operationExtendDTO.setCleanupStartTime(operationDTO.getCleanupStartTime());
        operationExtendDTO.setLastCleanupStartTime(operationDTO.getLastCleanupStartTime());
        operationExtendDTO.setCleanupEndTime(operationDTO.getCleanupEndTime());
        operationExtendDTO.setLastCleanupEndTime(operationDTO.getLastCleanupEndTime());
        operationExtendDTO.setCleanupDuration(operationDTO.getCleanupDuration());
        operationExtendDTO.setFeedFinishTime(operationDTO.getFeedFinishTime());
        operationExtendDTO.setUnscheduledReason(operationDTO.getUnscheduledReason());
        operationExtendDTO.setDelayReason(operationDTO.getDelayReason());
    }


    @Override
    public void doBatchPlanFeedBackProductionProcess(List<FeedbackProductionDTO> list) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        //获取子工序的生产反馈数据
        List<FeedbackProductionDTO> childOperationFeedBackList = list.stream().filter(item -> StringUtils.equals(ReportingTypeEnum.SON.getCode(), item.getReportingType())).collect(Collectors.toList());
        //获取父工序生产反馈数据
        List<FeedbackProductionDTO> parentOperationFeedBackList = list.stream().filter(item -> StringUtils.equals(ReportingTypeEnum.PARENT.getCode(), item.getReportingType())).collect(Collectors.toList());

        //获取父工序ID
        List<String> parentOperationIds = new ArrayList<>();
        Map<String, List<FeedbackProductionVO>> parentFeedbackProductionOfOperationId = new HashMap<>();
        if (CollectionUtils.isNotEmpty(parentOperationFeedBackList)) {
            parentOperationIds = parentOperationFeedBackList.stream().map(FeedbackProductionDTO::getOperationId).distinct().collect(Collectors.toList());
            //查询父工序的所有报工数据
            List<FeedbackProductionVO> feedbackProductionVOS = feedbackProductionService.selectByOperationIds(parentOperationIds);
            parentFeedbackProductionOfOperationId = feedbackProductionVOS.stream().collect(Collectors.groupingBy(FeedbackProductionVO::getOperationId));
        }


        //组装子工序Map
        Map<String, OperationVO> subOperationIdMaps = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(childOperationFeedBackList)) {
            List<String> childOperationIds = childOperationFeedBackList.stream().map(FeedbackProductionDTO::getOperationId).collect(Collectors.toList());
            List<OperationVO> subOperations = operationDao.selectVOByPrimaryKeys(childOperationIds);
            subOperationIdMaps = subOperations.stream().collect(Collectors.toMap(OperationVO::getId, Function.identity()));
            parentOperationIds.addAll(subOperations.stream().map(OperationVO::getParentId).distinct().collect(Collectors.toList()));
        }

        List<OperationPO> parentOperationPOList = new ArrayList<>();
        List<OperationPO> allSubOperations = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(parentOperationIds)) {
            //查询父工序
            parentOperationPOList = operationDao.selectByPrimaryKeys(parentOperationIds);
            //查询父工序关联的子工序
            allSubOperations = operationDao.selectByParentIds(parentOperationIds);
        }


        Map<String, List<OperationPO>> parentIdToSubOperationMaps = allSubOperations.stream().collect(Collectors.groupingBy(OperationPO::getParentId));
        Map<String, OperationPO> parentOperationIdMaps = parentOperationPOList.stream().collect(Collectors.toMap(OperationPO::getId, Function.identity()));

        List<String> processedParentOperationIdList = new ArrayList<>();
        List<OperationPO> updateOperationPOList = new ArrayList<>();
        //更新父工序的状态
        List<OperationPO> updateParentOperationPOList = new ArrayList<>();
        List<String> updateParentOperationPOIdList = new ArrayList<>();
        List<String> filterFeedbackIds = new ArrayList<>();
        for (FeedbackProductionDTO feedbackProductionDTO : list) {
            if (StringUtils.equals(ReportingTypeEnum.PARENT.getCode(), feedbackProductionDTO.getReportingType())) {
                if (processedParentOperationIdList.contains(feedbackProductionDTO.getOperationId())) {
                    continue;
                }
                processedParentOperationIdList.add(feedbackProductionDTO.getOperationId());
                //父工序生产反馈
                List<FeedbackProductionVO> parentFeedbackProductionVOList = parentFeedbackProductionOfOperationId.get(feedbackProductionDTO.getOperationId());
                //计算父工序的状态
                String planStatus = computeParentOperationStatus(FeedbackProductionConvertor.INSTANCE.vo2Pos(parentFeedbackProductionVOList));
                //修改父工序的状态
                OperationPO operationPO = parentOperationIdMaps.get(feedbackProductionDTO.getOperationId());
                if (null == operationPO) {
                    continue;
                }
                operationPO.setPlanStatus(planStatus);
                updateOperationPOList.add(operationPO);
                //同步修改子工序的状态
                List<OperationPO> childOperationPOList = parentIdToSubOperationMaps.get(feedbackProductionDTO.getOperationId());
                if (CollectionUtils.isEmpty(childOperationPOList)) {
                    continue;
                }
                childOperationPOList.forEach(item -> item.setPlanStatus(operationPO.getPlanStatus()));
                updateOperationPOList.addAll(childOperationPOList);
            } else {
                //子工序生产反馈
                //修改子工序的状态
                OperationVO operationVO = subOperationIdMaps.get(feedbackProductionDTO.getOperationId());
                int i = operationVO.getQuantity().compareTo(feedbackProductionDTO.getReportingQuantity());
                if (i <= 0) {
                    // 报工数量大于等于计划数量，则更新状态为已完工
                    operationVO.setPlanStatus(PlannedStatusEnum.FINISHED.getCode());
                    filterFeedbackIds.add(feedbackProductionDTO.getOperationId());
                } else {
                    // 报工数量小于计划数量，则更新状态为已开始
                    operationVO.setPlanStatus(PlannedStatusEnum.STARTED.getCode());
                }

                updateOperationPOList.add(OperationConvertor.INSTANCE.vo2Po(operationVO));
                if (!updateParentOperationPOIdList.contains(operationVO.getParentId())) {
                    if (parentOperationIdMaps.get(operationVO.getParentId()) == null) {
                        log.info("子工序{}对应的父工序不存在,父工序id{}", operationVO.getId(), operationVO.getParentId());
                        continue;
                    }
                    updateParentOperationPOList.add(parentOperationIdMaps.get(operationVO.getParentId()));
                }
                updateParentOperationPOIdList.add(operationVO.getParentId());
            }
        }

        //更新工序的状态
        if (CollectionUtils.isNotEmpty(updateOperationPOList)) {
            updateOperationPOList.forEach(t->t.setRemark("根据报工状态更新子工序状态"));
            BasePOUtils.updateBatchFiller(updateOperationPOList);
            operationDao.updateBatch(updateOperationPOList);
            List<String> updateOperationIds = updateOperationPOList.stream().map(BasePO::getId).collect(Collectors.toList());
            List<FeedbackProductionDTO> needUpdateList = list.stream()
                    .filter(t -> updateOperationIds.contains(t.getOperationId())).collect(Collectors.toList());
            needUpdateList.forEach(t -> t.setWhetherFeedBack(YesOrNoEnum.YES.getCode()));
            if (CollectionUtils.isNotEmpty(needUpdateList)) {
                feedbackProductionService.doUpdateBatch(needUpdateList);
            }
        }

        //根据工序查询制造订单
        List<String> workOrderIdList = updateOperationPOList.stream().map(OperationPO::getOrderId).distinct().collect(Collectors.toList());
        List<WorkOrderVO> workOrderVOList = workOrderService.selectByParams(ImmutableMap.of("ids",workOrderIdList));

        //根据制造订单查询最新工序信息
        List<OperationPO> operationPoList = operationDao.selectByOrderIds(workOrderIdList);
        Map<String, List<OperationPO>> childOperationMapOfParentId = operationPoList.stream().filter(item ->
                StringUtils.isNotBlank(item.getParentId())).collect(Collectors.groupingBy(OperationPO::getParentId));
        //更新父工序状态
        if (CollectionUtils.isNotEmpty(updateParentOperationPOList)) {
            for (OperationPO operationPO : updateParentOperationPOList) {
                if(null == operationPO){
                    log.error("operationPO为空{}", JSON.toJSONString(updateParentOperationPOList));
                    continue;
                }
                if(null == childOperationMapOfParentId){
                    log.error("childOperationMapOfParentId为空");
                    continue;
                }
                if(!childOperationMapOfParentId.containsKey(operationPO.getId())){
                    continue;
                }
                List<OperationPO> childOperationPOS = childOperationMapOfParentId.get(operationPO.getId());
                //获取父更新状态
                operationPO.setPlanStatus(getParentOperationStatus(operationPO.getQuantity(), OperationConvertor.INSTANCE.po2Dtos(childOperationPOS)));
            }
            BasePOUtils.updateBatchFiller(updateOperationPOList);
            operationDao.updateBatch(updateParentOperationPOList);
        }

        doUpdateWorkOrderPlanStatus(operationPoList, workOrderVOList);

        if (CollectionUtils.isNotEmpty(filterFeedbackIds)) {
            list.removeIf(t -> filterFeedbackIds.contains(t.getOperationId()));
        }
    }

    /**
     * 更新制造订单状态
     * @param operationPoList
     * @param workOrderVOList
     */
    public void doUpdateWorkOrderPlanStatus(List<OperationPO> operationPoList, List<WorkOrderVO> workOrderVOList) {
        //更新制造订单状态
        Map<String, List<OperationPO>> operationMapOfOrderId = operationPoList.stream().collect(Collectors.groupingBy(OperationPO::getOrderId));
        for (WorkOrderVO workOrderVO : workOrderVOList) {
            List<OperationPO> operationsOfWorkOrder = operationMapOfOrderId.get(workOrderVO.getId());
            String workOrderStatus = workOrderService.getWorkOrderStatus(operationsOfWorkOrder);
            workOrderVO.setPlanStatus(workOrderStatus);
        }
        //更新制造订单
        workOrderService.doUpdateBatch(WorkOrderConvertor.INSTANCE.vo2Dtos(workOrderVOList));

        //更新父制造订单状态
        //获取parentId
        List<String> parentWorkOrderIdList = workOrderVOList.stream().map(WorkOrderVO::getParentId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(parentWorkOrderIdList)) {
            //查询父制造订单数据
            List<WorkOrderVO> parentWorkOrderList = workOrderService.selectByPrimaryKeys(parentWorkOrderIdList);
            //查询父订单的子制造订单
            List<WorkOrderVO> childWorkOrderList = workOrderService.selectByParentIds(parentWorkOrderIdList);
            Map<String, List<WorkOrderVO>> workOrderMapOfParentId = childWorkOrderList.stream().collect(Collectors.groupingBy(WorkOrderVO::getParentId));
            for (WorkOrderVO workOrderVO : parentWorkOrderList) {
                List<WorkOrderVO> workOrderVOS = workOrderMapOfParentId.get(workOrderVO.getId());
                String parentWorkOrderStatus = workOrderService.getParentWorkOrderStatus(WorkOrderConvertor.INSTANCE.vo2Dtos(workOrderVOS));
                workOrderVO.setPlanStatus(parentWorkOrderStatus);
            }
            //更新父制造订单状态
            workOrderService.doUpdateBatch(WorkOrderConvertor.INSTANCE.vo2Dtos(parentWorkOrderList));
        }
    }

    private String computeParentOperationStatus(List<FeedbackProductionPO> feedbackProductionPOS) {
        List<FeedbackProductionPO> reportingTimeNotNullList = feedbackProductionPOS.stream().filter(item -> null != item.getReportingTime()).collect(Collectors.toList());
        List<FeedbackProductionPO> endTimeList = feedbackProductionPOS.stream().filter(item -> null != item.getEndTime()).collect(Collectors.toList());
        if (reportingTimeNotNullList.size() == feedbackProductionPOS.size()) {
            //取“实绩取得时刻”最晚的生产反馈“状态”
            FeedbackProductionPO feedbackProductionPO = feedbackProductionPOS.stream().sorted(Comparator.comparing(FeedbackProductionPO::getReportingTime)).collect(Collectors.toList()).get(feedbackProductionPOS.size() - 1);
            feedbackProductionPOS.forEach(item -> item.setReportingStatus(feedbackProductionPO.getReportingStatus()));
            return feedbackProductionPO.getReportingStatus();
        }
        if (endTimeList.size() == feedbackProductionPOS.size()) {
            //取“实绩结束时刻”最晚的生产反馈“状态”
            FeedbackProductionPO feedbackProductionPO = feedbackProductionPOS.stream().sorted(Comparator.comparing(FeedbackProductionPO::getEndTime)).collect(Collectors.toList()).get(feedbackProductionPOS.size() - 1);
            feedbackProductionPOS.forEach(item -> item.setReportingStatus(feedbackProductionPO.getReportingStatus()));
            return feedbackProductionPO.getReportingStatus();
        }
        //存在“实绩取得时刻”为空的情况，且“实绩结束时刻”存在为空
        List<String> statusList = feedbackProductionPOS.stream().map(FeedbackProductionBasicPO::getReportingStatus).distinct().collect(Collectors.toList());
        if (statusList.size() > 1) {
            //状态不一致，则生产反馈状态修改为“已开始”
            return ReportingStatusEnum.STARTED.getCode();
        }
        //状态一致
        return feedbackProductionPOS.get(0).getReportingStatus();
    }

    private String getParentOperationStatus(BigDecimal parentOperationQuantity, List<OperationDTO> operations) {
        if (CollectionUtils.isEmpty(operations)) {
            return PlannedStatusEnum.UNPLAN.getCode();
        }
        for (OperationDTO operation : operations) {
            if (operation.getQuantity() == null) {
                log.info("getParentOperationStatus，exist children qty is null, operationId is {}", operation.getParentId());
            }
        }
        Boolean leftQtyFlag = Boolean.FALSE;
        //父工序有剩余量则reserve置为TRUE，表示待拆
        BigDecimal subOperationQty = operations.stream().map(OperationDTO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        if ((parentOperationQuantity.subtract(subOperationQty)).compareTo(new BigDecimal(0)) > 0) {
            leftQtyFlag = Boolean.TRUE;
        }
        long plannedSize = operations.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.PLANNED.getCode())).count();
        long issuedPlanSize = operations.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.ISSUED.getCode())).count();
        long identifiedPlanSize = operations.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.PLAN_IDENTIFIED.getCode())).count();
        long startedPlanSize = operations.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.STARTED.getCode())).count();
        long finishedPlanSize = operations.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.FINISHED.getCode())).count();
        if (CollectionUtils.isEmpty(operations)) {
            return PlannedStatusEnum.UNPLAN.getCode();
        }
        if (!leftQtyFlag && plannedSize == operations.size()) {
            return PlannedStatusEnum.PLANNED.getCode();
        }
        if (leftQtyFlag) {
            return PlannedStatusEnum.SOME_PLANNED.getCode();
        }
        if (issuedPlanSize == operations.size()) {
            return PlannedStatusEnum.ISSUED.getCode();
        }
        if (issuedPlanSize > 0 && identifiedPlanSize == 0 && startedPlanSize == 0 && finishedPlanSize == 0) {
            return PlannedStatusEnum.ISSUED.getCode();
        }
        if (identifiedPlanSize == operations.size()) {
            return PlannedStatusEnum.PLAN_IDENTIFIED.getCode();
        }
        if (identifiedPlanSize > 0 && startedPlanSize == 0 && finishedPlanSize == 0) {
            return PlannedStatusEnum.PLAN_IDENTIFIED.getCode();
        }
        if (finishedPlanSize == operations.size()) {
            return PlannedStatusEnum.FINISHED.getCode();
        }
        if (finishedPlanSize > 0 && identifiedPlanSize > 0 && (finishedPlanSize + identifiedPlanSize) >= operations.size()) {
            return PlannedStatusEnum.FINISHED.getCode();
        }
        return PlannedStatusEnum.STARTED.getCode();
    }

    @Override
    public List<FeedbackProductionDTO> doUpdateOtherOperationStatus(PlanningHorizonVO planningHorizon, List<FeedbackProductionDTO> allFeedbackProduction, String logId) {
        // 每个报工的产线，只留最后一个工序进入生产反馈算法，其他的工序全部更新成完工
        List<String> resourceIds = allFeedbackProduction.stream()
                .map(FeedbackProductionDTO::getPhysicalResourceId).distinct().collect(Collectors.toList());
        // String startTimeStr = DateUtils.dateToString(planningHorizon.getPlanStartTime(), DateUtils.COMMON_DATE_STR1);
        // 报工产线上排的所有的工单id
        List<String> orderIds = masterPlanExtDao.selectWorkOrderIdsByResourceId(resourceIds);
        if (CollectionUtils.isEmpty(orderIds)) {
            return allFeedbackProduction;
        }
        List<WorkOrderVO> workOrderVOList = workOrderService.selectByParams(ImmutableMap.of("ids",orderIds));

        List<OperationPO> operationPOList = operationDao.selectByOrderIds(orderIds);
        Map<String, List<OperationPO>> subOperationOfResourceIdMap = operationPOList.stream()
                .filter(t -> StringUtils.isNotEmpty(t.getPlannedResourceId()) &&
                        StringUtils.isNotEmpty(t.getParentId()))
                .collect(Collectors.groupingBy(OperationBasicPO::getPlannedResourceId));

        Map<String, OperationPO> operaionOfIdMap = operationPOList.stream().collect(Collectors.toMap(BasePO::getId, Function.identity()));

        Map<String, List<OperationPO>> childOperationMapOfParentId = operationPOList.stream().filter(item ->
                StringUtils.isNotBlank(item.getParentId())).collect(Collectors.groupingBy(OperationPO::getParentId));

        List<String> subOperationIds = childOperationMapOfParentId.values().stream().flatMap(Collection::stream)
                .map(BasePO::getId).collect(Collectors.toList());
        List<OperationTaskPO> operationTaskList = operationTaskDao.selectByParams(ImmutableMap.of("operationIds", subOperationIds));
        Map<String, List<OperationTaskPO>> operationTaskMapOfOperationId = operationTaskList.stream()
                .collect(Collectors.groupingBy(OperationTaskPO::getOperationId));
        List<OperationSubTaskPO> operationSubTaskList = operationSubTaskDao.selectByParams(ImmutableMap.of("operationIds", subOperationIds));
        Map<String, List<OperationSubTaskPO>> operationSubTaskMapOfOperationId = operationSubTaskList.stream()
                .collect(Collectors.groupingBy(OperationSubTaskPO::getOperationId));

        Map<String, List<FeedbackProductionDTO>> feedbackProductionMap = allFeedbackProduction.stream()
                .collect(Collectors.groupingBy(FeedbackProductionBasicDTO::getPhysicalResourceId));

        Map<String, FeedbackProductionDTO> feedbackOfOperationMap = allFeedbackProduction.stream()
                .collect(Collectors.toMap(FeedbackProductionDTO::getOperationId, Function.identity(),
                        (v1, v2) -> v1));

        List<OperationPO> updateParentOperationList = new ArrayList<>();
        List<OperationPO> updateSubOperationList = new ArrayList<>();

        List<OperationTaskPO> updateOperationTaskList = new ArrayList<>();
        List<OperationSubTaskPO> updateOperationSubTaskList = new ArrayList<>();
        List<SdsOrdOperationHistoryLogPO> insertOperationHistoryLogList = new ArrayList<>();

        List<FeedbackProductionDTO> updateFeedbackProductionList = new ArrayList<>();
        List<FeedbackProductionDTO> needFeedbackProductionList = new ArrayList<>();
        List<OperationPO> noFeedBackOperation = new ArrayList<>();
        for (Map.Entry<String, List<FeedbackProductionDTO>> entry : feedbackProductionMap.entrySet()) {
            String resourceId = entry.getKey();
            List<FeedbackProductionDTO> feedbackProductionList = entry.getValue();
            List<OperationPO> operationPOs = subOperationOfResourceIdMap.get(resourceId);
            if (CollectionUtils.isEmpty(operationPOs)) {
                log.info("doUpdateOtherOperationStatus, no sub operation, resourceId is {}", resourceId);
                continue;
            }
            List<String> operationIds = feedbackProductionList.stream()
                    .map(FeedbackProductionBasicDTO::getOperationId).collect(Collectors.toList());
            // 需要进生产反馈算法的工序
            OperationPO needFeedBackOperation = operationPOs.stream().filter(t -> operationIds.contains(t.getId()))
                    .max(Comparator.comparing(OperationPO::getStartTime)).orElse(null);
            if (needFeedBackOperation == null) {
                log.info("doUpdateOtherOperationStatus, no need feedback operation, resourceId is {}", resourceId);
                continue;
            }
            OperationPO maxStartOrFinishOperation = operationPOs.stream().filter(t -> feedbackOperationStatusList.contains(t.getPlanStatus()))
                    .max(Comparator.comparing(OperationPO::getStartTime)).orElse(null);
            if (maxStartOrFinishOperation != null && feedbackOperationStatusList.contains(needFeedBackOperation.getPlanStatus())) {
                String id = maxStartOrFinishOperation.getId();
                // 当前报工的不是这条线上最晚开始的工序，不需要进生产反馈算法，这条数据直接更新成已完工，构建成点
                if (!id.equals(needFeedBackOperation.getId())) {
                    log.info("doUpdateOtherOperationStatus, no need feedback operation " +
                            "because operation {} is not last start operation, lastOperationId {}, resourceId is {}",needFeedBackOperation.getId(), id, resourceId);
                    noFeedBackOperation.add(needFeedBackOperation);
                    updateFeedbackProductionList.add(feedbackOfOperationMap.get(needFeedBackOperation.getId()));
                    continue;
                }
            }
            List<FeedbackProductionDTO> needFeedbackList = feedbackProductionList.stream().filter(t -> needFeedBackOperation.getId().equals(t.getOperationId()))
                    .collect(Collectors.toList());
            List<FeedbackProductionDTO> updateFeedbackList = feedbackProductionList.stream().filter(t -> !needFeedBackOperation.getId().equals(t.getOperationId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(needFeedbackList)) {
                needFeedbackProductionList.addAll(needFeedbackList);
            }
            if (CollectionUtils.isNotEmpty(updateFeedbackList)) {
                updateFeedbackProductionList.addAll(updateFeedbackList);
            }
            List<OperationPO> collect = operationPOs.stream()
                    .filter(t -> !t.getId().equals(needFeedBackOperation.getId()) &&
                            operationIds.contains(t.getId()))
                    .collect(Collectors.toList());
            noFeedBackOperation.addAll(collect);
        }
        if (CollectionUtils.isNotEmpty(noFeedBackOperation)) {
            // 更新非进生产反馈算的工序状态
            for (OperationPO operationPO : noFeedBackOperation) {
                FeedbackProductionDTO feedbackProductionDTO = feedbackOfOperationMap.get(operationPO.getId());
                insertOperationHistoryLogList.add(createOperationHistoryLogPO(operationPO,
                        feedbackProductionDTO.getReportingTime(), feedbackProductionDTO.getReportingTime(), logId));

                operationPO.setPlanStatus(PlannedStatusEnum.FINISHED.getCode());
                updateSubOperationList.add(operationPO);
                String parentId = operationPO.getParentId();
                OperationPO parentOperationPO = operaionOfIdMap.get(parentId);
                if (parentOperationPO == null) {
                    log.warn("doUpdateOtherOperationStatus, no parent operation, operationId is {}", parentId);
                    continue;
                }
                updateOperationTask(operationPO, operationTaskMapOfOperationId, feedbackProductionDTO, updateOperationTaskList);
                updateOperationSubTask(operationPO, operationSubTaskMapOfOperationId, feedbackProductionDTO, updateOperationSubTaskList);

                operationPO.setStartTime(feedbackProductionDTO.getReportingTime());
                operationPO.setEndTime(feedbackProductionDTO.getReportingTime());
                insertOperationHistoryLogList.add(createOperationHistoryLogPO(parentOperationPO,
                        feedbackProductionDTO.getReportingTime(), feedbackProductionDTO.getReportingTime(), logId));
                parentOperationPO.setStartTime(feedbackProductionDTO.getReportingTime());
                parentOperationPO.setEndTime(feedbackProductionDTO.getReportingTime());
                updateParentOperationList.add(parentOperationPO);
            }
        }
        if (CollectionUtils.isNotEmpty(updateParentOperationList)) {
            for (OperationPO operationPO : updateParentOperationList) {
                List<OperationPO> childOperationPOS = childOperationMapOfParentId.get(operationPO.getId());
                //获取父更新状态
                operationPO.setPlanStatus(getParentOperationStatus(operationPO.getQuantity(), OperationConvertor.INSTANCE.po2Dtos(childOperationPOS)));
            }
        }
        List<OperationPO> allUpdateOperationList = new ArrayList<>();
        allUpdateOperationList.addAll(updateParentOperationList);
        allUpdateOperationList.addAll(updateSubOperationList);
        if (CollectionUtils.isNotEmpty(allUpdateOperationList)) {
            BasePOUtils.updateBatchFiller(allUpdateOperationList);
            allUpdateOperationList.forEach(t -> t.setRemark("报工更新-非最后一道工序"));
            operationDao.updateBatch(allUpdateOperationList);
        }
        if (CollectionUtils.isNotEmpty(updateOperationTaskList)) {
            BasePOUtils.updateBatchFiller(updateOperationTaskList);
            operationTaskDao.updateBatch(updateOperationTaskList);
        }
        if (CollectionUtils.isNotEmpty(updateOperationSubTaskList)) {
            BasePOUtils.updateBatchFiller(updateOperationSubTaskList);
            operationSubTaskDao.updateBatch(updateOperationSubTaskList);
        }
        if (CollectionUtils.isNotEmpty(insertOperationHistoryLogList)) {
            rzzAlgorithmOutputService.saveOperationHistoryLog(insertOperationHistoryLogList);
        }
        if (CollectionUtils.isNotEmpty(updateFeedbackProductionList)) {
            updateFeedbackProductionList.forEach(t -> t.setWhetherFeedBack(YesOrNoEnum.YES.getCode()));
            feedbackProductionService.doUpdateBatch(updateFeedbackProductionList);
        }
        // 更新工单状态
        doUpdateWorkOrderPlanStatus(operationPOList, workOrderVOList);
        return needFeedbackProductionList;
    }

    private static void updateOperationSubTask(OperationPO operationPO, Map<String, List<OperationSubTaskPO>> operationSubTaskMapOfOperationId, FeedbackProductionDTO feedbackProductionDTO, List<OperationSubTaskPO> updateOperationSubTaskList) {
        List<OperationSubTaskPO> operationSubTaskPOS = operationSubTaskMapOfOperationId.get(operationPO.getId());
        if (CollectionUtils.isNotEmpty(operationSubTaskPOS)) {
            operationSubTaskPOS.forEach(t -> {
                t.setStartTime(feedbackProductionDTO.getReportingTime());
                t.setEndTime(feedbackProductionDTO.getReportingTime());
            });
            updateOperationSubTaskList.addAll(operationSubTaskPOS);
        }
    }

    private static void updateOperationTask(OperationPO operationPO, Map<String, List<OperationTaskPO>> operationTaskMapOfOperationId, FeedbackProductionDTO feedbackProductionDTO, List<OperationTaskPO> updateOperationTaskList) {
        List<OperationTaskPO> operationTaskPOS = operationTaskMapOfOperationId.get(operationPO.getId());
        if (CollectionUtils.isNotEmpty(operationTaskPOS)) {
            operationTaskPOS.forEach(t -> {
                t.setStartTime(feedbackProductionDTO.getReportingTime());
                t.setEndTime(feedbackProductionDTO.getReportingTime());
                t.setSetupStartTime(feedbackProductionDTO.getReportingTime());
                t.setSetupEndTime(feedbackProductionDTO.getReportingTime());
                t.setProductionStartTime(feedbackProductionDTO.getReportingTime());
                t.setProductionEndTime(feedbackProductionDTO.getReportingTime());
                t.setCleanupStartTime(feedbackProductionDTO.getReportingTime());
                t.setCleanupEndTime(feedbackProductionDTO.getReportingTime());
            });
            updateOperationTaskList.addAll(operationTaskPOS);
        }
    }


    private SdsOrdOperationHistoryLogPO createOperationHistoryLogPO(OperationPO operationPO, Date startTime, Date endTime, String logId) {
        SdsOrdOperationHistoryLogPO operationHistoryLogPO = new SdsOrdOperationHistoryLogPO();
        operationHistoryLogPO.setId(UUIDUtil.getUUID());
        operationHistoryLogPO.setLogId(logId);
        operationHistoryLogPO.setOperationId(operationPO.getId());
        operationHistoryLogPO.setWorkOrderId(operationPO.getOrderId());
        operationHistoryLogPO.setPlanStatus(operationPO.getPlanStatus());
        operationHistoryLogPO.setParentOperationId(operationPO.getParentId());
        operationHistoryLogPO.setSourcePlannedResourceId(operationPO.getPlannedResourceId());
        operationHistoryLogPO.setSourceStartTime(operationPO.getStartTime());
        operationHistoryLogPO.setSourceEndTime(operationPO.getEndTime());
        operationHistoryLogPO.setTargetPlannedResourceId(operationPO.getPlannedResourceId());
        operationHistoryLogPO.setTargetStartTime(startTime);
        operationHistoryLogPO.setTargetEndTime(endTime);
        operationHistoryLogPO.setRemark("报工更新-构建成点");
        return operationHistoryLogPO;
    }
}
