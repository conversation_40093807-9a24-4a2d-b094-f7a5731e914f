package com.yhl.scp.mps.capacityBalance.service.impl;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.biz.common.enums.MinIOBusinessEnum;
import com.yhl.scp.biz.common.enums.RedisKeyManageEnum;
import com.yhl.scp.biz.common.util.MinioUtils;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mps.capacityBalance.convertor.CapacityBalanceVersionConvertor;
import com.yhl.scp.mps.capacityBalance.domain.entity.CapacityBalanceVersionDO;
import com.yhl.scp.mps.capacityBalance.domain.service.CapacityBalanceVersionDomainService;
import com.yhl.scp.mps.capacityBalance.dto.CapacityBalanceVersionDTO;
import com.yhl.scp.mps.capacityBalance.enums.CapacityBalanceRule;
import com.yhl.scp.mps.capacityBalance.enums.CapacityBalanceTypeEnum;
import com.yhl.scp.mps.capacityBalance.enums.LockStatusEnum;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacityBalanceVersionDao;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacityLoadDao;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacitySupplyRelationshipDao;
import com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacityBalanceVersionPO;
import com.yhl.scp.mps.capacityBalance.service.CapacityBalanceVersionService;
import com.yhl.scp.mps.capacityBalance.service.CapacityLoadService;
import com.yhl.scp.mps.capacityBalance.service.CapacitySupplyRelationshipService;
import com.yhl.scp.mps.capacityBalance.service.CapacityWeekBalanceService;
import com.yhl.scp.mps.capacityBalance.vo.*;
import com.yhl.scp.mps.enums.ObjectTypeEnum;
import com.yhl.scp.mps.enums.SupplyModelEnum;
import com.yhl.scp.mps.excel.model.MasterPlanMultipartFile;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>CapacityBalanceVersionServiceImpl</code>
 * <p>
 * 产能平衡版本应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-16 14:16:50
 */
@Slf4j
@Service
public class CapacityBalanceVersionServiceImpl extends AbstractService implements CapacityBalanceVersionService {

    @Resource
    private CapacityBalanceVersionDao capacityBalanceVersionDao;

    @Resource
    private CapacityBalanceVersionDomainService capacityBalanceVersionDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private CapacityLoadService capacityLoadService;

    @Resource
    private CapacityLoadDao capacityLoadDao;

    @Resource
    private CapacitySupplyRelationshipService capacitySupplyRelationshipService;

    @Resource
    private CapacitySupplyRelationshipDao capacitySupplyRelationshipDao;

    @Resource
    private CapacityWeekBalanceService capacityWeekBalanceService;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private MinioUtils minioUtils;
    @Value("${minio.bucket}")
    private String bucketName;


    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(CapacityBalanceVersionDTO capacityBalanceVersionDTO) {
        // 0.数据转换
        CapacityBalanceVersionDO capacityBalanceVersionDO = CapacityBalanceVersionConvertor.INSTANCE.dto2Do(capacityBalanceVersionDTO);
        CapacityBalanceVersionPO capacityBalanceVersionPO = CapacityBalanceVersionConvertor.INSTANCE.dto2Po(capacityBalanceVersionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        capacityBalanceVersionDomainService.validation(capacityBalanceVersionDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(capacityBalanceVersionPO);
        capacityBalanceVersionDao.insertWithPrimaryKey(capacityBalanceVersionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(CapacityBalanceVersionDTO capacityBalanceVersionDTO) {
        // 0.数据转换
        CapacityBalanceVersionDO capacityBalanceVersionDO = CapacityBalanceVersionConvertor.INSTANCE.dto2Do(capacityBalanceVersionDTO);
        CapacityBalanceVersionPO capacityBalanceVersionPO = CapacityBalanceVersionConvertor.INSTANCE.dto2Po(capacityBalanceVersionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        capacityBalanceVersionDomainService.validation(capacityBalanceVersionDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(capacityBalanceVersionPO);
        capacityBalanceVersionDao.update(capacityBalanceVersionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<CapacityBalanceVersionDTO> list) {
        List<CapacityBalanceVersionPO> newList = CapacityBalanceVersionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        capacityBalanceVersionDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<CapacityBalanceVersionDTO> list) {
        List<CapacityBalanceVersionPO> newList = CapacityBalanceVersionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        capacityBalanceVersionDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return capacityBalanceVersionDao.deleteBatch(idList);
        }
        return capacityBalanceVersionDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public CapacityBalanceVersionVO selectByPrimaryKey(String id) {
        CapacityBalanceVersionPO po = capacityBalanceVersionDao.selectByPrimaryKey(id);
        return CapacityBalanceVersionConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "CAPACITY_BALANCE_VERSION")
    public List<CapacityBalanceVersionVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "CAPACITY_BALANCE_VERSION")
    public List<CapacityBalanceVersionVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<CapacityBalanceVersionVO> dataList = capacityBalanceVersionDao.selectByCondition(sortParam, queryCriteriaParam);
        CapacityBalanceVersionServiceImpl target = springBeanUtils.getBean(CapacityBalanceVersionServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<CapacityBalanceVersionVO> selectByParams(Map<String, Object> params) {
        List<CapacityBalanceVersionPO> list = capacityBalanceVersionDao.selectByParams(params);
        return CapacityBalanceVersionConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<CapacityBalanceVersionVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.CAPACITY_BALANCE_VERSION.getCode();
    }

    @Override
    public List<CapacityBalanceVersionVO> invocation(List<CapacityBalanceVersionVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    @BusinessMonitorLog(businessCode = "月度产能平衡发布", moduleCode = "MPS", businessFrequency = "MONTH")
    public BaseResponse<String> publishVersionLock() {
        Boolean setIfAbsent = redisTemplate.opsForValue().setIfAbsent(RedisKeyManageEnum.CAPACITY_BALANCE_PUBLISH.getKey(), RedisKeyManageEnum.CAPACITY_BALANCE_PUBLISH.getKey(), 30, TimeUnit.MINUTES);
        if (!Objects.nonNull(setIfAbsent) || Boolean.FALSE.equals(setIfAbsent)) {
            throw new BusinessException("产能平衡发布中, 请勿重复执行");
        }
        BaseResponse<String> baseResponse = BaseResponse.success();
        try {
            publishVersion(SystemHolder.getScenario());
        } catch (Exception e) {
            log.error("执行报错", e);
            baseResponse = BaseResponse.error("执行报错：" + e.getMessage());
        } finally {
            redisTemplate.delete(RedisKeyManageEnum.CAPACITY_BALANCE_PUBLISH.getKey());
        }
        return baseResponse;
    }

    @Override
    public BaseResponse<String> weekPublishVersionLock() {
        Boolean setIfAbsent = redisTemplate.opsForValue().setIfAbsent(RedisKeyManageEnum.CAPACITY_BALANCE_WEEK_PUBLISH.getKey(), RedisKeyManageEnum.CAPACITY_BALANCE_WEEK_PUBLISH.getKey(), 30, TimeUnit.MINUTES);
        if (!Objects.nonNull(setIfAbsent) || Boolean.FALSE.equals(setIfAbsent)) {
            throw new BusinessException("周产能平衡发布中, 请勿重复执行");
        }
        BaseResponse<String> baseResponse = BaseResponse.success();
        try {
            weekPublishVersion();
        } catch (Exception e) {
            log.error("执行报错", e);
            baseResponse = BaseResponse.error("执行报错：" + e.getMessage());
        } finally {
            redisTemplate.delete(RedisKeyManageEnum.CAPACITY_BALANCE_WEEK_PUBLISH.getKey());
        }
        return baseResponse;
    }

    @Override
    public void publishVersion(String scenario) {
        CapacitySupplyRelationshipVO capacitySupplyRelationshipVO = capacitySupplyRelationshipDao.selectLastVersion();
        if (capacitySupplyRelationshipVO == null) { //没有要发布的数据
            log.warn("没有可发布的数据");
            return;
        }

        StopWatch stopWatch = new StopWatch("产能平衡数据发布");
        stopWatch.start("保存版本数据文件");
        //对应的一致性业务预测计划周期
        String planPeriod = capacitySupplyRelationshipVO.getPlanPeriod();
        String versionCode = getVersionCode(planPeriod);

        // 保存版本文件
        String fileName = exportExcelAndUpload(versionCode, scenario);

        stopWatch.stop();
        stopWatch.start("删除过期版本数据");
        // 始终保留每月第一版的数据
        List<String> needDeleteVersionIds = capacityBalanceVersionDao.selectNeedDeleteVersion();
        // String currentMonthFirstVersionCode = planPeriod + "V1";
        // needDeleteVersionIds.remove(currentMonthFirstVersionCode);
        capacitySupplyRelationshipService.deleteByVersionIds(needDeleteVersionIds);
        capacityLoadService.doDeleteByVersionIds(needDeleteVersionIds);

        CapacityBalanceVersionPO capacityBalanceVersionPO = new CapacityBalanceVersionPO();
        capacityBalanceVersionPO.setForecastMonth(planPeriod);
        capacityBalanceVersionPO.setForecastVersion(capacitySupplyRelationshipVO.getVersionCode());
        capacityBalanceVersionPO.setVersionCode(versionCode);
        capacityBalanceVersionPO.setFileName(fileName);
        capacityBalanceVersionPO.setType(CapacityBalanceTypeEnum.MONTH.getCode());
        BasePOUtils.insertFiller(capacityBalanceVersionPO);
        capacityBalanceVersionDao.insertWithPrimaryKey(capacityBalanceVersionPO);

        stopWatch.stop();
        stopWatch.start("开始保存一版最新产能供应关系");
        log.info("开始保存一版最新产能供应关系");
        CapacityBalanceVersionVO capacityBalanceVersionVO = this.selectLatestVersionCode();
        log.info("$$$$$$$$$$$$ 最新版本产能平衡数据：{}", capacityBalanceVersionVO.getVersionCode());
        //不存id了，直接存版本号
        String versionId = capacityBalanceVersionPO.getVersionCode();
        capacitySupplyRelationshipService.doSaveCapacitySupplyRelationshipBasedOnVersion(versionId);
        stopWatch.stop();
        stopWatch.start("开始保存最新产能负荷数据");
        log.info("开始保存最新产能负荷数据");
        capacityLoadService.doSaveVersionCapacityLoad(versionId);
        stopWatch.stop();
        // stopWatch.start("刷新产品资源生产关系");
        // log.info("刷新产品资源生产关系");
        // String scenario = SystemHolder.getScenario();
        // CompletableFuture.runAsync(() -> {
        //     DynamicDataSourceContextHolder.setDataSource(scenario);
        //     capacitySupplyRelationshipService.saveEquipmentProductionRelation(scenario);
        //     DynamicDataSourceContextHolder.clearDataSource();
        // });
        // stopWatch.stop();
        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
    }

    @Override
    public void weekPublishVersion() {
        CapacitySupplyRelationshipVO capacitySupplyRelationshipVO = capacitySupplyRelationshipDao.selectWeekLastVersion();
        if (capacitySupplyRelationshipVO == null) { //没有要发布的数据
            log.warn("没有可发布的数据");
            return;
        }
        String weekVersionCode = getWeekVersionCode();
        StopWatch stopWatch = new StopWatch("周产能平衡数据发布");
        stopWatch.start("保存版本数据文件");
        // 保存版本文件
        String fileName = weekExportExcelAndUpload(weekVersionCode);

        stopWatch.stop();
        stopWatch.start("保存版本数据");
        CapacityBalanceVersionPO capacityBalanceVersionPO = new CapacityBalanceVersionPO();
        capacityBalanceVersionPO.setVersionCode(weekVersionCode);
        capacityBalanceVersionPO.setFileName(fileName);
        capacityBalanceVersionPO.setType(CapacityBalanceTypeEnum.WEEK.getCode());
        BasePOUtils.insertFiller(capacityBalanceVersionPO);
        capacityBalanceVersionDao.insertWithPrimaryKey(capacityBalanceVersionPO);
        stopWatch.stop();
        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
    }

    @Override
    public CapacityBalanceVersionVO selectLatestVersionCode() {
        return capacityBalanceVersionDao.selectLatestVersionCode(null, CapacityBalanceTypeEnum.MONTH.getCode());
    }

    @Override
    public List<CapacityLoadVO4> contrastCapacityLoad(List<String> versionIds, String plantCode, String operationCode, String operationName, String resourceGroupCode, String resourceCode) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("versionIds", versionIds);
        map.put("plantCode", plantCode);
        map.put("operationCode", operationCode);
        map.put("operationName", operationName);
        map.put("resourceGroupCode", resourceGroupCode);
        map.put("resourceCode", resourceCode);
        List<CapacityLoadVO> capacityLoadVOS = capacityLoadService.selectByParams(map);
        List<Date> dateList = capacityLoadVOS.stream().sorted(Comparator.comparing(CapacityLoadVO::getForecastTime)).map(CapacityLoadVO::getForecastTime).distinct().collect(Collectors.toList());
        Map<Date, BigDecimal> capacityMap = new HashMap<>();
        dateList.forEach(t -> {
            int year = DateUtils.getYearByDate(t);
            int month = DateUtils.getMonthByDate(t) + 1;
            int days = DateUtils.getMonthDayCount(year, month) - 4;
            int i = days * 24 * 3600;
            capacityMap.put(t, BigDecimal.valueOf(i));
        });
        capacityLoadVOS.forEach(t -> {
            t.setAvailableCapacity(capacityMap.get(t.getForecastTime()));
            t.setCapacityUtilization(t.getProductionCapacity().divide(t.getAvailableCapacity(), 2, RoundingMode.HALF_UP));
        });
        // 根据工厂编码+设备组+设备分组整合数据并转换为需要的格式
        Map<String, List<CapacityLoadVO>> groupMap = capacityLoadVOS.stream().collect(Collectors.groupingBy(e -> e.getPlantCode() + e.getResourceGroupCode() + e.getResourceCode()));
        List<String> monthList = capacityLoadVOS.stream().map(t -> DateUtils.dateToString(t.getForecastTime(), "yyyyMM")).distinct().sorted().collect(Collectors.toList());
        List<String> versionCodeList = capacityLoadVOS.stream().map(CapacityLoadVO::getVersionId).distinct().collect(Collectors.toList());
        List<CapacityLoadVO4> vo4List = new ArrayList<>();
        for (Map.Entry<String, List<CapacityLoadVO>> entry : groupMap.entrySet()) {
            List<CapacityLoadVO> value = entry.getValue();
            CapacityLoadVO value1 = value.get(0);
            CapacityLoadVO4 capacityLoadVO4 = new CapacityLoadVO4();
            BeanUtils.copyProperties(value1, capacityLoadVO4);
            List<CapacityLoadVO3> versionList = new ArrayList<>();
            Map<String, List<CapacityLoadVO>> versionMap = value.stream().collect(Collectors.groupingBy(CapacityLoadVO::getVersionId));
            for (String versionCode : versionIds) {
                CapacityLoadVO3 capacityLoadVO3 = new CapacityLoadVO3();
                capacityLoadVO3.setVersionCode(versionCode);
                List<CapacityLoadVO2> dataList = new ArrayList<>();
                if (versionMap.containsKey(versionCode)) {
                    Map<String, List<CapacityLoadVO>> monthMap = versionMap.get(versionCode).stream().collect(Collectors.groupingBy(t -> DateUtils.dateToString(t.getForecastTime(), "yyyyMM")));
                    for (String month : monthList) {
                        if (monthMap.containsKey(month)) {
                            List<CapacityLoadVO> list = monthMap.get(month);
                            for (CapacityLoadVO capacityLoadVO : list) {
                                CapacityLoadVO2 capacityLoadVO2 = new CapacityLoadVO2();
                                BeanUtils.copyProperties(capacityLoadVO, capacityLoadVO2);
                                dataList.add(capacityLoadVO2);
                            }
                        } else {
                            CapacityLoadVO2 capacityLoadVO2 = new CapacityLoadVO2();
                            capacityLoadVO2.setForecastTime(DateUtils.stringToDate(month, "yyyyMM"));
                            capacityLoadVO2.setCapacityUtilization(BigDecimal.ZERO);
                            capacityLoadVO2.setDemandQuantity(BigDecimal.ZERO);
                            capacityLoadVO2.setProductionCapacity(BigDecimal.ZERO);
                            dataList.add(capacityLoadVO2);
                        }
                    }
                } else {
                    for (String month : monthList) {
                        CapacityLoadVO2 capacityLoadVO2 = new CapacityLoadVO2();
                        capacityLoadVO2.setForecastTime(DateUtils.stringToDate(month, "yyyyMM"));
                        capacityLoadVO2.setCapacityUtilization(BigDecimal.ZERO);
                        capacityLoadVO2.setDemandQuantity(BigDecimal.ZERO);
                        capacityLoadVO2.setProductionCapacity(BigDecimal.ZERO);
                        dataList.add(capacityLoadVO2);
                    }
                }
                capacityLoadVO3.setDataList(dataList);
                versionList.add(capacityLoadVO3);
            }
            capacityLoadVO4.setVersionList(versionList);
            vo4List.add(capacityLoadVO4);
        }
        return vo4List;
    }

    @Override
    public List<CapacitySupplyRelationshipVO2> contrastCapacitySupplyRelationship(List<String> versionIds, String operationCode, String operationName, String supplyTimeStart, String supplyTimeEnd, String productCode) {
        if ((supplyTimeStart == null) != (supplyTimeEnd == null)) {
            throw new BusinessException("起始时间，结束时间必须同时存在");
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("versionIds", versionIds);
        map.put("operationCode", operationCode);
        map.put("operationName", operationName);
        map.put("supplyTimeStart", supplyTimeStart);
        map.put("supplyTimeEnd", supplyTimeEnd);
        map.put("productCode", productCode);
        List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOS = capacitySupplyRelationshipService.selectByParams(map);
        // 根据工厂编码+设备组+设备分组整合数据并转换为需要的格式
        Map<String, List<CapacitySupplyRelationshipVO>> groupMap = capacitySupplyRelationshipVOS.stream().collect(Collectors.groupingBy(e -> e.getProductCode() + e.getOperationCode() + e.getResourceCode()));
        List<String> monthList = capacitySupplyRelationshipVOS.stream().map(t -> DateUtils.dateToString(t.getForecastTime(), "yyyyMM")).distinct().sorted().collect(Collectors.toList());
        List<String> versionCodeList = capacitySupplyRelationshipVOS.stream().map(CapacitySupplyRelationshipVO::getVersionId).distinct().sorted().collect(Collectors.toList());
        List<CapacitySupplyRelationshipVO2> vo2List = new ArrayList<>();
        for (Map.Entry<String, List<CapacitySupplyRelationshipVO>> entry : groupMap.entrySet()) {
            List<CapacitySupplyRelationshipVO> value = entry.getValue();
            CapacitySupplyRelationshipVO value1 = value.get(0);
            CapacitySupplyRelationshipVO2 vo2 = new CapacitySupplyRelationshipVO2();
            vo2.setProductCode(value1.getProductCode());
            vo2.setProductName(value1.getProductName());
            vo2.setOperationCode(value1.getOperationCode());
            vo2.setOperationName(value1.getOperationName());
            vo2.setResourceCode(value1.getResourceCode());
            vo2.setResourceName(value1.getResourceName());
            List<CapacitySupplyRelationshipVO3> dataList = new ArrayList<>();
            Map<String, List<CapacitySupplyRelationshipVO>> forecastTimeMap = value.stream().collect(Collectors.groupingBy(t -> DateUtils.dateToString(t.getForecastTime(), "yyyyMM")));
            for (String forecastTime : monthList) {

                CapacitySupplyRelationshipVO3 vo3 = new CapacitySupplyRelationshipVO3();
                vo3.setForecastTime(forecastTime);
                List<CapacitySupplyRelationshipVO4> versionList = new ArrayList<>();
                if (forecastTimeMap.containsKey(forecastTime)) {
                    Map<String, List<CapacitySupplyRelationshipVO>> versionMap = forecastTimeMap.get(forecastTime).stream().collect(Collectors.groupingBy(CapacitySupplyRelationshipVO::getVersionId));
                    for (String versionCode : versionIds) {
                        CapacitySupplyRelationshipVO4 vo4 = new CapacitySupplyRelationshipVO4();
                        vo4.setVersionCode(versionCode);
                        if (versionMap.containsKey(versionCode)) {
                            vo4.setSupplyModel("本厂/委外");
        /*                    if (SupplyModelEnum.OUTSOURCED.getCode().equals(versionMap.get(versionCode).get(0).getSupplyModel())){
                                vo4.setSupplyQuantity(versionMap.get(versionCode).get(0).getDemandQuantity());
                            }else {
                                vo4.setSupplyQuantity(versionMap.get(versionCode).get(0).getSupplyQuantity());
                            }
                            vo4.setDemandQuantity(versionMap.get(versionCode).get(0).getDemandQuantity());*/
                            //todo 这里有点问题，这一个月的供应关系可能半个月是
                            BigDecimal demandQuantity = versionMap.get(versionCode).stream()
                                    .map(CapacitySupplyRelationshipVO::getDemandQuantity)
                                    .filter(quantity -> quantity.compareTo(BigDecimal.ZERO) >= 0)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            vo4.setDemandQuantity(demandQuantity);
                            BigDecimal outSourcedQty = versionMap.get(versionCode).stream()
                                    .filter(t -> SupplyModelEnum.OUTSOURCED.getCode().equals(t.getSupplyModel()))
                                    .map(CapacitySupplyRelationshipVO::getDemandQuantity)
                                    .filter(quantity -> quantity.compareTo(BigDecimal.ZERO) >= 0)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal localQty = versionMap.get(versionCode).stream()
                                    .filter(t -> SupplyModelEnum.LOCAL.getCode().equals(t.getSupplyModel()))
                                    .map(CapacitySupplyRelationshipVO::getDemandQuantity)
                                    .filter(quantity -> quantity.compareTo(BigDecimal.ZERO) >= 0)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            String qty = localQty.intValue() + "/" + outSourcedQty.intValue();
                            vo4.setSupplyQuantity(qty);
                        }
                        versionList.add(vo4);
                    }
                } else {
                    for (String versionCode : versionIds) {
                        CapacitySupplyRelationshipVO4 vo4 = new CapacitySupplyRelationshipVO4();
                        vo4.setVersionCode(versionCode);
                        versionList.add(vo4);
                    }
                }
                vo3.setVersionList(versionList);
                dataList.add(vo3);
            }
            vo2.setDataList(dataList);
            vo2List.add(vo2);
        }
        List<CapacitySupplyRelationshipVO2> noResourceData = vo2List.stream().filter(t -> t.getResourceCode() == null).collect(Collectors.toList());
        noResourceData.forEach(t -> {
            t.setResourceCode(SupplyModelEnum.OUTSOURCED.getDesc());
            t.setResourceName(SupplyModelEnum.OUTSOURCED.getDesc());
        });
        return vo2List;
    }

    /**
     * 预测月份
     *
     * @param planPeriod 2024-08
     * @return
     */
    private String getVersionCode(String planPeriod) {
        String versionCode = planPeriod;
        // 查询最新版本号 格式：2024-08_V1
        CapacityBalanceVersionVO latestVersionCode = capacityBalanceVersionDao.selectLatestVersionCode(planPeriod, CapacityBalanceTypeEnum.MONTH.getCode());
        if (latestVersionCode == null || StringUtils.isBlank(latestVersionCode.getVersionCode())) {
            versionCode = versionCode + "V1";
        } else {
            String[] split = latestVersionCode.getVersionCode().split("V");
            try {
                int version = Integer.parseInt(split[1]);
                // 数字加1
                version++;
                versionCode = versionCode + "V" + version;
            } catch (Exception e) {
                throw new IllegalArgumentException("Invalid format for the last characters of the version code.", e);
            }
        }
        return versionCode;
    }

    private String getWeekVersionCode() {
        String versionCode = DateUtils.dateToString(new Date(), DateUtils.COMMON_DATE_STR3);
        // 查询最新版本号 格式：2024-08_V1
        CapacityBalanceVersionVO latestVersionCode = capacityBalanceVersionDao.selectLatestVersionCode(null,
                CapacityBalanceTypeEnum.WEEK.getCode());
        if (latestVersionCode == null || StringUtils.isBlank(latestVersionCode.getVersionCode())) {
            versionCode = versionCode + "V1";
        } else {
            if (latestVersionCode.getVersionCode().contains(versionCode)) {
                String[] split = latestVersionCode.getVersionCode().split("V");
                try {
                    int version = Integer.parseInt(split[1]);
                    // 数字加1
                    version++;
                    versionCode = versionCode + "V" + version;
                } catch (Exception e) {
                    throw new IllegalArgumentException("Invalid format for the last characters of the version code.", e);
                }
            } else {
                versionCode = versionCode + "V1";
            }

        }
        return versionCode;
    }

    @Override
    public String selectWeekMaxVersionTime() {
        Date date = capacityLoadDao.selectWeekMaxVersionTime();
        if (Objects.isNull(date)) {
            return "暂无";
        }
        return DateUtils.dateToString(date, DateUtils.COMMON_DATE_STR1);
    }

    @Override
    public List<LabelValue<String>> versionDropdown() {
        List<CapacityBalanceVersionPO> capacityBalanceVersionPOS = capacityBalanceVersionDao.selectByParams(new HashMap<>());
        List<String> versionList = capacityBalanceVersionPOS.stream().map(CapacityBalanceVersionPO::getVersionCode).collect(Collectors.toList());
        return versionList.stream()
                .map(item -> new LabelValue<>(item, item))
                .collect(Collectors.toList());
    }

    @Override
    public CapacityBalanceVersionVO selectVersionInfoByVersionCode(String versionCode) {
        if (StringUtils.isEmpty(versionCode)) {
            throw new BusinessException("请选择一个版本号！");
        }
        List<CapacityBalanceVersionPO> capacityBalanceVersionPOS = capacityBalanceVersionDao.selectByParams(ImmutableMap.of("versionCode", versionCode));
        CapacityBalanceVersionVO vo = new CapacityBalanceVersionVO();
        if (CollectionUtils.isNotEmpty(capacityBalanceVersionPOS)) {
            vo = CapacityBalanceVersionConvertor.INSTANCE.po2Vo(capacityBalanceVersionPOS.get(0));
        }
        return vo;
    }

    @Override
    public Map<String, BigDecimal> getFirstVersionResourceQuantityByPlanPeriod(String planPeriod) {
        Date date = DateUtils.stringToDate(planPeriod, DateUtils.YEAR_MONTH);
        String beforeMonth = DateUtils.dateToString(DateUtils.moveMonth(date, -1), DateUtils.YEAR_MONTH);
        Map<String, BigDecimal> result = MapUtil.newHashMap();
        Map<String, Object> versionParam = MapUtil.newHashMap();
        versionParam.put("forecastMonth", beforeMonth);
        versionParam.put("enabled", YesOrNoEnum.YES.getCode());
        List<CapacityBalanceVersionVO> capacityBalanceVersions = this.selectByParams(versionParam);
        if (CollectionUtils.isEmpty(capacityBalanceVersions)) {
            return result;
        }
        CapacityBalanceVersionVO capacityBalanceVersionVO = capacityBalanceVersions.stream().min(Comparator.comparing(CapacityBalanceVersionVO::getVersionCode)).get();
        Map<String, Object> relationParam = MapUtil.newHashMap();
        Date yesterday = DateUtils.moveDay(new Date(), -1);
        String yesterdayOfMonth = DateUtils.dateToString(yesterday, DateUtils.YEAR_MONTH);
        relationParam.put("versionId", capacityBalanceVersionVO.getVersionCode());
        relationParam.put("forecastMonth", yesterdayOfMonth);
        relationParam.put("enabled", YesOrNoEnum.YES.getCode());
        List<CapacitySupplyRelationshipVO> capacitySupplyRelationships = capacitySupplyRelationshipService.selectByParams(relationParam);
        if (CollectionUtils.isEmpty(capacitySupplyRelationships)) {
            return result;
        }
        capacitySupplyRelationships.stream().collect(Collectors.groupingBy(CapacitySupplyRelationshipVO::getResourceCode))
                .forEach((k, v) -> {
                    BigDecimal quantity = v.stream().map(CapacitySupplyRelationshipVO::getDemandQuantity)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    result.put(k, quantity);
                });
        return result;
    }

    @Override
    public String exportExcelAndUpload(String versionId, String scenario) {
        if (StringUtils.isEmpty(versionId)) {
            return null;
        }
        List<CapacityLoadVO> capacityLoadVOS = capacityLoadService.selectByCondition(null, "version_Id = 'NEW'");
        if (CollectionUtils.isEmpty(capacityLoadVOS)) {
            return null;
        }
        List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOS =
                capacitySupplyRelationshipDao.selectCapacitySupplyRelationshipCollect(new HashMap<>());
        if (CollectionUtils.isEmpty(capacitySupplyRelationshipVOS)) {
            return null;
        }
        // 分别存入一个excel文件的两个sheet页
        try (Workbook workbook = new XSSFWorkbook()) {
            capacityLoadExport(workbook, capacityLoadVOS, false);
            capacitySupplyRelationshipExport(workbook, capacitySupplyRelationshipVOS, false);

            // 将工作簿写入内存流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            // 将内存流转为MultipartFile并上传
            byte[] bytes = outputStream.toByteArray();
            ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
            MultipartFile file = new MasterPlanMultipartFile(bytes, "capacityBalance", "产能平衡"+versionId+"数据.xlsx");

            // 上传到MinIO
            String fileName = minioUtils.upload(file, bucketName, scenario,
                    MinIOBusinessEnum.MPS_CAPACITY_BALANCE_VERSION.getCode(), "产能平衡数据", false);

            // 关闭流
            outputStream.close();
            inputStream.close();

            return fileName;
        } catch (Exception e) {
            log.error("产能平衡导出Excel失败", e);
            throw new BusinessException("产能平衡导出Excel失败: " + e.getMessage());
        }
    }

    @Override
    public String weekExportExcelAndUpload(String versionId) {
        if (StringUtils.isEmpty(versionId)) {
            return null;
        }
        // 设备维度
        List<CapacityLoadVO> capacityLoadVOOfResourceList =
                capacityWeekBalanceService.selectResourceByPage(null, null, null);
        // 工序维度
        List<CapacityLoadVO> operationCapacityLoadVOList = new ArrayList<>();
        operationCapacityLoadVOList = CapacityWeekBalanceServiceImpl.getOperationCapacityLoadVOList(capacityLoadVOOfResourceList,
                operationCapacityLoadVOList);
        // 供应关系数据
        List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOS =
                capacityWeekBalanceService.selectSupplyByPage(new Pagination(1, 999999), null, null);
        // 分别存入一个excel文件的两个sheet页
        try (Workbook workbook = new XSSFWorkbook()) {
            capacityLoadExport(workbook, capacityLoadVOOfResourceList, true);
            operationCapacityLoadExport(workbook, operationCapacityLoadVOList);
            capacitySupplyRelationshipExport(workbook, capacitySupplyRelationshipVOS, true);

            // 将工作簿写入内存流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            // 将内存流转为MultipartFile并上传
            byte[] bytes = outputStream.toByteArray();
            ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
            MultipartFile file = new MasterPlanMultipartFile(bytes, "capacityBalance", "周产能平衡"+versionId+"数据.xlsx");

            // 上传到MinIO
            String fileName = minioUtils.upload(file, bucketName, SystemHolder.getScenario(),
                    MinIOBusinessEnum.MPS_CAPACITY_BALANCE_VERSION.getCode(), "周产能平衡数据", false);

            // 关闭流
            outputStream.close();
            inputStream.close();

            return fileName;
        } catch (Exception e) {
            log.error("产能平衡导出Excel失败", e);
            throw new BusinessException("产能平衡导出Excel失败: " + e.getMessage());
        }
    }

    @Override
    public void download(String scenario, String fileName, HttpServletResponse response) {
        if (fileName == null) {
            throw new BusinessException("文件名不能为空！");
        }
        if (scenario == null) {
            throw new BusinessException("场景不能为空！");
        }
        minioUtils.download(bucketName, scenario, MinIOBusinessEnum.MPS_CAPACITY_BALANCE_VERSION.getCode(), fileName, response);
    }

    public static void capacityLoadExport(Workbook workbook, List<CapacityLoadVO> capacityLoadVOS, boolean isWeek) {
        Sheet sheet = workbook.createSheet(isWeek ? "周设备负荷" : "产能负荷");
        List<String> headers;
        if (isWeek) {
            headers = capacityLoadVOS.get(0).getCapacityLoadVO2List().stream().map(CapacityLoadVO2::getForecastWeekTime)
                    .distinct().sorted().collect(Collectors.toList());
        } else {
            headers = capacityLoadVOS.get(0).getCapacityLoadVO2List().stream().map(t -> DateUtils.dateToString(t.getForecastTime(), DateUtils.YEAR_MONTH))
                    .distinct().sorted().collect(Collectors.toList());
        }
        // 固定表头
        List<String> fixedHeaders = Arrays.asList("组织", "工序名称", "产线组", "产线", "负荷情况");

        List<String> allHeaders = new ArrayList<>();
        allHeaders.addAll(fixedHeaders);
        allHeaders.addAll(headers);
        setHeaderStyle(workbook, sheet, allHeaders);

        AtomicInteger rowNum = new AtomicInteger(1);

        Map<String, List<CapacityLoadVO>> plantAndOperationMap = capacityLoadVOS.stream()
                .collect(Collectors.groupingBy(t -> String.join("-", t.getPlantCode(), t.getOperationCode())));
        List<String> plantAndOperationKeyList = plantAndOperationMap.keySet().stream().sorted().collect(Collectors.toList());
        for (String plantAndOperation : plantAndOperationKeyList) {
            List<CapacityLoadVO> plantAndOperationValueList = plantAndOperationMap.get(plantAndOperation);
            Map<String, List<CapacityLoadVO>> resourceGroupMap = plantAndOperationValueList.stream()
                    .collect(Collectors.groupingBy(CapacityLoadVO::getResourceGroupCode));
            List<String> resourceGroupKeyList = resourceGroupMap.keySet().stream().sorted().collect(Collectors.toList());
            // 记录当前plantAndOperation的起始行
            int plantAndOperationStartRow = rowNum.get();
            for (String resourceGroup : resourceGroupKeyList) {
                List<CapacityLoadVO> resourceGroupValueList = resourceGroupMap.get(resourceGroup);
                String plantCode = resourceGroupValueList.get(0).getPlantCode();
                String operationName = resourceGroupValueList.get(0).getOperationName();
                String resourceGroupCode = resourceGroupValueList.get(0).getResourceGroupCode();
                // 记录当前resourceGroup的起始行
                int resourceGroupStartRow = rowNum.get();
                for (CapacityLoadVO capacityLoadVO : resourceGroupValueList) {
                    Row firstRow = sheet.createRow(rowNum.getAndIncrement());
                    firstRow.createCell(0).setCellValue(plantCode);
                    firstRow.createCell(1).setCellValue(operationName);
                    firstRow.createCell(2).setCellValue(resourceGroupCode);
                    firstRow.createCell(3).setCellValue(capacityLoadVO.getResourceCode());
                    firstRow.createCell(4).setCellValue("需求量");

                    List<CapacityLoadVO2> capacityLoadVO2List = capacityLoadVO.getCapacityLoadVO2List();
                    Map<String, CapacityLoadVO2> forecastOfcapacityLoadMap;
                    if (isWeek) {
                        forecastOfcapacityLoadMap = capacityLoadVO2List.stream()
                                .collect(Collectors.toMap(CapacityLoadVO2::getForecastWeekTime, Function.identity()));
                    } else {
                        forecastOfcapacityLoadMap = capacityLoadVO2List.stream().collect(Collectors.toMap(t ->
                                DateUtils.dateToString(t.getForecastTime(), DateUtils.YEAR_MONTH), Function.identity()));
                    }
                    int firstColIndex = 5;
                    for (String dateKey : headers) {
                        CapacityLoadVO2 capacityLoadVO2 = forecastOfcapacityLoadMap.get(dateKey);
                        Cell cell = firstRow.createCell(firstColIndex++);
                        if (capacityLoadVO2 != null) {
                            cell.setCellValue(capacityLoadVO2.getDemandQuantity().doubleValue());
                        }
                    }
                    Row secondRow = sheet.createRow(rowNum.getAndIncrement());
                    secondRow.createCell(0).setCellValue(plantCode);
                    secondRow.createCell(1).setCellValue(operationName);
                    secondRow.createCell(2).setCellValue(resourceGroupCode);
                    secondRow.createCell(3).setCellValue(capacityLoadVO.getResourceCode());
                    secondRow.createCell(4).setCellValue("负荷率");
                    int secondColIndex = 5;
                    for (String dateKey : headers) {
                        CapacityLoadVO2 capacityLoadVO2 = forecastOfcapacityLoadMap.get(dateKey);
                        Cell cell = secondRow.createCell(secondColIndex++);
                        if (capacityLoadVO2 != null) {
                            cell.setCellValue(capacityLoadVO2.getCapacityUtilization().doubleValue());
                            String color = capacityLoadVO2.getColor();
                            CellStyle style = workbook.createCellStyle();
                            style.setAlignment(HorizontalAlignment.CENTER);
                            style.setVerticalAlignment(VerticalAlignment.CENTER);
                            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                            style.setBorderTop(BorderStyle.MEDIUM);
                            style.setBorderBottom(BorderStyle.MEDIUM);
                            style.setBorderLeft(BorderStyle.MEDIUM);
                            style.setBorderRight(BorderStyle.MEDIUM);
                            // 设置百分比格式
                            style.setDataFormat(workbook.createDataFormat().getFormat("0.00%"));
                            if ("1".equals(color)) {
                                style.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
                            } else if ("2".equals(color)) {
                                style.setFillForegroundColor(IndexedColors.RED.getIndex());
                            }
                            cell.setCellStyle(style);
                        }
                    }
                    Row thirdRow = sheet.createRow(rowNum.getAndIncrement());
                    thirdRow.createCell(0).setCellValue(plantCode);
                    thirdRow.createCell(1).setCellValue(operationName);
                    thirdRow.createCell(2).setCellValue(resourceGroupCode);
                    thirdRow.createCell(3).setCellValue(capacityLoadVO.getResourceCode());
                    thirdRow.createCell(4).setCellValue(isWeek ? "周平均节拍" : "月平均节拍");
                    int thirdColIndex = 5;
                    for (String dateKey : headers) {
                        CapacityLoadVO2 capacityLoadVO2 = forecastOfcapacityLoadMap.get(dateKey);
                        Cell cell = thirdRow.createCell(thirdColIndex++);
                        if (capacityLoadVO2 != null) {
                            cell.setCellValue(capacityLoadVO2.getAverageBeat().doubleValue());
                        }
                    }
                    Row fourthRow = sheet.createRow(rowNum.getAndIncrement());
                    fourthRow.createCell(0).setCellValue(plantCode);
                    fourthRow.createCell(1).setCellValue(operationName);
                    fourthRow.createCell(2).setCellValue(resourceGroupCode);
                    fourthRow.createCell(3).setCellValue(capacityLoadVO.getResourceCode());
                    fourthRow.createCell(4).setCellValue(isWeek ? "周总产能" : "月总产能");
                    int fourthColIndex = 5;
                    for (String dateKey : headers) {
                        CapacityLoadVO2 capacityLoadVO2 = forecastOfcapacityLoadMap.get(dateKey);
                        Cell cell = fourthRow.createCell(fourthColIndex++);
                        if (capacityLoadVO2 != null) {
                            cell.setCellValue(capacityLoadVO2.getTotalCapacity().doubleValue());
                        }
                    }
                    sheet.addMergedRegion(new CellRangeAddress(rowNum.get() - 4, rowNum.get() - 1, 3, 3));
                }
                // 合并resourceGroupCode列（第3列）
                if (!resourceGroupValueList.isEmpty()) { // 有resource就合并
                    sheet.addMergedRegion(new CellRangeAddress(resourceGroupStartRow, rowNum.get() - 1, 2, 2));
                }
            }
            // 合并plantCode列（第1列）和operationName列（第2列）
            if (!resourceGroupMap.isEmpty()) { // 只要有resource group就合并
                sheet.addMergedRegion(new CellRangeAddress(plantAndOperationStartRow, rowNum.get() - 1, 0, 0));
                sheet.addMergedRegion(new CellRangeAddress(plantAndOperationStartRow, rowNum.get() - 1, 1, 1));
            }
        }

        setRowStyle(workbook, sheet, allHeaders);
    }

    public static void operationCapacityLoadExport(Workbook workbook, List<CapacityLoadVO> capacityLoadVOS) {
        Sheet sheet = workbook.createSheet("周工序负荷");
        List<String> headers = capacityLoadVOS.get(0).getCapacityLoadVO2List().stream().map(CapacityLoadVO2::getForecastWeekTime)
                .distinct().sorted().collect(Collectors.toList());
        // 固定表头
        List<String> fixedHeaders = Arrays.asList("组织", "工序名称", "负荷情况");

        List<String> allHeaders = new ArrayList<>();
        allHeaders.addAll(fixedHeaders);
        allHeaders.addAll(headers);
        setHeaderStyle(workbook, sheet, allHeaders);

        AtomicInteger rowNum = new AtomicInteger(1);

        Map<String, List<CapacityLoadVO>> plantAndOperationMap = capacityLoadVOS.stream()
                .collect(Collectors.groupingBy(CapacityLoadVO::getPlantCode));
        List<String> plantAndOperationKeyList = plantAndOperationMap.keySet().stream().sorted().collect(Collectors.toList());
        for (String plantAndOperation : plantAndOperationKeyList) {
            List<CapacityLoadVO> plantAndOperationValueList = plantAndOperationMap.get(plantAndOperation);
            Map<String, List<CapacityLoadVO>> resourceGroupMap = plantAndOperationValueList.stream()
                    .collect(Collectors.groupingBy(CapacityLoadVO::getOperationCode));
            List<String> resourceGroupKeyList = resourceGroupMap.keySet().stream().sorted().collect(Collectors.toList());
            // 记录当前plantAndOperation的起始行
            int plantAndOperationStartRow = rowNum.get();
            for (String resourceGroup : resourceGroupKeyList) {
                List<CapacityLoadVO> resourceGroupValueList = resourceGroupMap.get(resourceGroup);
                String plantCode = resourceGroupValueList.get(0).getPlantCode();
                String operationName = resourceGroupValueList.get(0).getOperationName();
                // 记录当前resourceGroup的起始行
                int resourceGroupStartRow = rowNum.get();
                for (CapacityLoadVO capacityLoadVO : resourceGroupValueList) {
                    Row firstRow = sheet.createRow(rowNum.getAndIncrement());
                    firstRow.createCell(0).setCellValue(plantCode);
                    firstRow.createCell(1).setCellValue(operationName);
                    firstRow.createCell(2).setCellValue("需求量");

                    List<CapacityLoadVO2> capacityLoadVO2List = capacityLoadVO.getCapacityLoadVO2List();
                    Map<String, CapacityLoadVO2> forecastOfcapacityLoadMap = capacityLoadVO2List.stream()
                            .collect(Collectors.toMap(CapacityLoadVO2::getForecastWeekTime, Function.identity()));
                    int firstColIndex = 3;
                    for (String dateKey : headers) {
                        CapacityLoadVO2 capacityLoadVO2 = forecastOfcapacityLoadMap.get(dateKey);
                        Cell cell = firstRow.createCell(firstColIndex++);
                        if (capacityLoadVO2 != null) {
                            cell.setCellValue(capacityLoadVO2.getDemandQuantity().doubleValue());
                        }
                    }
                    Row secondRow = sheet.createRow(rowNum.getAndIncrement());
                    secondRow.createCell(0).setCellValue(plantCode);
                    secondRow.createCell(1).setCellValue(operationName);
                    secondRow.createCell(2).setCellValue("负荷率");
                    int secondColIndex = 3;
                    for (String dateKey : headers) {
                        CapacityLoadVO2 capacityLoadVO2 = forecastOfcapacityLoadMap.get(dateKey);
                        Cell cell = secondRow.createCell(secondColIndex++);
                        if (capacityLoadVO2 != null) {
                            cell.setCellValue(capacityLoadVO2.getCapacityUtilization().doubleValue());
                            String color = capacityLoadVO2.getColor();
                            CellStyle style = workbook.createCellStyle();
                            style.setAlignment(HorizontalAlignment.CENTER);
                            style.setVerticalAlignment(VerticalAlignment.CENTER);
                            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                            style.setBorderTop(BorderStyle.MEDIUM);
                            style.setBorderBottom(BorderStyle.MEDIUM);
                            style.setBorderLeft(BorderStyle.MEDIUM);
                            style.setBorderRight(BorderStyle.MEDIUM);
                            // 设置百分比格式
                            style.setDataFormat(workbook.createDataFormat().getFormat("0.00%"));
                            if ("1".equals(color)) {
                                style.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
                            } else if ("2".equals(color)) {
                                style.setFillForegroundColor(IndexedColors.RED.getIndex());
                            }
                            cell.setCellStyle(style);
                        }
                    }
                    Row thirdRow = sheet.createRow(rowNum.getAndIncrement());
                    thirdRow.createCell(0).setCellValue(plantCode);
                    thirdRow.createCell(1).setCellValue(operationName);
                    thirdRow.createCell(2).setCellValue("周总产能");
                    int thirdColIndex = 3;
                    for (String dateKey : headers) {
                        CapacityLoadVO2 capacityLoadVO2 = forecastOfcapacityLoadMap.get(dateKey);
                        Cell cell = thirdRow.createCell(thirdColIndex++);
                        if (capacityLoadVO2 != null) {
                            cell.setCellValue(capacityLoadVO2.getTotalCapacity().doubleValue());
                        }
                    }
                    sheet.addMergedRegion(new CellRangeAddress(rowNum.get() - 3, rowNum.get() - 1, 2, 2));
                }
                // 合并"工序名称"列（第2列）
                if (!resourceGroupValueList.isEmpty()) { // 确保有数据再合并
                    sheet.addMergedRegion(new CellRangeAddress(resourceGroupStartRow, rowNum.get() - 1, 1, 1));
                }
            }
            // 合并"组织"列（第1列）
            if (!resourceGroupMap.isEmpty()) { // 确保有数据再合并
                sheet.addMergedRegion(new CellRangeAddress(plantAndOperationStartRow, rowNum.get() - 1, 0, 0));
            }
        }

        setRowStyle(workbook, sheet, allHeaders);
    }


    public static void capacitySupplyRelationshipExport(Workbook workbook,
                                                        List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOS,
                                                        boolean isWeek) {
        Sheet sheet = workbook.createSheet("产能供应关系");
        List<String> heads = getCapacitySupplyRelationshipHead();

        Map<String, String> lockStatusMap = Arrays.stream(LockStatusEnum.values())
                .collect(Collectors.toMap(LockStatusEnum::getCode, LockStatusEnum::getDesc));
        Map<String, String> supplyModelMap = Arrays.stream(SupplyModelEnum.values())
                .collect(Collectors.toMap(SupplyModelEnum::getCode, SupplyModelEnum::getDesc));
        Map<String, String> ruleMap = Arrays.stream(CapacityBalanceRule.values())
                .collect(Collectors.toMap(CapacityBalanceRule::getCode, CapacityBalanceRule::getDesc));

        setHeaderStyle(workbook, sheet, heads);

        AtomicInteger rowNum = new AtomicInteger(1);
        for (CapacitySupplyRelationshipVO vo : capacitySupplyRelationshipVOS) {
            Row row = sheet.createRow(rowNum.getAndIncrement());
            row.createCell(0).setCellValue(isWeek ? DateUtils.dateToString(vo.getForecastTime(), DateUtils.COMMON_DATE_STR3) :
                    vo.getForecastMonth());
            row.createCell(1).setCellValue(vo.getVehicleModelCode());
            row.createCell(2).setCellValue(vo.getProductCode());
            row.createCell(3).setCellValue(vo.getProductName());
            row.createCell(4).setCellValue(vo.getDemandQuantity().doubleValue());
            row.createCell(5).setCellValue(vo.getSupplyQuantity().doubleValue());
            row.createCell(6).setCellValue(vo.getOperationCode());
            row.createCell(7).setCellValue(vo.getResourceCode());
            row.createCell(8).setCellValue(vo.getResourceName());
            if (vo.getBeat() != null) {
                row.createCell(9).setCellValue(Double.parseDouble(vo.getBeat()));
            }
            row.createCell(10).setCellValue(supplyModelMap.get(vo.getSupplyModel()));
            row.createCell(11).setCellValue(isWeek ? DateUtils.dateToString(vo.getSupplyTime(), DateUtils.COMMON_DATE_STR3) :
                    vo.getSupplyMonth());
            row.createCell(12).setCellValue(lockStatusMap.get(vo.getLockStatus()));
            row.createCell(13).setCellValue(ruleMap.get(vo.getRule()));
        }
        setRowStyle(workbook, sheet, heads);
    }

    private static List<String> getCapacitySupplyRelationshipHead() {
        List<String> headers = new ArrayList<>();
        headers.add("需求时间");
        headers.add("内部车型代码");
        headers.add("产品编码");
        headers.add("产品名称");
        headers.add("需求数量");
        headers.add("供应数量");
        headers.add("工序");
        headers.add("产线");
        headers.add("分配产线组名称");
        headers.add("节拍");
        headers.add("供应方式");
        headers.add("供应时间");
        headers.add("锁定状态");
        headers.add("分配规则");
        return headers;
    }

    private static void setHeaderStyle(Workbook workbook, Sheet sheet, List<String> allHeaders) {
        // 创建表头
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < allHeaders.size(); i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(allHeaders.get(i));
        }
        // 设置标题行样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setColor(IndexedColors.BLACK.getIndex());
        headerStyle.setFont(headerFont);

        for (Cell cell : headerRow) {
            cell.setCellStyle(headerStyle);
        }
    }

    private static void setRowStyle(Workbook workbook, Sheet sheet, List<String> allHeaders) {
        // 创建基础样式，用于没有样式的单元格
        CellStyle baseStyle = workbook.createCellStyle();
        baseStyle.setBorderTop(BorderStyle.MEDIUM);
        baseStyle.setBorderBottom(BorderStyle.MEDIUM);
        baseStyle.setBorderLeft(BorderStyle.MEDIUM);
        baseStyle.setBorderRight(BorderStyle.MEDIUM);
        baseStyle.setAlignment(HorizontalAlignment.CENTER);
        baseStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 创建一个样式缓存，避免重复创建相同属性的样式
        Map<String, CellStyle> styleCache = new HashMap<>();

        // 为所有行设置边框，包括已有样式的单元格
        for (Row row : sheet) {
            for (Cell cell : row) {
                CellStyle cellStyle = cell.getCellStyle();
                if (Objects.nonNull(cellStyle)) {
                    // 生成样式标识符
                    String styleKey = generateStyleKey(cellStyle);

                    // 检查缓存中是否已有该样式
                    CellStyle cachedStyle = styleCache.get(styleKey);
                    if (cachedStyle == null) {
                        // 创建新样式并添加到缓存
                        cachedStyle = workbook.createCellStyle();
                        cachedStyle.cloneStyleFrom(cellStyle);
                        // 添加边框
                        cachedStyle.setBorderTop(BorderStyle.MEDIUM);
                        cachedStyle.setBorderBottom(BorderStyle.MEDIUM);
                        cachedStyle.setBorderLeft(BorderStyle.MEDIUM);
                        cachedStyle.setBorderRight(BorderStyle.MEDIUM);
                        // 保持原有的对齐方式，除非没有设置
                        if (cachedStyle.getAlignment() == HorizontalAlignment.GENERAL) {
                            cachedStyle.setAlignment(HorizontalAlignment.CENTER);
                        }
                        if (cachedStyle.getVerticalAlignment() == VerticalAlignment.BOTTOM) {
                            cachedStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                        }
                        styleCache.put(styleKey, cachedStyle);
                    }
                    cell.setCellStyle(cachedStyle);
                } else {
                    // 如果没有样式，则使用基础样式
                    cell.setCellStyle(baseStyle);
                }
            }
        }
        // 设置列宽
        for (int i = 0; i < allHeaders.size(); i++) {
            if (i == 4) { // 负荷情况列（索引为4）
                // 设置较宽的列宽，约20个字符宽度
                sheet.setColumnWidth(i, 20 * 256);
            } else {
                // 其他列自适应宽度
                sheet.autoSizeColumn(i);
                // 设置最小宽度为12个字符
                int currentWidth = sheet.getColumnWidth(i);
                int minWidth = 12 * 256;
                if (currentWidth < minWidth) {
                    sheet.setColumnWidth(i, minWidth);
                }
                // 设置最大宽度为50个字符，防止某些列过宽
                int maxWidth = 50 * 256;
                if (currentWidth > maxWidth) {
                    sheet.setColumnWidth(i, maxWidth);
                }
            }
        }
    }

    /**
     * 生成样式标识符，用于缓存样式
     */
    private static String generateStyleKey(CellStyle style) {
        StringBuilder key = new StringBuilder();
        key.append(style.getAlignment().ordinal()).append("|");
        key.append(style.getVerticalAlignment().ordinal()).append("|");
        key.append(style.getFillForegroundColor()).append("|");
        key.append(style.getFillPattern().ordinal()).append("|");
        key.append(style.getDataFormat()).append("|");
        // 可以根据需要添加更多样式属性
        return key.toString();
    }
}
