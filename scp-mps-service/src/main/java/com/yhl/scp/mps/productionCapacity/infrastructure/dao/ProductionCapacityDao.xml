<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.productionCapacity.infrastructure.dao.ProductionCapacityDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mps.productionCapacity.infrastructure.po.ProductionCapacityPO">
        <!--@Table mps_production_capacity-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="company" jdbcType="VARCHAR" property="company"/>
        <result column="plant_code" jdbcType="VARCHAR" property="plantCode"/>
        <result column="loading_position" jdbcType="VARCHAR" property="loadingPosition"/>
        <result column="operation_code" jdbcType="VARCHAR" property="operationCode"/>
        <result column="operation_name" jdbcType="VARCHAR" property="operationName"/>
        <result column="procedure_capacity_theory" jdbcType="INTEGER" property="procedureCapacityTheory"/>
        <result column="procedure_capacity_actual" jdbcType="INTEGER" property="procedureCapacityActual"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mps.productionCapacity.vo.ProductionCapacityVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,company,plant_code,loading_position,operation_code,operation_name,procedure_capacity_theory,procedure_capacity_actual
        ,version_value,enabled,creator,create_time,modifier,modify_time,remark
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.company != null and params.company != ''">
                and company = #{params.company,jdbcType=VARCHAR}
            </if>
            <if test="params.plantCode != null and params.plantCode != ''">
                and plant_code = #{params.plantCode,jdbcType=VARCHAR}
            </if>
            <if test="params.loadingPosition != null and params.loadingPosition != ''">
                and loading_position = #{params.loadingPosition,jdbcType=VARCHAR}
            </if>
            <if test="params.loadingPositionList != null and params.loadingPositionList.size() > 0">
                and loading_position in
                <foreach collection="params.loadingPositionList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.operationCode != null and params.operationCode != ''">
                and operation_code = #{params.operationCode,jdbcType=VARCHAR}
            </if>
            <if test="params.operationName != null and params.operationName != ''">
                and operation_name = #{params.operationName,jdbcType=VARCHAR}
            </if>
            <if test="params.procedureCapacityTheory != null">
                and procedure_capacity_theory = #{params.procedureCapacityTheory,jdbcType=INTEGER}
            </if>
            <if test="params.procedureCapacityActual != null">
                and procedure_capacity_actual = #{params.procedureCapacityActual,jdbcType=INTEGER}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_production_capacity
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_production_capacity
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from mps_production_capacity
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_production_capacity
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mps.productionCapacity.infrastructure.po.ProductionCapacityPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mps_production_capacity(
        id,
        company,
        plant_code,
        loading_position,
        operation_code,
        operation_name,
        procedure_capacity_theory,
        procedure_capacity_actual,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        remark)
        values (
        #{id,jdbcType=VARCHAR},
        #{company,jdbcType=VARCHAR},
        #{plantCode,jdbcType=VARCHAR},
        #{loadingPosition,jdbcType=VARCHAR},
        #{operationCode,jdbcType=VARCHAR},
        #{operationName,jdbcType=VARCHAR},
        #{procedureCapacityTheory,jdbcType=INTEGER},
        #{procedureCapacityActual,jdbcType=INTEGER},
        #{versionValue,jdbcType=INTEGER},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mps.productionCapacity.infrastructure.po.ProductionCapacityPO">
        insert into mps_production_capacity(
        id,
        company,
        plant_code,
        loading_position,
        operation_code,
        operation_name,
        procedure_capacity_theory,
        procedure_capacity_actual,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        remark)
        values (
        #{id,jdbcType=VARCHAR},
        #{company,jdbcType=VARCHAR},
        #{plantCode,jdbcType=VARCHAR},
        #{loadingPosition,jdbcType=VARCHAR},
        #{operationCode,jdbcType=VARCHAR},
        #{operationName,jdbcType=VARCHAR},
        #{procedureCapacityTheory,jdbcType=INTEGER},
        #{procedureCapacityActual,jdbcType=INTEGER},
        #{versionValue,jdbcType=INTEGER},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mps_production_capacity(
        id,
        company,
        plant_code,
        loading_position,
        operation_code,
        operation_name,
        procedure_capacity_theory,
        procedure_capacity_actual,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        remark)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.company,jdbcType=VARCHAR},
        #{entity.plantCode,jdbcType=VARCHAR},
        #{entity.loadingPosition,jdbcType=VARCHAR},
        #{entity.operationCode,jdbcType=VARCHAR},
        #{entity.operationName,jdbcType=VARCHAR},
        #{entity.procedureCapacityTheory,jdbcType=INTEGER},
        #{entity.procedureCapacityActual,jdbcType=INTEGER},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mps_production_capacity(
        id,
        company,
        plant_code,
        loading_position,
        operation_code,
        operation_name,
        procedure_capacity_theory,
        procedure_capacity_actual,
        version_value,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        remark)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.company,jdbcType=VARCHAR},
        #{entity.plantCode,jdbcType=VARCHAR},
        #{entity.loadingPosition,jdbcType=VARCHAR},
        #{entity.operationCode,jdbcType=VARCHAR},
        #{entity.operationName,jdbcType=VARCHAR},
        #{entity.procedureCapacityTheory,jdbcType=INTEGER},
        #{entity.procedureCapacityActual,jdbcType=INTEGER},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mps.productionCapacity.infrastructure.po.ProductionCapacityPO">
        update mps_production_capacity set
        company = #{company,jdbcType=VARCHAR},
        plant_code = #{plantCode,jdbcType=VARCHAR},
        loading_position = #{loadingPosition,jdbcType=VARCHAR},
        operation_code = #{operationCode,jdbcType=VARCHAR},
        operation_name = #{operationName,jdbcType=VARCHAR},
        procedure_capacity_theory = #{procedureCapacityTheory,jdbcType=INTEGER},
        procedure_capacity_actual = #{procedureCapacityActual,jdbcType=INTEGER},
        version_value = #{versionValue,jdbcType=INTEGER},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        remark = #{remark,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mps.productionCapacity.infrastructure.po.ProductionCapacityPO">
        update mps_production_capacity
        <set>
            <if test="item.company != null and item.company != ''">
                company = #{item.company,jdbcType=VARCHAR},
            </if>
            <if test="item.plantCode != null and item.plantCode != ''">
                plant_code = #{item.plantCode,jdbcType=VARCHAR},
            </if>
            <if test="item.loadingPosition != null and item.loadingPosition != ''">
                loading_position = #{item.loadingPosition,jdbcType=VARCHAR},
            </if>
            <if test="item.operationCode != null and item.operationCode != ''">
                operation_code = #{item.operationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.operationName != null and item.operationName != ''">
                operation_name = #{item.operationName,jdbcType=VARCHAR},
            </if>
            <if test="item.procedureCapacityTheory != null">
                procedure_capacity_theory = #{item.procedureCapacityTheory,jdbcType=INTEGER},
            </if>
            <if test="item.procedureCapacityActual != null">
                procedure_capacity_actual = #{item.procedureCapacityActual,jdbcType=INTEGER},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mps_production_capacity
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="company = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.company,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.plantCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="loading_position = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.loadingPosition,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operationCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operationName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="procedure_capacity_theory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.procedureCapacityTheory,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="procedure_capacity_actual = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.procedureCapacityActual,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mps_production_capacity 
        <set>
            <if test="item.company != null and item.company != ''">
                company = #{item.company,jdbcType=VARCHAR},
            </if>
            <if test="item.plantCode != null and item.plantCode != ''">
                plant_code = #{item.plantCode,jdbcType=VARCHAR},
            </if>
            <if test="item.loadingPosition != null and item.loadingPosition != ''">
                loading_position = #{item.loadingPosition,jdbcType=VARCHAR},
            </if>
            <if test="item.operationCode != null and item.operationCode != ''">
                operation_code = #{item.operationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.operationName != null and item.operationName != ''">
                operation_name = #{item.operationName,jdbcType=VARCHAR},
            </if>
            <if test="item.procedureCapacityTheory != null">
                procedure_capacity_theory = #{item.procedureCapacityTheory,jdbcType=INTEGER},
            </if>
            <if test="item.procedureCapacityActual != null">
                procedure_capacity_actual = #{item.procedureCapacityActual,jdbcType=INTEGER},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mps_production_capacity where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mps_production_capacity where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
