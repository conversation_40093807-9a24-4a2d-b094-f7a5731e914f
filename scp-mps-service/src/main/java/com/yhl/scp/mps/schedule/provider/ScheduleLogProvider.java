package com.yhl.scp.mps.schedule.provider;

import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.InetUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <code>ScheduleLogProvider</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-27 14:36:17
 */
@Slf4j
@Service
public class ScheduleLogProvider {

    @Resource
    private IpsFeign ipsFeign;

    // @Value("${algorithm.cwd}")
    private String linuxCwd = "";

    private static final String WINDOWS_CWD = "C:\\aps\\workspace\\";

    public AlgorithmLog createAlgorithmLog(String calculateModuleId, String executorId, String parentExecutorId, String calculationType, String status) {
        Date date = new Date();
        AlgorithmLog algorithmLog = new AlgorithmLog();
        algorithmLog.setId(executorId);
        algorithmLog.setModuleCode(SystemModuleEnum.AMS.getCode());
        algorithmLog.setStatus(status);
        algorithmLog.setCalculateModuleId(calculateModuleId);
        algorithmLog.setParentId(parentExecutorId);
        algorithmLog.setStartTime(new Date());
        algorithmLog.setScenario(SystemHolder.getScenario());
        algorithmLog.setCalculationType(calculationType);
        algorithmLog.setIpAddress(InetUtils.getLocalHostIp());
        algorithmLog.setCreator(SystemHolder.getUserId());
        algorithmLog.setCreateTime(date);
        algorithmLog.setModifier(SystemHolder.getUserId());
        algorithmLog.setModifyTime(date);
        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.contains("windows")) {
            algorithmLog.setFilePath(WINDOWS_CWD + executorId);
        } else if (osName.contains("linux")) {
            algorithmLog.setFilePath(linuxCwd + SystemHolder.getScenario() + "/" + executorId);
        }
        ipsFeign.createAlgorithmLog(algorithmLog);
        return algorithmLog;
    }

    public void updateScheduleLog(String executorId, String status, String msg, List<AlgorithmLog> algorithmLogList) {
        BaseResponse<AlgorithmLog> algorithmLogBaseResponse = ipsFeign.selectAlgorithmLogById(executorId);
        AlgorithmLog algorithmLog = algorithmLogBaseResponse.getData();
        algorithmLog.setStatus(status);
        algorithmLog.setEndTime(new Date());
        algorithmLog.setFailMsg(msg);
        algorithmLog.setModifyTime(new Date());
        algorithmLog.setModifier(SystemHolder.getUserId());
        algorithmLogList.add(algorithmLog);
        ipsFeign.updateAlgorithmLog(algorithmLog);
    }

    public void uploadFileZip(List<AlgorithmLog> algorithmLogList) {
/*        MinioClient minioClient = SpringBeanUtils.getBean(MinioClient.class);
        MinioUtils minioUtils = new MinioUtils(minioClient);
        if (CollectionUtils.isEmpty(algorithmLogList)) {
            return;
        }
        for (AlgorithmLog algorithmLog : algorithmLogList) {
            //进行压缩
            try {
                minioUtils.compressFolder(algorithmLog.getFilePath(), algorithmLog.getFilePath() + ".zip");
            } catch (IOException e) {
                log.error("压缩失败：{}", e);
            }
            //上传压缩包
            minioUtils.uploadZip(String.join("-", algorithmLog.getModuleCode(), MinioBucketEnum.BUCKET.getCode()), algorithmLog.getId() + ".zip", algorithmLog.getFilePath() + ".zip");
        }*/
    }

}
